<div class="analytics-dashboard">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>Analytics Dashboard</h1>
      <p>Monitor your business performance and insights</p>
    </div>
    <div class="header-actions">
      <button
        mat-icon-button
        [color]="autoRefresh ? 'primary' : 'basic'"
        (click)="toggleAutoRefresh()"
        [matTooltip]="autoRefresh ? 'Disable auto-refresh' : 'Enable auto-refresh'"
      >
        <mat-icon>{{ autoRefresh ? 'pause' : 'play_arrow' }}</mat-icon>
      </button>
      
      <button
        mat-icon-button
        (click)="refreshData()"
        [disabled]="!canRefresh"
        matTooltip="Refresh data"
      >
        <mat-icon [class.spinning]="refreshing">refresh</mat-icon>
      </button>
      
      <button
        mat-stroked-button
        [matMenuTriggerFor]="exportMenu"
        matTooltip="Export reports"
      >
        <mat-icon>download</mat-icon>
        Export
      </button>
    </div>
  </div>

  <!-- Export Menu -->
  <mat-menu #exportMenu="matMenu">
    <button mat-menu-item (click)="exportReport('sales', 'csv')">
      <mat-icon>table_chart</mat-icon>
      Sales Report (CSV)
    </button>
    <button mat-menu-item (click)="exportReport('customers', 'excel')">
      <mat-icon>people</mat-icon>
      Customer Report (Excel)
    </button>
    <button mat-menu-item (click)="exportReport('products', 'pdf')">
      <mat-icon>inventory</mat-icon>
      Product Report (PDF)
    </button>
    <button mat-menu-item (click)="exportReport('traffic', 'csv')">
      <mat-icon>traffic</mat-icon>
      Traffic Report (CSV)
    </button>
  </mat-menu>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-container">
        <!-- Period Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Time Period</mat-label>
          <mat-select [(value)]="filters.period" (selectionChange)="onPeriodChange()">
            <mat-option *ngFor="let option of periodOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Custom Date Range -->
        <ng-container *ngIf="isCustomPeriod">
          <mat-form-field appearance="outline">
            <mat-label>Start Date</mat-label>
            <input
              matInput
              type="date"
              [(ngModel)]="filters.start_date"
              (change)="onDateRangeChange()"
            >
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>End Date</mat-label>
            <input
              matInput
              type="date"
              [(ngModel)]="filters.end_date"
              (change)="onDateRangeChange()"
            >
          </mat-form-field>
        </ng-container>

        <!-- Compare Period Toggle -->
        <mat-slide-toggle
          [(ngModel)]="filters.compare_period"
          (change)="loadAllAnalytics()"
          color="primary"
        >
          Compare with previous period
        </mat-slide-toggle>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading analytics data...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading && hasData" class="dashboard-content">
    <!-- Tabs -->
    <mat-tab-group [(selectedIndex)]="selectedTab" (selectedTabChange)="onTabChange($event.tab.textLabel)">
      
      <!-- Overview Tab -->
      <mat-tab label="Overview">
        <div class="tab-content">
          <!-- Key Metrics -->
          <div class="metrics-grid">
            <mat-card class="metric-card revenue">
              <mat-card-content>
                <div class="metric-header">
                  <mat-icon>attach_money</mat-icon>
                  <span class="metric-title">Total Revenue</span>
                </div>
                <div class="metric-value">
                  {{ formatCurrency(dashboardStats?.total_revenue || 0) }}
                </div>
                <div class="metric-growth" [ngClass]="getGrowthColor(dashboardStats?.revenue_growth || 0)">
                  <mat-icon>{{ getGrowthIcon(dashboardStats?.revenue_growth || 0) }}</mat-icon>
                  <span>{{ formatPercentage(dashboardStats?.revenue_growth || 0) }}</span>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="metric-card orders">
              <mat-card-content>
                <div class="metric-header">
                  <mat-icon>shopping_cart</mat-icon>
                  <span class="metric-title">Total Orders</span>
                </div>
                <div class="metric-value">
                  {{ formatNumber(dashboardStats?.total_orders || 0) }}
                </div>
                <div class="metric-growth" [ngClass]="getGrowthColor(dashboardStats?.orders_growth || 0)">
                  <mat-icon>{{ getGrowthIcon(dashboardStats?.orders_growth || 0) }}</mat-icon>
                  <span>{{ formatPercentage(dashboardStats?.orders_growth || 0) }}</span>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="metric-card customers">
              <mat-card-content>
                <div class="metric-header">
                  <mat-icon>people</mat-icon>
                  <span class="metric-title">Total Customers</span>
                </div>
                <div class="metric-value">
                  {{ formatNumber(dashboardStats?.total_customers || 0) }}
                </div>
                <div class="metric-growth" [ngClass]="getGrowthColor(dashboardStats?.customers_growth || 0)">
                  <mat-icon>{{ getGrowthIcon(dashboardStats?.customers_growth || 0) }}</mat-icon>
                  <span>{{ formatPercentage(dashboardStats?.customers_growth || 0) }}</span>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="metric-card aov">
              <mat-card-content>
                <div class="metric-header">
                  <mat-icon>trending_up</mat-icon>
                  <span class="metric-title">Avg Order Value</span>
                </div>
                <div class="metric-value">
                  {{ formatCurrency(dashboardStats?.average_order_value || 0) }}
                </div>
                <div class="metric-growth" [ngClass]="getGrowthColor(dashboardStats?.conversion_rate || 0)">
                  <mat-icon>{{ getGrowthIcon(dashboardStats?.conversion_rate || 0) }}</mat-icon>
                  <span>{{ formatPercentage(dashboardStats?.conversion_rate || 0) }}</span>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Charts Row -->
          <div class="charts-row">
            <!-- Revenue Chart -->
            <mat-card class="chart-card">
              <mat-card-header>
                <mat-card-title>Revenue Trend</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="chart-container">
                  <canvas
                    *ngIf="revenueChartData"
                    baseChart
                    [data]="revenueChartData"
                    [options]="chartOptions"
                    type="line"
                  ></canvas>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Orders by Status -->
            <mat-card class="chart-card">
              <mat-card-header>
                <mat-card-title>Orders by Status</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="chart-container">
                  <canvas
                    *ngIf="ordersChartData"
                    baseChart
                    [data]="ordersChartData"
                    [options]="chartOptions"
                    type="doughnut"
                  ></canvas>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Tables Row -->
          <div class="tables-row">
            <!-- Top Selling Products -->
            <mat-card class="table-card">
              <mat-card-header>
                <mat-card-title>Top Selling Products</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="table-container">
                  <table mat-table [dataSource]="dashboardStats?.top_selling_products || []">
                    <ng-container matColumnDef="name">
                      <th mat-header-cell *matHeaderCellDef>Product</th>
                      <td mat-cell *matCellDef="let product">
                        <div class="product-info">
                          <img [src]="product.image_url" [alt]="product.name" class="product-image">
                          <span>{{ product.name }}</span>
                        </div>
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="sales">
                      <th mat-header-cell *matHeaderCellDef>Sales</th>
                      <td mat-cell *matCellDef="let product">{{ formatNumber(product.total_sales) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="revenue">
                      <th mat-header-cell *matHeaderCellDef>Revenue</th>
                      <td mat-cell *matCellDef="let product">{{ formatCurrency(product.revenue) }}</td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="['name', 'sales', 'revenue']"></tr>
                    <tr mat-row *matRowDef="let row; columns: ['name', 'sales', 'revenue'];"></tr>
                  </table>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Recent Orders -->
            <mat-card class="table-card">
              <mat-card-header>
                <mat-card-title>Recent Orders</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="table-container">
                  <table mat-table [dataSource]="dashboardStats?.recent_orders || []">
                    <ng-container matColumnDef="order_number">
                      <th mat-header-cell *matHeaderCellDef>Order #</th>
                      <td mat-cell *matCellDef="let order">{{ order.order_number }}</td>
                    </ng-container>

                    <ng-container matColumnDef="customer">
                      <th mat-header-cell *matHeaderCellDef>Customer</th>
                      <td mat-cell *matCellDef="let order">{{ order.customer_name }}</td>
                    </ng-container>

                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef>Amount</th>
                      <td mat-cell *matCellDef="let order">{{ formatCurrency(order.total_amount) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let order">
                        <mat-chip [color]="order.status === 'completed' ? 'primary' : 'accent'">
                          {{ order.status }}
                        </mat-chip>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="['order_number', 'customer', 'amount', 'status']"></tr>
                    <tr mat-row *matRowDef="let row; columns: ['order_number', 'customer', 'amount', 'status'];"></tr>
                  </table>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </mat-tab>

      <!-- Sales Tab -->
      <mat-tab label="Sales">
        <div class="tab-content">
          <app-sales-analytics
            [data]="salesAnalytics"
            [filters]="filters"
            [loading]="loading"
          ></app-sales-analytics>
        </div>
      </mat-tab>

      <!-- Customers Tab -->
      <mat-tab label="Customers">
        <div class="tab-content">
          <app-customer-analytics
            [data]="customerAnalytics"
            [filters]="filters"
            [loading]="loading"
          ></app-customer-analytics>
        </div>
      </mat-tab>

      <!-- Products Tab -->
      <mat-tab label="Products">
        <div class="tab-content">
          <app-product-analytics
            [data]="productAnalytics"
            [filters]="filters"
            [loading]="loading"
          ></app-product-analytics>
        </div>
      </mat-tab>

      <!-- Traffic Tab -->
      <mat-tab label="Traffic">
        <div class="tab-content">
          <app-traffic-analytics
            [data]="trafficAnalytics"
            [filters]="filters"
            [loading]="loading"
          ></app-traffic-analytics>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <!-- No Data State -->
  <div *ngIf="!loading && !hasData" class="no-data">
    <mat-icon>analytics</mat-icon>
    <h3>No Analytics Data</h3>
    <p>Analytics data will appear here once you have some activity in your store.</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      <mat-icon>refresh</mat-icon>
      Refresh Data
    </button>
  </div>
</div>
