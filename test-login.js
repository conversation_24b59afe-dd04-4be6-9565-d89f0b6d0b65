const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testLogin() {
    try {
        console.log('🔐 Testing admin login...');
        
        const response = await fetch('http://localhost:8001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123'
            })
        });

        const data = await response.json();
        
        if (response.ok && data.success) {
            console.log('✅ Login successful!');
            console.log('👤 User:', data.data.user.name);
            console.log('📧 Email:', data.data.user.email);
            console.log('🔑 Token:', data.data.token.substring(0, 20) + '...');
            
            // Test dashboard access with token
            console.log('\n📊 Testing dashboard access...');
            const dashboardResponse = await fetch('http://localhost:8001/api/dashboard/stats', {
                headers: {
                    'Authorization': `Bearer ${data.data.token}`
                }
            });
            
            const dashboardData = await dashboardResponse.json();
            
            if (dashboardResponse.ok && dashboardData.success) {
                console.log('✅ Dashboard access successful!');
                console.log('📈 Stats:', JSON.stringify(dashboardData.data, null, 2));
            } else {
                console.log('❌ Dashboard access failed:', dashboardData.message);
            }
            
        } else {
            console.log('❌ Login failed:', data.message);
        }
        
    } catch (error) {
        console.log('❌ Error:', error.message);
    }
}

testLogin();
