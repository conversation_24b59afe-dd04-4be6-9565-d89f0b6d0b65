<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_number',
        'user_id',
        'status',
        'payment_status',
        'payment_method',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'notes',
        'shipping_address',
        'billing_address',
        'coupon_code',
        'coupon_discount',
        'processed_at',
        'shipped_at',
        'delivered_at',
        'cancelled_at',
        'refunded_at',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'coupon_discount' => 'decimal:2',
        'shipping_address' => 'array',
        'billing_address' => 'array',
        'processed_at' => 'datetime',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    /**
     * Order status constants.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SHIPPED = 'shipped';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    /**
     * Payment status constants.
     */
    const PAYMENT_PENDING = 'pending';
    const PAYMENT_PAID = 'paid';
    const PAYMENT_FAILED = 'failed';
    const PAYMENT_REFUNDED = 'refunded';
    const PAYMENT_PARTIALLY_REFUNDED = 'partially_refunded';

    /**
     * Payment method constants.
     */
    const PAYMENT_COD = 'cod';
    const PAYMENT_RAZORPAY = 'razorpay';
    const PAYMENT_GPAY = 'gpay';
    const PAYMENT_BANK_TRANSFER = 'bank_transfer';

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['order_number', 'status', 'payment_status', 'total_amount'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = static::generateOrderNumber();
            }
            
            if (empty($order->currency)) {
                $order->currency = config('app.currency', 'USD');
            }
        });
    }

    /**
     * Get the user that owns the order.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created this order.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the order items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the order transactions.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the order invoice.
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }

    /**
     * Get the coupon used in this order.
     */
    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class, 'coupon_code', 'code');
    }

    /**
     * Get order status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_PROCESSING => 'info',
            self::STATUS_SHIPPED => 'primary',
            self::STATUS_DELIVERED => 'success',
            self::STATUS_CANCELLED => 'danger',
            self::STATUS_REFUNDED => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * Get payment status badge color.
     */
    public function getPaymentStatusColorAttribute(): string
    {
        return match($this->payment_status) {
            self::PAYMENT_PENDING => 'warning',
            self::PAYMENT_PAID => 'success',
            self::PAYMENT_FAILED => 'danger',
            self::PAYMENT_REFUNDED => 'secondary',
            self::PAYMENT_PARTIALLY_REFUNDED => 'info',
            default => 'secondary',
        };
    }

    /**
     * Check if order can be cancelled.
     */
    public function getCanBeCancelledAttribute(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PROCESSING]);
    }

    /**
     * Check if order can be refunded.
     */
    public function getCanBeRefundedAttribute(): bool
    {
        return $this->payment_status === self::PAYMENT_PAID && 
               in_array($this->status, [self::STATUS_DELIVERED, self::STATUS_SHIPPED]);
    }

    /**
     * Get formatted shipping address.
     */
    public function getFormattedShippingAddressAttribute(): string
    {
        if (!$this->shipping_address) {
            return '';
        }

        $address = $this->shipping_address;
        return implode(', ', array_filter([
            $address['name'] ?? '',
            $address['address'] ?? '',
            $address['city'] ?? '',
            $address['state'] ?? '',
            $address['postal_code'] ?? '',
            $address['country'] ?? '',
        ]));
    }

    /**
     * Get formatted billing address.
     */
    public function getFormattedBillingAddressAttribute(): string
    {
        if (!$this->billing_address) {
            return $this->formatted_shipping_address;
        }

        $address = $this->billing_address;
        return implode(', ', array_filter([
            $address['name'] ?? '',
            $address['address'] ?? '',
            $address['city'] ?? '',
            $address['state'] ?? '',
            $address['postal_code'] ?? '',
            $address['country'] ?? '',
        ]));
    }

    /**
     * Get total items count.
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Scope for orders with specific status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for orders with specific payment status.
     */
    public function scopePaymentStatus($query, string $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }

    /**
     * Scope for orders by user.
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for orders within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for orders with total amount range.
     */
    public function scopeTotalRange($query, $minAmount, $maxAmount)
    {
        return $query->whereBetween('total_amount', [$minAmount, $maxAmount]);
    }

    /**
     * Generate unique order number.
     */
    public static function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Y') . '-' . strtoupper(Str::random(8));
        } while (static::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Update order status.
     */
    public function updateStatus(string $status, ?string $note = null): void
    {
        $oldStatus = $this->status;
        $this->status = $status;

        // Set timestamps based on status
        switch ($status) {
            case self::STATUS_PROCESSING:
                $this->processed_at = now();
                break;
            case self::STATUS_SHIPPED:
                $this->shipped_at = now();
                break;
            case self::STATUS_DELIVERED:
                $this->delivered_at = now();
                break;
            case self::STATUS_CANCELLED:
                $this->cancelled_at = now();
                break;
            case self::STATUS_REFUNDED:
                $this->refunded_at = now();
                break;
        }

        $this->save();

        // Log status change
        activity()
            ->causedBy(auth()->user())
            ->performedOn($this)
            ->withProperties([
                'old_status' => $oldStatus,
                'new_status' => $status,
                'note' => $note,
            ])
            ->log('Order status updated');
    }

    /**
     * Calculate order totals.
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum(function ($item) {
            return $item->price * $item->quantity;
        });

        // Apply coupon discount
        if ($this->coupon_code && $this->coupon) {
            $this->coupon_discount = $this->coupon->calculateDiscount($this->subtotal);
            $this->discount_amount = $this->coupon_discount;
        }

        // Calculate tax (if applicable)
        $taxRate = config('app.tax_rate', 0);
        $this->tax_amount = ($this->subtotal - $this->discount_amount) * $taxRate;

        // Calculate total
        $this->total_amount = $this->subtotal + $this->tax_amount + $this->shipping_amount - $this->discount_amount;

        $this->save();
    }

    /**
     * Get order statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_orders' => static::count(),
            'pending_orders' => static::status(self::STATUS_PENDING)->count(),
            'processing_orders' => static::status(self::STATUS_PROCESSING)->count(),
            'shipped_orders' => static::status(self::STATUS_SHIPPED)->count(),
            'delivered_orders' => static::status(self::STATUS_DELIVERED)->count(),
            'cancelled_orders' => static::status(self::STATUS_CANCELLED)->count(),
            'total_revenue' => static::where('payment_status', self::PAYMENT_PAID)->sum('total_amount'),
            'average_order_value' => static::avg('total_amount'),
            'orders_today' => static::whereDate('created_at', today())->count(),
            'revenue_today' => static::whereDate('created_at', today())
                ->where('payment_status', self::PAYMENT_PAID)
                ->sum('total_amount'),
        ];
    }
}
