<?php

namespace App\Http\Controllers\Api;

use App\Models\Coupon;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class CouponController extends BaseController
{
    /**
     * Display a listing of coupons.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $status = $request->get('status');
        $type = $request->get('type');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Coupon::with(['creator:id,name'])
            ->withCount(['usages', 'orders']);

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($status) {
            switch ($status) {
                case 'active':
                    $query->valid();
                    break;
                case 'expired':
                    $query->expired();
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'used_up':
                    $query->whereColumn('used_count', '>=', 'usage_limit')
                          ->whereNotNull('usage_limit');
                    break;
            }
        }

        // Filter by type
        if ($type) {
            $query->where('type', $type);
        }

        // Sorting
        $allowedSortFields = ['name', 'code', 'type', 'value', 'used_count', 'created_at', 'expires_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $coupons = $query->paginate($perPage);

        // Transform coupon data
        $coupons->getCollection()->transform(function ($coupon) {
            return [
                'id' => $coupon->id,
                'name' => $coupon->name,
                'code' => $coupon->code,
                'description' => $coupon->description,
                'type' => $coupon->type,
                'value' => $coupon->value,
                'minimum_amount' => $coupon->minimum_amount,
                'maximum_discount' => $coupon->maximum_discount,
                'usage_limit' => $coupon->usage_limit,
                'usage_limit_per_user' => $coupon->usage_limit_per_user,
                'used_count' => $coupon->used_count,
                'remaining_usage' => $coupon->remaining_usage,
                'is_active' => $coupon->is_active,
                'status' => $coupon->status,
                'status_color' => $coupon->status_color,
                'is_valid' => $coupon->is_valid,
                'is_expired' => $coupon->is_expired,
                'starts_at' => $coupon->starts_at,
                'expires_at' => $coupon->expires_at,
                'applies_to' => $coupon->applies_to,
                'exclude_sale_items' => $coupon->exclude_sale_items,
                'individual_use' => $coupon->individual_use,
                'free_shipping' => $coupon->free_shipping,
                'usages_count' => $coupon->usages_count,
                'orders_count' => $coupon->orders_count,
                'creator' => $coupon->creator ? $coupon->creator->name : null,
                'created_at' => $coupon->created_at,
                'updated_at' => $coupon->updated_at,
            ];
        });

        return $this->sendPaginatedResponse($coupons);
    }

    /**
     * Store a newly created coupon.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:coupons,code',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:percentage,fixed_cart,fixed_product',
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'starts_at' => 'nullable|date|after_or_equal:today',
            'expires_at' => 'nullable|date|after:starts_at',
            'applies_to' => 'required|in:all,specific_products,specific_categories',
            'exclude_sale_items' => 'boolean',
            'individual_use' => 'boolean',
            'free_shipping' => 'boolean',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'integer|exists:products,id',
            'category_ids' => 'nullable|array',
            'category_ids.*' => 'integer|exists:categories,id',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        // Additional validation
        if ($request->type === 'percentage' && $request->value > 100) {
            return $this->sendValidationError(['value' => ['Percentage value cannot exceed 100']]);
        }

        if ($request->applies_to === 'specific_products' && empty($request->product_ids)) {
            return $this->sendValidationError(['product_ids' => ['Product IDs are required when applying to specific products']]);
        }

        if ($request->applies_to === 'specific_categories' && empty($request->category_ids)) {
            return $this->sendValidationError(['category_ids' => ['Category IDs are required when applying to specific categories']]);
        }

        DB::beginTransaction();
        try {
            $coupon = Coupon::create([
                'name' => $request->name,
                'code' => $request->code,
                'description' => $request->description,
                'type' => $request->type,
                'value' => $request->value,
                'minimum_amount' => $request->minimum_amount,
                'maximum_discount' => $request->maximum_discount,
                'usage_limit' => $request->usage_limit,
                'usage_limit_per_user' => $request->usage_limit_per_user,
                'is_active' => $request->get('is_active', true),
                'starts_at' => $request->starts_at,
                'expires_at' => $request->expires_at,
                'applies_to' => $request->applies_to,
                'exclude_sale_items' => $request->get('exclude_sale_items', false),
                'individual_use' => $request->get('individual_use', false),
                'free_shipping' => $request->get('free_shipping', false),
                'created_by' => auth()->id(),
            ]);

            // Attach products if specified
            if ($request->applies_to === 'specific_products' && $request->product_ids) {
                $coupon->products()->attach($request->product_ids);
            }

            // Attach categories if specified
            if ($request->applies_to === 'specific_categories' && $request->category_ids) {
                $coupon->categories()->attach($request->category_ids);
            }

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($coupon)
                ->log('Coupon created');

            return $this->sendCreated([
                'id' => $coupon->id,
                'name' => $coupon->name,
                'code' => $coupon->code,
                'type' => $coupon->type,
                'value' => $coupon->value,
                'status' => $coupon->status,
            ], 'Coupon created successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to create coupon: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified coupon.
     */
    public function show(string $id): JsonResponse
    {
        $coupon = Coupon::with([
            'creator:id,name',
            'products:id,name,slug,price',
            'categories:id,name,slug',
            'usages.user:id,name,email',
            'usages.order:id,order_number,total_amount'
        ])->find($id);

        if (!$coupon) {
            return $this->sendNotFound('Coupon not found');
        }

        return $this->sendResponse([
            'id' => $coupon->id,
            'name' => $coupon->name,
            'code' => $coupon->code,
            'description' => $coupon->description,
            'type' => $coupon->type,
            'value' => $coupon->value,
            'minimum_amount' => $coupon->minimum_amount,
            'maximum_discount' => $coupon->maximum_discount,
            'usage_limit' => $coupon->usage_limit,
            'usage_limit_per_user' => $coupon->usage_limit_per_user,
            'used_count' => $coupon->used_count,
            'remaining_usage' => $coupon->remaining_usage,
            'is_active' => $coupon->is_active,
            'status' => $coupon->status,
            'status_color' => $coupon->status_color,
            'is_valid' => $coupon->is_valid,
            'is_expired' => $coupon->is_expired,
            'starts_at' => $coupon->starts_at,
            'expires_at' => $coupon->expires_at,
            'applies_to' => $coupon->applies_to,
            'exclude_sale_items' => $coupon->exclude_sale_items,
            'individual_use' => $coupon->individual_use,
            'free_shipping' => $coupon->free_shipping,
            'products' => $coupon->products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'price' => $product->price,
                ];
            }),
            'categories' => $coupon->categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                ];
            }),
            'recent_usages' => $coupon->usages->take(10)->map(function ($usage) {
                return [
                    'id' => $usage->id,
                    'user' => $usage->user ? [
                        'id' => $usage->user->id,
                        'name' => $usage->user->name,
                        'email' => $usage->user->email,
                    ] : null,
                    'order' => $usage->order ? [
                        'id' => $usage->order->id,
                        'order_number' => $usage->order->order_number,
                        'total_amount' => $usage->order->total_amount,
                    ] : null,
                    'used_at' => $usage->used_at,
                ];
            }),
            'creator' => $coupon->creator ? [
                'id' => $coupon->creator->id,
                'name' => $coupon->creator->name,
            ] : null,
            'created_at' => $coupon->created_at,
            'updated_at' => $coupon->updated_at,
        ]);
    }

    /**
     * Update the specified coupon.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $coupon = Coupon::find($id);

        if (!$coupon) {
            return $this->sendNotFound('Coupon not found');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:coupons,code,' . $coupon->id,
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:percentage,fixed_cart,fixed_product',
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
            'applies_to' => 'required|in:all,specific_products,specific_categories',
            'exclude_sale_items' => 'boolean',
            'individual_use' => 'boolean',
            'free_shipping' => 'boolean',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'integer|exists:products,id',
            'category_ids' => 'nullable|array',
            'category_ids.*' => 'integer|exists:categories,id',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        // Additional validation
        if ($request->type === 'percentage' && $request->value > 100) {
            return $this->sendValidationError(['value' => ['Percentage value cannot exceed 100']]);
        }

        if ($request->applies_to === 'specific_products' && empty($request->product_ids)) {
            return $this->sendValidationError(['product_ids' => ['Product IDs are required when applying to specific products']]);
        }

        if ($request->applies_to === 'specific_categories' && empty($request->category_ids)) {
            return $this->sendValidationError(['category_ids' => ['Category IDs are required when applying to specific categories']]);
        }

        DB::beginTransaction();
        try {
            $coupon->update([
                'name' => $request->name,
                'code' => $request->code,
                'description' => $request->description,
                'type' => $request->type,
                'value' => $request->value,
                'minimum_amount' => $request->minimum_amount,
                'maximum_discount' => $request->maximum_discount,
                'usage_limit' => $request->usage_limit,
                'usage_limit_per_user' => $request->usage_limit_per_user,
                'is_active' => $request->get('is_active', true),
                'starts_at' => $request->starts_at,
                'expires_at' => $request->expires_at,
                'applies_to' => $request->applies_to,
                'exclude_sale_items' => $request->get('exclude_sale_items', false),
                'individual_use' => $request->get('individual_use', false),
                'free_shipping' => $request->get('free_shipping', false),
            ]);

            // Update product associations
            if ($request->applies_to === 'specific_products') {
                $coupon->products()->sync($request->product_ids ?? []);
            } else {
                $coupon->products()->detach();
            }

            // Update category associations
            if ($request->applies_to === 'specific_categories') {
                $coupon->categories()->sync($request->category_ids ?? []);
            } else {
                $coupon->categories()->detach();
            }

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($coupon)
                ->log('Coupon updated');

            return $this->sendUpdated([
                'id' => $coupon->id,
                'name' => $coupon->name,
                'code' => $coupon->code,
                'type' => $coupon->type,
                'value' => $coupon->value,
                'status' => $coupon->status,
            ], 'Coupon updated successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to update coupon: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Remove the specified coupon.
     */
    public function destroy(string $id): JsonResponse
    {
        $coupon = Coupon::find($id);

        if (!$coupon) {
            return $this->sendNotFound('Coupon not found');
        }

        // Check if coupon has been used
        if ($coupon->used_count > 0) {
            return $this->sendError('Cannot delete coupon that has been used. You can deactivate it instead.');
        }

        try {
            $coupon->delete();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($coupon)
                ->log('Coupon deleted');

            return $this->sendDeleted('Coupon deleted successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to delete coupon: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Validate a coupon code.
     */
    public function validate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string',
            'user_id' => 'nullable|integer|exists:users,id',
            'cart_items' => 'nullable|array',
            'cart_items.*.product_id' => 'required|integer|exists:products,id',
            'cart_items.*.price' => 'required|numeric|min:0',
            'cart_items.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        $coupon = Coupon::where('code', $request->code)->first();

        if (!$coupon) {
            return $this->sendError('Invalid coupon code');
        }

        $errors = $coupon->validateForUser($request->user_id, $request->cart_items ?? []);

        if (!empty($errors)) {
            return $this->sendError(implode(', ', $errors));
        }

        // Calculate discount
        $cartTotal = collect($request->cart_items ?? [])->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });

        $discount = $coupon->calculateDiscount($cartTotal, $request->cart_items ?? []);

        return $this->sendResponse([
            'valid' => true,
            'coupon' => [
                'id' => $coupon->id,
                'name' => $coupon->name,
                'code' => $coupon->code,
                'type' => $coupon->type,
                'value' => $coupon->value,
                'free_shipping' => $coupon->free_shipping,
            ],
            'discount_amount' => $discount,
            'cart_total' => $cartTotal,
            'final_total' => max(0, $cartTotal - $discount),
        ], 'Coupon is valid');
    }

    /**
     * Get coupon statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = Coupon::getStatistics();

            return $this->sendResponse($stats);

        } catch (\Exception $e) {
            return $this->sendError('Failed to get coupon statistics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Generate a unique coupon code.
     */
    public function generateCode(Request $request): JsonResponse
    {
        $length = $request->get('length', 8);

        if ($length < 4 || $length > 20) {
            return $this->sendValidationError(['length' => ['Length must be between 4 and 20 characters']]);
        }

        $code = Coupon::generateUniqueCode($length);

        return $this->sendResponse(['code' => $code]);
    }

    /**
     * Bulk update coupon status.
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'coupon_ids' => 'required|array|min:1',
            'coupon_ids.*' => 'integer|exists:coupons,id',
            'is_active' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $updated = Coupon::whereIn('id', $request->coupon_ids)
                ->update(['is_active' => $request->is_active]);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->withProperties([
                    'coupon_ids' => $request->coupon_ids,
                    'is_active' => $request->is_active,
                ])
                ->log('Bulk coupon status update');

            return $this->sendResponse([
                'updated_count' => $updated,
                'status' => $request->is_active ? 'activated' : 'deactivated',
            ], "Successfully {$request->is_active ? 'activated' : 'deactivated'} {$updated} coupons");

        } catch (\Exception $e) {
            return $this->sendError('Failed to update coupon status: ' . $e->getMessage(), [], 500);
        }
    }
}
