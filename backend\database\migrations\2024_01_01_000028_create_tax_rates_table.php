<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tax_rates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->decimal('rate', 5, 4); // e.g., 0.0825 for 8.25%
            $table->enum('type', ['percentage', 'fixed']);
            $table->string('country', 2);
            $table->string('state')->nullable();
            $table->string('city')->nullable();
            $table->string('postal_code')->nullable();
            $table->boolean('is_compound')->default(false); // Tax on tax
            $table->integer('priority')->default(0); // Order of application
            $table->boolean('is_active')->default(true);
            $table->date('effective_from')->nullable();
            $table->date('effective_to')->nullable();
            $table->text('description')->nullable();
            $table->json('applicable_categories')->nullable(); // Product categories this tax applies to
            $table->json('exempt_categories')->nullable(); // Product categories exempt from this tax
            $table->timestamps();
            
            // Indexes
            $table->index(['country', 'state', 'is_active']);
            $table->index(['is_active', 'priority']);
            $table->index(['effective_from', 'effective_to']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tax_rates');
    }
};
