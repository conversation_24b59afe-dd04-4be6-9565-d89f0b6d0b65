import ApiService from './ApiService';
import AsyncStorage from '@react-native-async-storage/async-storage';

class UserService {
  /**
   * Get current user profile
   */
  async getProfile() {
    try {
      const response = await ApiService.get('/user/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData) {
    try {
      const response = await ApiService.put('/user/profile', profileData);
      return response.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword, newPassword, confirmPassword) {
    try {
      const data = {
        current_password: currentPassword,
        password: newPassword,
        password_confirmation: confirmPassword
      };

      const response = await ApiService.put('/user/change-password', data);
      return response.data;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }

  /**
   * Upload profile image
   */
  async uploadProfileImage(imageUri) {
    try {
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'profile.jpg',
      });

      const response = await ApiService.post('/user/profile-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading profile image:', error);
      throw error;
    }
  }

  /**
   * Delete profile image
   */
  async deleteProfileImage() {
    try {
      const response = await ApiService.delete('/user/profile-image');
      return response.data;
    } catch (error) {
      console.error('Error deleting profile image:', error);
      throw error;
    }
  }

  /**
   * Get user addresses
   */
  async getAddresses() {
    try {
      const response = await ApiService.get('/user/addresses');
      return response.data;
    } catch (error) {
      console.error('Error fetching user addresses:', error);
      throw error;
    }
  }

  /**
   * Add new address
   */
  async addAddress(addressData) {
    try {
      const response = await ApiService.post('/user/addresses', addressData);
      return response.data;
    } catch (error) {
      console.error('Error adding address:', error);
      throw error;
    }
  }

  /**
   * Update address
   */
  async updateAddress(addressId, addressData) {
    try {
      const response = await ApiService.put(`/user/addresses/${addressId}`, addressData);
      return response.data;
    } catch (error) {
      console.error('Error updating address:', error);
      throw error;
    }
  }

  /**
   * Delete address
   */
  async deleteAddress(addressId) {
    try {
      const response = await ApiService.delete(`/user/addresses/${addressId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting address:', error);
      throw error;
    }
  }

  /**
   * Set default address
   */
  async setDefaultAddress(addressId, type = 'both') {
    try {
      const response = await ApiService.post(`/user/addresses/${addressId}/set-default`, {
        type: type // 'billing', 'shipping', or 'both'
      });
      return response.data;
    } catch (error) {
      console.error('Error setting default address:', error);
      throw error;
    }
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences() {
    try {
      const response = await ApiService.get('/user/notification-preferences');
      return response.data;
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      throw error;
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(preferences) {
    try {
      const response = await ApiService.put('/user/notification-preferences', preferences);
      return response.data;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }

  /**
   * Get privacy settings
   */
  async getPrivacySettings() {
    try {
      const response = await ApiService.get('/user/privacy-settings');
      return response.data;
    } catch (error) {
      console.error('Error fetching privacy settings:', error);
      throw error;
    }
  }

  /**
   * Update privacy settings
   */
  async updatePrivacySettings(settings) {
    try {
      const response = await ApiService.put('/user/privacy-settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      throw error;
    }
  }

  /**
   * Get user activity log
   */
  async getActivityLog(page = 1, limit = 20) {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: limit.toString()
      });

      const response = await ApiService.get(`/user/activity?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching activity log:', error);
      throw error;
    }
  }

  /**
   * Delete account
   */
  async deleteAccount(password, reason = null) {
    try {
      const data = {
        password: password
      };

      if (reason) {
        data.reason = reason;
      }

      const response = await ApiService.post('/user/delete-account', data);
      return response.data;
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  }

  /**
   * Export user data
   */
  async exportUserData() {
    try {
      const response = await ApiService.post('/user/export-data');
      return response.data;
    } catch (error) {
      console.error('Error exporting user data:', error);
      throw error;
    }
  }

  /**
   * Get app settings from local storage
   */
  async getAppSettings() {
    try {
      const settings = await AsyncStorage.getItem('app_settings');
      return settings ? JSON.parse(settings) : this.getDefaultAppSettings();
    } catch (error) {
      console.error('Error getting app settings:', error);
      return this.getDefaultAppSettings();
    }
  }

  /**
   * Save app settings to local storage
   */
  async saveAppSettings(settings) {
    try {
      await AsyncStorage.setItem('app_settings', JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Error saving app settings:', error);
      throw error;
    }
  }

  /**
   * Get default app settings
   */
  getDefaultAppSettings() {
    return {
      theme: 'light', // 'light', 'dark', 'auto'
      language: 'en',
      currency: 'USD',
      notifications: {
        push_enabled: true,
        email_enabled: true,
        order_updates: true,
        promotions: true,
        new_products: false,
        price_alerts: true
      },
      privacy: {
        analytics: true,
        personalization: true,
        location_tracking: false,
        crash_reporting: true
      },
      display: {
        show_prices: true,
        show_stock: true,
        grid_view: true,
        auto_play_videos: false
      },
      security: {
        biometric_login: false,
        auto_logout: 30, // minutes
        require_password_for_purchases: false
      }
    };
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages() {
    return [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'es', name: 'Spanish', nativeName: 'Español' },
      { code: 'fr', name: 'French', nativeName: 'Français' },
      { code: 'de', name: 'German', nativeName: 'Deutsch' },
      { code: 'it', name: 'Italian', nativeName: 'Italiano' },
      { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
      { code: 'ru', name: 'Russian', nativeName: 'Русский' },
      { code: 'zh', name: 'Chinese', nativeName: '中文' },
      { code: 'ja', name: 'Japanese', nativeName: '日本語' },
      { code: 'ko', name: 'Korean', nativeName: '한국어' }
    ];
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies() {
    return [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
      { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
      { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
      { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
      { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
      { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
      { code: 'BRL', name: 'Brazilian Real', symbol: 'R$' }
    ];
  }

  /**
   * Get theme options
   */
  getThemeOptions() {
    return [
      { value: 'light', label: 'Light', description: 'Light theme for better visibility' },
      { value: 'dark', label: 'Dark', description: 'Dark theme for reduced eye strain' },
      { value: 'auto', label: 'Auto', description: 'Follow system theme setting' }
    ];
  }

  /**
   * Validate email format
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Validate password strength
   */
  validatePassword(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar,
      errors: {
        minLength: password.length < minLength,
        hasUpperCase: !hasUpperCase,
        hasLowerCase: !hasLowerCase,
        hasNumbers: !hasNumbers,
        hasSpecialChar: !hasSpecialChar
      }
    };
  }

  /**
   * Format user display name
   */
  formatDisplayName(user) {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    } else if (user.name) {
      return user.name;
    } else {
      return user.email.split('@')[0];
    }
  }

  /**
   * Get user initials for avatar
   */
  getUserInitials(user) {
    const displayName = this.formatDisplayName(user);
    const names = displayName.split(' ');
    
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    } else {
      return displayName.substring(0, 2).toUpperCase();
    }
  }
}

export default new UserService();
