import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  TextInput,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

import OrderService from '../services/OrderService';
import OrderCard from '../components/OrderCard';
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';
import FilterModal from '../components/FilterModal';
import { colors, spacing, typography } from '../theme';
import { formatCurrency, formatDate } from '../utils/helpers';

const OrderListScreen = () => {
  const navigation = useNavigation();

  // State
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [totalItems, setTotalItems] = useState(0);

  // Filters
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    payment_status: '',
    date_from: '',
    date_to: '',
    sort_by: 'created_at',
    sort_order: 'desc',
    per_page: 20
  });

  // Filter options
  const statusOptions = OrderService.getOrderStatusOptions();
  const paymentStatusOptions = OrderService.getPaymentStatusOptions();

  useEffect(() => {
    loadOrders(true);
  }, [filters.status, filters.payment_status, filters.sort_by, filters.sort_order]);

  useEffect(() => {
    // Set navigation header
    navigation.setOptions({
      title: 'My Orders',
      headerRight: () => (
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowSearch(!showSearch)}
          >
            <Icon name="search" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilters(true)}
          >
            <Icon name="filter-list" size={24} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      )
    });
  }, [navigation, showSearch]);

  const loadOrders = async (reset = false) => {
    try {
      if (reset) {
        setLoading(true);
        setCurrentPage(1);
      } else {
        setLoadingMore(true);
      }

      const page = reset ? 1 : currentPage + 1;
      const searchFilters = {
        ...filters,
        page,
        search: searchQuery
      };

      const response = await OrderService.getOrders(searchFilters);

      if (response.success) {
        const newOrders = response.data;
        
        if (reset) {
          setOrders(newOrders);
        } else {
          setOrders(prev => [...prev, ...newOrders]);
        }

        setCurrentPage(page);
        setHasMorePages(response.meta.current_page < response.meta.last_page);
        setTotalItems(response.meta.total);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
      Alert.alert('Error', 'Failed to load orders. Please try again.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadOrders(true);
  }, [filters, searchQuery]);

  const onLoadMore = () => {
    if (!loadingMore && hasMorePages) {
      loadOrders(false);
    }
  };

  const onSearch = (query) => {
    setSearchQuery(query);
    if (query.length >= 2 || query.length === 0) {
      loadOrders(true);
    }
  };

  const onApplyFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setShowFilters(false);
    loadOrders(true);
  };

  const onOrderPress = (order) => {
    navigation.navigate('OrderDetail', { orderId: order.id, order });
  };

  const onCancelOrder = async (order) => {
    Alert.alert(
      'Cancel Order',
      `Are you sure you want to cancel order ${OrderService.formatOrderNumber(order.order_number)}?`,
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              await OrderService.cancelOrder(order.id, 'Cancelled by customer');
              Alert.alert('Success', 'Order cancelled successfully');
              loadOrders(true);
            } catch (error) {
              console.error('Error cancelling order:', error);
              Alert.alert('Error', 'Failed to cancel order. Please try again.');
            }
          }
        }
      ]
    );
  };

  const onTrackOrder = (order) => {
    navigation.navigate('OrderTracking', { orderId: order.id, order });
  };

  const onReorderItems = async (order) => {
    try {
      // Add all order items to cart
      for (const item of order.items) {
        await ProductService.addToCart(item.product_id, item.quantity);
      }
      
      Alert.alert(
        'Items Added',
        'All items from this order have been added to your cart.',
        [
          { text: 'Continue Shopping', style: 'cancel' },
          { text: 'View Cart', onPress: () => navigation.navigate('Cart') }
        ]
      );
    } catch (error) {
      console.error('Error reordering items:', error);
      Alert.alert('Error', 'Failed to add items to cart. Please try again.');
    }
  };

  const renderOrder = ({ item }) => (
    <OrderCard
      order={item}
      onPress={() => onOrderPress(item)}
      onCancel={() => onCancelOrder(item)}
      onTrack={() => onTrackOrder(item)}
      onReorder={() => onReorderItems(item)}
      style={styles.orderCard}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {showSearch && (
        <View style={styles.searchContainer}>
          <Icon name="search" size={20} color={colors.text.secondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search orders..."
            value={searchQuery}
            onChangeText={onSearch}
            autoFocus
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => onSearch('')}
            >
              <Icon name="clear" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          )}
        </View>
      )}
      
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsText}>
          {totalItems} order{totalItems !== 1 ? 's' : ''} found
        </Text>
        
        {/* Quick Status Filters */}
        <View style={styles.quickFilters}>
          <TouchableOpacity
            style={[
              styles.quickFilterButton,
              filters.status === '' && styles.activeQuickFilter
            ]}
            onPress={() => onApplyFilters({ status: '' })}
          >
            <Text style={[
              styles.quickFilterText,
              filters.status === '' && styles.activeQuickFilterText
            ]}>
              All
            </Text>
          </TouchableOpacity>
          
          {statusOptions.slice(0, 3).map((status) => (
            <TouchableOpacity
              key={status.value}
              style={[
                styles.quickFilterButton,
                filters.status === status.value && styles.activeQuickFilter
              ]}
              onPress={() => onApplyFilters({ status: status.value })}
            >
              <Text style={[
                styles.quickFilterText,
                filters.status === status.value && styles.activeQuickFilterText
              ]}>
                {status.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    
    return (
      <View style={styles.loadingMore}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.loadingMoreText}>Loading more orders...</Text>
      </View>
    );
  };

  const renderEmpty = () => (
    <EmptyState
      icon="shopping_bag"
      title="No orders found"
      message={searchQuery ? 
        `No orders match "${searchQuery}". Try adjusting your search or filters.` :
        "You haven't placed any orders yet. Start shopping to see your orders here!"
      }
      actionText={searchQuery ? "Clear Search" : "Start Shopping"}
      onAction={() => {
        if (searchQuery) {
          setSearchQuery('');
          setFilters(prev => ({ ...prev, search: '', status: '', payment_status: '' }));
          loadOrders(true);
        } else {
          navigation.navigate('Products');
        }
      }}
    />
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={orders}
        renderItem={renderOrder}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      {/* Filter Modal */}
      <FilterModal
        visible={showFilters}
        filters={filters}
        availableFilters={{
          statuses: statusOptions,
          paymentStatuses: paymentStatusOptions
        }}
        onApply={onApplyFilters}
        onClose={() => setShowFilters(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: spacing.sm,
    marginLeft: spacing.xs,
  },
  header: {
    padding: spacing.md,
    backgroundColor: colors.background.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
  },
  clearButton: {
    padding: spacing.xs,
  },
  resultsHeader: {
    marginBottom: spacing.md,
  },
  resultsText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
  },
  quickFilters: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  quickFilterButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.background.secondary,
    borderWidth: 1,
    borderColor: colors.border,
  },
  activeQuickFilter: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  quickFilterText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  activeQuickFilterText: {
    color: colors.background.primary,
  },
  listContent: {
    flexGrow: 1,
  },
  orderCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  loadingMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  loadingMoreText: {
    marginLeft: spacing.sm,
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
  },
});

export default OrderListScreen;
