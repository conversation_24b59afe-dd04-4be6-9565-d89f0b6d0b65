<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'permission_id',
        'can_create',
        'can_read',
        'can_update',
        'can_delete',
        'can_export',
        'can_import',
        'custom_permissions',
        'is_granted',
        'granted_at',
        'granted_by',
    ];

    protected $casts = [
        'can_create' => 'boolean',
        'can_read' => 'boolean',
        'can_update' => 'boolean',
        'can_delete' => 'boolean',
        'can_export' => 'boolean',
        'can_import' => 'boolean',
        'custom_permissions' => 'array',
        'is_granted' => 'boolean',
        'granted_at' => 'datetime',
    ];

    /**
     * Get the user that owns this permission.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the permission that this belongs to.
     */
    public function permission(): BelongsTo
    {
        return $this->belongsTo(ModulePermission::class, 'permission_id');
    }

    /**
     * Get the user who granted this permission.
     */
    public function grantedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'granted_by');
    }

    /**
     * Check if the user has a specific action permission.
     */
    public function hasAction(string $action): bool
    {
        if (!$this->is_granted) {
            return false;
        }
        
        $actionField = "can_{$action}";
        
        if (property_exists($this, $actionField)) {
            return $this->$actionField;
        }
        
        return false;
    }

    /**
     * Get all granted actions.
     */
    public function getGrantedActionsAttribute(): array
    {
        if (!$this->is_granted) {
            return [];
        }
        
        $actions = [];
        
        if ($this->can_create) $actions[] = 'create';
        if ($this->can_read) $actions[] = 'read';
        if ($this->can_update) $actions[] = 'update';
        if ($this->can_delete) $actions[] = 'delete';
        if ($this->can_export) $actions[] = 'export';
        if ($this->can_import) $actions[] = 'import';
        
        return $actions;
    }

    /**
     * Set permissions from array.
     */
    public function setPermissions(array $permissions): void
    {
        $this->update([
            'can_create' => $permissions['create'] ?? false,
            'can_read' => $permissions['read'] ?? false,
            'can_update' => $permissions['update'] ?? false,
            'can_delete' => $permissions['delete'] ?? false,
            'can_export' => $permissions['export'] ?? false,
            'can_import' => $permissions['import'] ?? false,
            'is_granted' => true,
            'granted_at' => now(),
            'granted_by' => auth()->id(),
        ]);
    }

    /**
     * Grant all permissions.
     */
    public function grantAll(): void
    {
        $this->update([
            'can_create' => true,
            'can_read' => true,
            'can_update' => true,
            'can_delete' => true,
            'can_export' => true,
            'can_import' => true,
            'is_granted' => true,
            'granted_at' => now(),
            'granted_by' => auth()->id(),
        ]);
    }

    /**
     * Revoke all permissions.
     */
    public function revokeAll(): void
    {
        $this->update([
            'can_create' => false,
            'can_read' => false,
            'can_update' => false,
            'can_delete' => false,
            'can_export' => false,
            'can_import' => false,
            'is_granted' => false,
        ]);
    }

    /**
     * Grant permission.
     */
    public function grant(): void
    {
        $this->update([
            'is_granted' => true,
            'granted_at' => now(),
            'granted_by' => auth()->id(),
        ]);
    }

    /**
     * Revoke permission.
     */
    public function revoke(): void
    {
        $this->update([
            'is_granted' => false,
        ]);
    }

    /**
     * Check if any permission is granted.
     */
    public function hasAnyPermission(): bool
    {
        return $this->is_granted && (
            $this->can_create || 
            $this->can_read || 
            $this->can_update || 
            $this->can_delete || 
            $this->can_export || 
            $this->can_import
        );
    }

    /**
     * Get permission summary.
     */
    public function getPermissionSummaryAttribute(): string
    {
        if (!$this->is_granted) {
            return 'Access denied';
        }
        
        $actions = $this->granted_actions;
        
        if (empty($actions)) {
            return 'No permissions';
        }
        
        if (count($actions) >= 4) {
            return 'Full access';
        }
        
        return ucfirst(implode(', ', $actions));
    }

    /**
     * Scope for granted permissions.
     */
    public function scopeGranted($query)
    {
        return $query->where('is_granted', true);
    }

    /**
     * Scope for revoked permissions.
     */
    public function scopeRevoked($query)
    {
        return $query->where('is_granted', false);
    }
}
