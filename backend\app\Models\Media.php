<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Media extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'filename',
        'original_filename',
        'path',
        'url',
        'mime_type',
        'size',
        'alt_text',
        'title',
        'description',
        'folder',
        'disk',
        'uploaded_by',
        'is_public',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'size' => 'integer',
        'is_public' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['filename', 'original_filename', 'folder', 'is_public'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the user who uploaded this media.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the full URL of the media file.
     */
    public function getFullUrlAttribute(): string
    {
        if ($this->url) {
            return $this->url;
        }

        if ($this->disk === 'public') {
            return asset('storage/' . $this->path);
        }

        return Storage::disk($this->disk)->url($this->path);
    }

    /**
     * Get the file extension.
     */
    public function getExtensionAttribute(): string
    {
        return pathinfo($this->filename, PATHINFO_EXTENSION);
    }

    /**
     * Get human readable file size.
     */
    public function getHumanSizeAttribute(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if the media is an image.
     */
    public function getIsImageAttribute(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if the media is a video.
     */
    public function getIsVideoAttribute(): bool
    {
        return str_starts_with($this->mime_type, 'video/');
    }

    /**
     * Check if the media is an audio file.
     */
    public function getIsAudioAttribute(): bool
    {
        return str_starts_with($this->mime_type, 'audio/');
    }

    /**
     * Check if the media is a document.
     */
    public function getIsDocumentAttribute(): bool
    {
        $documentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
        ];

        return in_array($this->mime_type, $documentTypes);
    }

    /**
     * Get thumbnail URL for images.
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        if (!$this->is_image) {
            return null;
        }

        // Check if thumbnail exists
        $thumbnailPath = $this->getThumbnailPath();
        
        if (Storage::disk($this->disk)->exists($thumbnailPath)) {
            if ($this->disk === 'public') {
                return asset('storage/' . $thumbnailPath);
            }
            return Storage::disk($this->disk)->url($thumbnailPath);
        }

        // Return original image if no thumbnail
        return $this->full_url;
    }

    /**
     * Get thumbnail path.
     */
    public function getThumbnailPath(): string
    {
        $pathInfo = pathinfo($this->path);
        return $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
    }

    /**
     * Scope for images only.
     */
    public function scopeImages($query)
    {
        return $query->where('mime_type', 'like', 'image/%');
    }

    /**
     * Scope for documents only.
     */
    public function scopeDocuments($query)
    {
        $documentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'text/csv',
        ];

        return $query->whereIn('mime_type', $documentTypes);
    }

    /**
     * Scope for specific folder.
     */
    public function scopeInFolder($query, string $folder)
    {
        return $query->where('folder', $folder);
    }

    /**
     * Scope for public files.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for files uploaded by specific user.
     */
    public function scopeUploadedBy($query, int $userId)
    {
        return $query->where('uploaded_by', $userId);
    }

    /**
     * Delete the physical file when model is deleted.
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($media) {
            // Delete the main file
            if (Storage::disk($media->disk)->exists($media->path)) {
                Storage::disk($media->disk)->delete($media->path);
            }

            // Delete thumbnail if it exists
            $thumbnailPath = $media->getThumbnailPath();
            if (Storage::disk($media->disk)->exists($thumbnailPath)) {
                Storage::disk($media->disk)->delete($thumbnailPath);
            }
        });
    }

    /**
     * Get media statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_files' => self::count(),
            'total_size' => self::sum('size'),
            'total_images' => self::images()->count(),
            'total_documents' => self::documents()->count(),
            'folders' => self::distinct('folder')->whereNotNull('folder')->pluck('folder')->toArray(),
            'recent_uploads' => self::latest()->limit(5)->get(),
        ];
    }

    /**
     * Create thumbnail for image.
     */
    public function createThumbnail(int $width = 300, int $height = 300): bool
    {
        if (!$this->is_image) {
            return false;
        }

        try {
            $image = \Intervention\Image\Facades\Image::make(Storage::disk($this->disk)->path($this->path));
            $image->fit($width, $height);
            
            $thumbnailPath = $this->getThumbnailPath();
            $thumbnailDir = dirname(Storage::disk($this->disk)->path($thumbnailPath));
            
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }
            
            $image->save(Storage::disk($this->disk)->path($thumbnailPath));
            
            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to create thumbnail: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get file icon based on mime type.
     */
    public function getIconAttribute(): string
    {
        if ($this->is_image) {
            return 'image';
        }

        if ($this->is_video) {
            return 'video';
        }

        if ($this->is_audio) {
            return 'audio';
        }

        switch ($this->mime_type) {
            case 'application/pdf':
                return 'pdf';
            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return 'word';
            case 'application/vnd.ms-excel':
            case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                return 'excel';
            case 'application/vnd.ms-powerpoint':
            case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                return 'powerpoint';
            case 'application/zip':
            case 'application/x-rar-compressed':
            case 'application/x-7z-compressed':
                return 'archive';
            case 'text/plain':
                return 'text';
            case 'text/csv':
                return 'csv';
            default:
                return 'file';
        }
    }
}
