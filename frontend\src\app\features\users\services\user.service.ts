import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  role: 'super_admin' | 'admin' | 'manager' | 'customer';
  role_color: string;
  is_active: boolean;
  email_verified_at?: string;
  last_login_at?: string;
  profile_image_url?: string;
  permissions?: string[];
  orders_count?: number;
  total_spent?: number;
  average_order_value?: number;
  last_order_date?: string;
  created_at: string;
  updated_at: string;
}

export interface Role {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  permissions: Permission[];
  users_count?: number;
  created_at: string;
  updated_at: string;
}

export interface Permission {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  group: string;
}

export interface UserFilters {
  search?: string;
  role?: string;
  is_active?: boolean;
  email_verified?: boolean;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  per_page?: number;
  page?: number;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  phone?: string;
  password: string;
  password_confirmation: string;
  role: string;
  is_active?: boolean;
  permissions?: string[];
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  phone?: string;
  role?: string;
  is_active?: boolean;
  permissions?: string[];
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  links: {
    first: string;
    last: string;
    prev?: string;
    next?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly API_URL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get paginated list of users
   */
  getUsers(filters: UserFilters = {}): Observable<PaginatedResponse<User>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof UserFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<PaginatedResponse<User>>(`${this.API_URL}/users`, { params });
  }

  /**
   * Get single user by ID
   */
  getUser(id: number): Observable<ApiResponse<User>> {
    return this.http.get<ApiResponse<User>>(`${this.API_URL}/users/${id}`);
  }

  /**
   * Create new user
   */
  createUser(userData: CreateUserRequest): Observable<ApiResponse<User>> {
    return this.http.post<ApiResponse<User>>(`${this.API_URL}/users`, userData);
  }

  /**
   * Update existing user
   */
  updateUser(id: number, userData: UpdateUserRequest): Observable<ApiResponse<User>> {
    return this.http.put<ApiResponse<User>>(`${this.API_URL}/users/${id}`, userData);
  }

  /**
   * Delete user
   */
  deleteUser(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.API_URL}/users/${id}`);
  }

  /**
   * Update user status (activate/deactivate)
   */
  updateUserStatus(id: number, isActive: boolean): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/users/${id}/status`, {
      is_active: isActive
    });
  }

  /**
   * Reset user password
   */
  resetPassword(id: number, newPassword: string): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/users/${id}/reset-password`, {
      password: newPassword,
      password_confirmation: newPassword
    });
  }

  /**
   * Send password reset email
   */
  sendPasswordResetEmail(id: number): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/users/${id}/send-password-reset`, {});
  }

  /**
   * Verify user email
   */
  verifyEmail(id: number): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/users/${id}/verify-email`, {});
  }

  /**
   * Bulk update user status
   */
  bulkUpdateStatus(userIds: number[], isActive: boolean): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/users/bulk-update-status`, {
      user_ids: userIds,
      is_active: isActive
    });
  }

  /**
   * Get user statistics
   */
  getStatistics(): Observable<ApiResponse<any>> {
    return this.http.get<ApiResponse<any>>(`${this.API_URL}/users/statistics`);
  }

  /**
   * Get all roles
   */
  getRoles(): Observable<ApiResponse<Role[]>> {
    return this.http.get<ApiResponse<Role[]>>(`${this.API_URL}/roles`);
  }

  /**
   * Get single role by ID
   */
  getRole(id: number): Observable<ApiResponse<Role>> {
    return this.http.get<ApiResponse<Role>>(`${this.API_URL}/roles/${id}`);
  }

  /**
   * Create new role
   */
  createRole(roleData: Partial<Role>): Observable<ApiResponse<Role>> {
    return this.http.post<ApiResponse<Role>>(`${this.API_URL}/roles`, roleData);
  }

  /**
   * Update existing role
   */
  updateRole(id: number, roleData: Partial<Role>): Observable<ApiResponse<Role>> {
    return this.http.put<ApiResponse<Role>>(`${this.API_URL}/roles/${id}`, roleData);
  }

  /**
   * Delete role
   */
  deleteRole(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.API_URL}/roles/${id}`);
  }

  /**
   * Get all permissions
   */
  getPermissions(): Observable<ApiResponse<Permission[]>> {
    return this.http.get<ApiResponse<Permission[]>>(`${this.API_URL}/permissions`);
  }

  /**
   * Get permissions grouped by category
   */
  getPermissionsGrouped(): Observable<ApiResponse<{ [key: string]: Permission[] }>> {
    return this.http.get<ApiResponse<{ [key: string]: Permission[] }>>(`${this.API_URL}/permissions/grouped`);
  }

  /**
   * Search users
   */
  searchUsers(query: string, limit: number = 10): Observable<ApiResponse<User[]>> {
    const params = new HttpParams()
      .set('search', query)
      .set('per_page', limit.toString());

    return this.http.get<ApiResponse<User[]>>(`${this.API_URL}/users/search`, { params });
  }

  /**
   * Export users
   */
  exportUsers(format: 'csv' | 'excel' = 'csv', filters: UserFilters = {}): Observable<Blob> {
    let params = new HttpParams().set('format', format);
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof UserFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get(`${this.API_URL}/users/export`, {
      params,
      responseType: 'blob'
    });
  }

  /**
   * Import users
   */
  importUsers(file: File): Observable<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<ApiResponse<any>>(`${this.API_URL}/users/import`, formData);
  }

  /**
   * Upload user profile image
   */
  uploadProfileImage(userId: number, file: File): Observable<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('image', file);

    return this.http.post<ApiResponse<any>>(`${this.API_URL}/users/${userId}/profile-image`, formData);
  }

  /**
   * Get user activity log
   */
  getUserActivity(userId: number, limit: number = 20): Observable<ApiResponse<any[]>> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<ApiResponse<any[]>>(`${this.API_URL}/users/${userId}/activity`, { params });
  }

  /**
   * Get role options for dropdowns
   */
  getRoleOptions(): { value: string; label: string; color: string }[] {
    return [
      { value: 'super_admin', label: 'Super Admin', color: 'warn' },
      { value: 'admin', label: 'Admin', color: 'primary' },
      { value: 'manager', label: 'Manager', color: 'accent' },
      { value: 'customer', label: 'Customer', color: 'basic' }
    ];
  }

  /**
   * Get status options for dropdowns
   */
  getStatusOptions(): { value: boolean | string; label: string }[] {
    return [
      { value: '', label: 'All Status' },
      { value: true, label: 'Active' },
      { value: false, label: 'Inactive' }
    ];
  }

  /**
   * Get email verification options for dropdowns
   */
  getEmailVerificationOptions(): { value: boolean | string; label: string }[] {
    return [
      { value: '', label: 'All Users' },
      { value: true, label: 'Email Verified' },
      { value: false, label: 'Email Not Verified' }
    ];
  }
}
