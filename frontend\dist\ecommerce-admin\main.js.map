{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;AAC+C;;AASzC,MAAOC,YAAY;EAPzBC,YAAA;IAQE,KAAAC,KAAK,GAAG,+BAA+B;;;;uBAD5BF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTvBP,4DADF,aAA2B,SACrB;UAAAA,oDAAA,GAAS;UAAAA,0DAAA,EAAK;UAIZA,4DAHN,aAAuB,aACG,aACC,SACjB;UAAAA,oDAAA,qBAAc;UAAAA,0DAAA,EAAK;UACvBA,4DAAA,aAAyB;UAAAA,oDAAA,YAAK;UAChCA,0DADgC,EAAM,EAChC;UAEJA,4DADF,cAAuB,UACjB;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAK;UACrBA,4DAAA,cAAyB;UAAAA,oDAAA,WAAG;UAC9BA,0DAD8B,EAAM,EAC9B;UAEJA,4DADF,cAAuB,UACjB;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAK;UACpBA,4DAAA,cAAyB;UAAAA,oDAAA,WAAG;UAC9BA,0DAD8B,EAAM,EAC9B;UAEJA,4DADF,cAAuB,UACjB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAK;UAChBA,4DAAA,cAAyB;UAAAA,oDAAA,eAAO;UAEpCA,0DAFoC,EAAM,EAClC,EACF;UAGJA,4DADF,cAA6B,UACvB;UAAAA,oDAAA,oDAAkC;UAAAA,0DAAA,EAAK;UAC3CA,4DAAA,SAAG;UAAAA,oDAAA,qBAAa;UAAAA,4DAAA,YAAgD;UAAAA,oDAAA,6BAAqB;UAAIA,0DAAJ,EAAI,EAAI;UAC7FA,4DAAA,SAAG;UAAAA,oDAAA,4BAAoB;UAAAA,4DAAA,YAAgD;UAAAA,oDAAA,6BAAqB;UAAIA,0DAAJ,EAAI,EAAI;UACpGA,4DAAA,iBAAgC;UAAAA,oDAAA,+BAAuB;UAG7DA,0DAH6D,EAAS,EAC5D,EACF,EACF;;;UA5BAA,uDAAA,GAAS;UAATA,+DAAA,CAAAQ,GAAA,CAAAZ,KAAA,CAAS;;;qBDKHH,yDAAY;MAAAqB,MAAA;IAAA;EAAA;;;;;;;;;;;;;;AENyC;AACd;AAEnDC,+EAAoB,CAACrB,4DAAY,CAAC,CAACsB,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC,C", "sources": ["./src/app/app.component.ts", "./src/app/app.component.html", "./src/main.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = '🛒 E-Commerce Admin Dashboard';\n}\n", "<div class=\"app-container\">\n  <h1>{{title}}</h1>\n  <div class=\"dashboard\">\n    <div class=\"stats-grid\">\n      <div class=\"stat-card\">\n        <h3>Total Products</h3>\n        <div class=\"stat-number\">1,234</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Total Orders</h3>\n        <div class=\"stat-number\">567</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Total Users</h3>\n        <div class=\"stat-number\">890</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Revenue</h3>\n        <div class=\"stat-number\">$12,345</div>\n      </div>\n    </div>\n\n    <div class=\"welcome-section\">\n      <h2>🎉 Your E-Commerce System is Live!</h2>\n      <p>Backend API: <a href=\"http://localhost:8001\" target=\"_blank\">http://localhost:8001</a></p>\n      <p>Frontend Dashboard: <a href=\"http://localhost:4200\" target=\"_blank\">http://localhost:4200</a></p>\n      <button onclick=\"testBackend()\">Test Backend Connection</button>\n    </div>\n  </div>\n</div>\n\n<script>\nfunction testBackend() {\n  fetch('http://localhost:8001/api/dashboard/stats')\n    .then(response => response.json())\n    .then(data => {\n      alert('Backend connection successful! ' + JSON.stringify(data, null, 2));\n    })\n    .catch(error => {\n      alert('Backend connection failed: ' + error.message);\n    });\n}\n</script>\n", "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\n\nbootstrapApplication(AppComponent).catch(err => console.error(err));\n"], "names": ["CommonModule", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "styles", "bootstrapApplication", "catch", "err", "console", "error"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}