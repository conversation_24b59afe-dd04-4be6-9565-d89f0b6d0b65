<div class="address-list-container">
  <div class="header">
    <h2>User Addresses</h2>
    <button mat-raised-button color="primary" (click)="openAddressForm()">
      <mat-icon>add</mat-icon>
      Add Address
    </button>
  </div>

  <div class="content" *ngIf="!loading">
    <!-- Address Cards View -->
    <div class="address-cards" *ngIf="hasAddresses()">
      <div class="address-section" *ngIf="getShippingAddresses().length > 0">
        <h3>Shipping Addresses</h3>
        <div class="cards-grid">
          <mat-card class="address-card" *ngFor="let address of getShippingAddresses()">
            <mat-card-header>
              <mat-card-title>
                {{ getFullName(address) }}
                <span class="default-badge" *ngIf="address.is_default">DEFAULT</span>
              </mat-card-title>
              <mat-card-subtitle>
                <span class="type-badge" [ngClass]="getTypeBadgeClass(address.type)">
                  {{ getTypeDisplayText(address.type) }}
                </span>
              </mat-card-subtitle>
            </mat-card-header>
            
            <mat-card-content>
              <div class="address-details">
                <p class="company" *ngIf="address.company">{{ address.company }}</p>
                <p>{{ address.address_line_1 }}</p>
                <p *ngIf="address.address_line_2">{{ address.address_line_2 }}</p>
                <p>{{ address.city }}, {{ address.state }} {{ address.postal_code }}</p>
                <p>{{ address.country }}</p>
                <p class="phone" *ngIf="address.phone">
                  <mat-icon>phone</mat-icon>
                  {{ address.phone }}
                </p>
              </div>
            </mat-card-content>
            
            <mat-card-actions>
              <button mat-button (click)="openAddressForm(address)">
                <mat-icon>edit</mat-icon>
                Edit
              </button>
              <button mat-button 
                      (click)="setAsDefault(address)" 
                      *ngIf="!address.is_default"
                      color="primary">
                <mat-icon>star</mat-icon>
                Set Default
              </button>
              <button mat-button 
                      (click)="deleteAddress(address)" 
                      *ngIf="canDelete(address)"
                      color="warn">
                <mat-icon>delete</mat-icon>
                Delete
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>

      <div class="address-section" *ngIf="getBillingAddresses().length > 0">
        <h3>Billing Addresses</h3>
        <div class="cards-grid">
          <mat-card class="address-card" *ngFor="let address of getBillingAddresses()">
            <mat-card-header>
              <mat-card-title>
                {{ getFullName(address) }}
                <span class="default-badge" *ngIf="address.is_default">DEFAULT</span>
              </mat-card-title>
              <mat-card-subtitle>
                <span class="type-badge" [ngClass]="getTypeBadgeClass(address.type)">
                  {{ getTypeDisplayText(address.type) }}
                </span>
              </mat-card-subtitle>
            </mat-card-header>
            
            <mat-card-content>
              <div class="address-details">
                <p class="company" *ngIf="address.company">{{ address.company }}</p>
                <p>{{ address.address_line_1 }}</p>
                <p *ngIf="address.address_line_2">{{ address.address_line_2 }}</p>
                <p>{{ address.city }}, {{ address.state }} {{ address.postal_code }}</p>
                <p>{{ address.country }}</p>
                <p class="phone" *ngIf="address.phone">
                  <mat-icon>phone</mat-icon>
                  {{ address.phone }}
                </p>
              </div>
            </mat-card-content>
            
            <mat-card-actions>
              <button mat-button (click)="openAddressForm(address)">
                <mat-icon>edit</mat-icon>
                Edit
              </button>
              <button mat-button 
                      (click)="setAsDefault(address)" 
                      *ngIf="!address.is_default"
                      color="primary">
                <mat-icon>star</mat-icon>
                Set Default
              </button>
              <button mat-button 
                      (click)="deleteAddress(address)" 
                      *ngIf="canDelete(address)"
                      color="warn">
                <mat-icon>delete</mat-icon>
                Delete
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="!hasAddresses()">
      <mat-icon class="empty-icon">location_off</mat-icon>
      <h3>No Addresses Found</h3>
      <p>You haven't added any addresses yet. Add your first address to get started.</p>
      <button mat-raised-button color="primary" (click)="openAddressForm()">
        <mat-icon>add</mat-icon>
        Add First Address
      </button>
    </div>

    <!-- Table View (Alternative) -->
    <div class="table-view" style="display: none;">
      <table mat-table [dataSource]="addresses" class="addresses-table">
        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Name</th>
          <td mat-cell *matCellDef="let address">
            <div class="name-cell">
              <strong>{{ getFullName(address) }}</strong>
              <small *ngIf="address.company">{{ address.company }}</small>
            </div>
          </td>
        </ng-container>

        <!-- Type Column -->
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef>Type</th>
          <td mat-cell *matCellDef="let address">
            <span class="type-badge" [ngClass]="getTypeBadgeClass(address.type)">
              {{ getTypeDisplayText(address.type) }}
            </span>
          </td>
        </ng-container>

        <!-- Address Column -->
        <ng-container matColumnDef="address">
          <th mat-header-cell *matHeaderCellDef>Address</th>
          <td mat-cell *matCellDef="let address">
            <div class="address-cell">
              {{ formatAddress(address) }}
            </div>
          </td>
        </ng-container>

        <!-- Default Column -->
        <ng-container matColumnDef="default">
          <th mat-header-cell *matHeaderCellDef>Default</th>
          <td mat-cell *matCellDef="let address">
            <mat-icon *ngIf="address.is_default" color="primary">star</mat-icon>
            <span *ngIf="!address.is_default">-</span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let address">
            <button mat-icon-button (click)="openAddressForm(address)" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button 
                    (click)="setAsDefault(address)" 
                    *ngIf="!address.is_default"
                    matTooltip="Set as Default">
              <mat-icon>star_border</mat-icon>
            </button>
            <button mat-icon-button 
                    (click)="deleteAddress(address)" 
                    *ngIf="canDelete(address)"
                    color="warn"
                    matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-state" *ngIf="loading">
    <mat-spinner></mat-spinner>
    <p>Loading addresses...</p>
  </div>
</div>
