<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductVariation extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'sku',
        'mrp',
        'selling_price',
        'price',
        'sale_price',
        'discount_percentage',
        'stock_quantity',
        'manage_stock',
        'stock_status',
        'weight',
        'length',
        'width',
        'height',
        'image',
        'attributes',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'mrp' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'weight' => 'decimal:2',
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'stock_quantity' => 'integer',
        'manage_stock' => 'boolean',
        'is_active' => 'boolean',
        'attributes' => 'array',
    ];

    /**
     * Get the product that owns the variation.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the effective price (sale price if available, otherwise regular price).
     */
    public function getEffectivePriceAttribute(): float
    {
        return $this->sale_price && $this->sale_price > 0 ? $this->sale_price : $this->price;
    }

    /**
     * Check if variation is on sale.
     */
    public function getIsOnSaleAttribute(): bool
    {
        return $this->sale_price && $this->sale_price > 0 && $this->sale_price < $this->price;
    }

    /**
     * Check if variation is in stock.
     */
    public function getIsInStockAttribute(): bool
    {
        if (!$this->manage_stock) {
            return $this->stock_status === 'in_stock';
        }

        return $this->stock_quantity > 0;
    }

    /**
     * Get the variation image URL.
     */
    public function getImageUrlAttribute(): ?string
    {
        if (!$this->image) {
            return $this->product->main_image_url;
        }

        if (str_starts_with($this->image, 'http')) {
            return $this->image;
        }

        return asset('storage/' . $this->image);
    }

    /**
     * Get variation display name.
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->product->name;
        
        if (!empty($this->attributes)) {
            $attributeStrings = [];
            foreach ($this->attributes as $key => $value) {
                $attributeStrings[] = ucfirst($key) . ': ' . $value;
            }
            $name .= ' (' . implode(', ', $attributeStrings) . ')';
        }

        return $name;
    }

    /**
     * Scope for active variations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for in-stock variations.
     */
    public function scopeInStock($query)
    {
        return $query->where(function ($q) {
            $q->where('manage_stock', false)
              ->where('stock_status', 'in_stock')
              ->orWhere(function ($q2) {
                  $q2->where('manage_stock', true)
                     ->where('stock_quantity', '>', 0);
              });
        });
    }

    /**
     * Update stock quantity.
     */
    public function updateStock(int $quantity, string $operation = 'set'): void
    {
        if (!$this->manage_stock) {
            return;
        }

        switch ($operation) {
            case 'increase':
                $this->increment('stock_quantity', $quantity);
                break;
            case 'decrease':
                $this->decrement('stock_quantity', $quantity);
                break;
            case 'set':
            default:
                $this->update(['stock_quantity' => $quantity]);
                break;
        }

        // Update stock status
        $this->update([
            'stock_status' => $this->stock_quantity > 0 ? 'in_stock' : 'out_of_stock'
        ]);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($variation) {
            if (empty($variation->sku)) {
                $variation->sku = Product::generateUniqueSku();
            }
        });
    }
}
