<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class UserGroup extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'color',
        'is_active',
        'discount_percentage',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'discount_percentage' => 'decimal:2',
    ];

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'description', 'is_active', 'discount_percentage'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the users in this group.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_group_members');
    }

    /**
     * Get the user who created this group.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the coupons targeted to this group.
     */
    public function coupons(): BelongsToMany
    {
        return $this->belongsToMany(Coupon::class, 'coupon_user_groups');
    }

    /**
     * Scope for active groups.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the total number of users in this group.
     */
    public function getUsersCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * Get the total number of active users in this group.
     */
    public function getActiveUsersCountAttribute(): int
    {
        return $this->users()->where('is_active', true)->count();
    }

    /**
     * Check if group has specific user.
     */
    public function hasUser(User $user): bool
    {
        return $this->users()->where('users.id', $user->id)->exists();
    }

    /**
     * Add user to group.
     */
    public function addUser(User $user): void
    {
        if (!$this->hasUser($user)) {
            $this->users()->attach($user->id, [
                'added_at' => now(),
                'added_by' => auth()->id(),
            ]);

            activity()
                ->causedBy(auth()->user())
                ->performedOn($this)
                ->withProperties(['user_id' => $user->id, 'user_name' => $user->name])
                ->log('User added to group');
        }
    }

    /**
     * Remove user from group.
     */
    public function removeUser(User $user): void
    {
        if ($this->hasUser($user)) {
            $this->users()->detach($user->id);

            activity()
                ->causedBy(auth()->user())
                ->performedOn($this)
                ->withProperties(['user_id' => $user->id, 'user_name' => $user->name])
                ->log('User removed from group');
        }
    }

    /**
     * Get group statistics.
     */
    public function getStatistics(): array
    {
        $users = $this->users();
        
        return [
            'total_users' => $users->count(),
            'active_users' => $users->where('is_active', true)->count(),
            'total_orders' => Order::whereIn('user_id', $users->pluck('id'))->count(),
            'total_revenue' => Order::whereIn('user_id', $users->pluck('id'))
                ->where('status', 'completed')
                ->sum('total_amount'),
            'average_order_value' => Order::whereIn('user_id', $users->pluck('id'))
                ->where('status', 'completed')
                ->avg('total_amount') ?? 0,
        ];
    }
}
