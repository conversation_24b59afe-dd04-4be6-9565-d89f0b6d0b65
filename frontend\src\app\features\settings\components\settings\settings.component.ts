import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { SettingsService, SettingGroup } from '../../services/settings.service';
import { LoadingService } from '../../../../core/services/loading.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {
  settingGroups: SettingGroup[] = [];
  selectedGroup: SettingGroup | null = null;
  loading = false;
  saving = false;

  constructor(
    private settingsService: SettingsService,
    private loadingService: LoadingService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadSettings();
  }

  private loadSettings(): void {
    this.loading = true;
    this.loadingService.setLoading(true);

    this.settingsService.getSettingsGrouped().subscribe({
      next: (response) => {
        if (response.success) {
          this.settingGroups = response.data;
          
          // Select first group by default
          if (this.settingGroups.length > 0) {
            this.selectedGroup = this.settingGroups[0];
          }
        }
        this.loading = false;
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error loading settings:', error);
        this.snackBar.open('Error loading settings', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loading = false;
        this.loadingService.setLoading(false);
      }
    });
  }

  selectGroup(group: SettingGroup): void {
    this.selectedGroup = group;
  }

  onSettingsUpdate(settings: { [key: string]: any }): void {
    this.saving = true;
    this.loadingService.setLoading(true);

    this.settingsService.updateSettings(settings).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Settings updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          
          // Refresh the current group
          this.loadSettings();
        }
        this.saving = false;
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error updating settings:', error);
        this.snackBar.open('Error updating settings', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.saving = false;
        this.loadingService.setLoading(false);
      }
    });
  }

  resetGroupSettings(): void {
    if (!this.selectedGroup) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Reset Settings',
        message: `Are you sure you want to reset all ${this.selectedGroup.label} settings to their default values? This action cannot be undone.`,
        confirmText: 'Reset',
        cancelText: 'Cancel',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.selectedGroup) {
        this.loadingService.setLoading(true);

        this.settingsService.resetSettings(this.selectedGroup.name).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Settings reset successfully', 'Close', {
                duration: 3000,
                panelClass: ['success-snackbar']
              });
              this.loadSettings();
            }
            this.loadingService.setLoading(false);
          },
          error: (error) => {
            console.error('Error resetting settings:', error);
            this.snackBar.open('Error resetting settings', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            this.loadingService.setLoading(false);
          }
        });
      }
    });
  }

  exportSettings(): void {
    this.loadingService.setLoading(true);

    this.settingsService.exportSettings().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        window.URL.revokeObjectURL(url);
        
        this.snackBar.open('Settings exported successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error exporting settings:', error);
        this.snackBar.open('Error exporting settings', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.importSettings(file);
    }
  }

  private importSettings(file: File): void {
    this.loadingService.setLoading(true);

    this.settingsService.importSettings(file).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Settings imported successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadSettings();
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error importing settings:', error);
        this.snackBar.open('Error importing settings', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  clearCache(): void {
    this.loadingService.setLoading(true);

    this.settingsService.clearCache().subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Cache cleared successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error clearing cache:', error);
        this.snackBar.open('Error clearing cache', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  getGroupIcon(groupName: string): string {
    const iconMap: { [key: string]: string } = {
      general: 'settings',
      ecommerce: 'store',
      email: 'email',
      payment: 'payment',
      shipping: 'local_shipping',
      tax: 'receipt',
      security: 'security',
      advanced: 'tune'
    };
    return iconMap[groupName] || 'settings';
  }

  getGroupColor(groupName: string): string {
    const colorMap: { [key: string]: string } = {
      general: 'primary',
      ecommerce: 'accent',
      email: 'warn',
      payment: 'primary',
      shipping: 'accent',
      tax: 'warn',
      security: 'primary',
      advanced: 'accent'
    };
    return colorMap[groupName] || 'primary';
  }

  isGroupSelected(group: SettingGroup): boolean {
    return this.selectedGroup?.name === group.name;
  }

  get hasUnsavedChanges(): boolean {
    // This would be implemented based on form state
    return false;
  }

  get canResetSettings(): boolean {
    return this.selectedGroup !== null && !this.saving;
  }

  get canExportSettings(): boolean {
    return !this.loading && !this.saving;
  }

  get canImportSettings(): boolean {
    return !this.loading && !this.saving;
  }

  get canClearCache(): boolean {
    return !this.loading && !this.saving;
  }
}
