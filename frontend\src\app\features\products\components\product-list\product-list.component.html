<div class="product-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>Products</h1>
      <p>Manage your product catalog</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="createProduct()">
        <mat-icon>add</mat-icon>
        Add Product
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-container">
        <!-- Search -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search products</mat-label>
          <input
            matInput
            placeholder="Search by name, SKU, or description"
            (input)="onSearch($event.target.value)"
            [value]="filters.search || ''"
          >
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Category Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Category</mat-label>
          <mat-select [(value)]="filters.category_id" (selectionChange)="onFilterChange()">
            <mat-option value="">All Categories</mat-option>
            <mat-option *ngFor="let category of categories" [value]="category.id">
              {{ category.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Status Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(value)]="filters.status" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of statusOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Stock Status Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Stock Status</mat-label>
          <mat-select [(value)]="filters.stock_status" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of stockStatusOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Featured Filter -->
        <mat-checkbox
          [(ngModel)]="filters.featured"
          (change)="onFilterChange()"
          class="featured-filter"
        >
          Featured Only
        </mat-checkbox>

        <!-- Clear Filters -->
        <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
          <mat-icon>clear</mat-icon>
          Clear Filters
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Bulk Actions -->
  <div class="bulk-actions" *ngIf="selection.selected.length > 0">
    <mat-card>
      <mat-card-content>
        <div class="bulk-actions-content">
          <span class="selection-count">
            {{ selection.selected.length }} product(s) selected
          </span>
          
          <div class="bulk-action-buttons">
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus('published')"
            >
              <mat-icon>publish</mat-icon>
              Publish
            </button>
            
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus('draft')"
            >
              <mat-icon>edit</mat-icon>
              Draft
            </button>
            
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus('archived')"
            >
              <mat-icon>archive</mat-icon>
              Archive
            </button>
            
            <button
              mat-stroked-button
              color="warn"
              [disabled]="bulkActionLoading"
              (click)="bulkDelete()"
            >
              <mat-icon>delete</mat-icon>
              Delete
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Data Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <!-- Table Actions -->
      <div class="table-actions">
        <div class="table-info">
          <span>{{ totalItems }} products found</span>
        </div>
        
        <div class="table-action-buttons">
          <button mat-icon-button (click)="loadProducts()" [disabled]="loading">
            <mat-icon>refresh</mat-icon>
          </button>
          
          <button mat-icon-button (click)="exportProducts()">
            <mat-icon>download</mat-icon>
          </button>
        </div>
      </div>

      <!-- Loading Indicator -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading products...</p>
      </div>

      <!-- Data Table -->
      <div class="table-container" *ngIf="!loading">
        <table mat-table [dataSource]="dataSource" matSort class="products-table">
          
          <!-- Select Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? masterToggle() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
              ></mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let product">
              <mat-checkbox
                (click)="$event.stopPropagation()"
                (change)="$event ? selection.toggle(product) : null"
                [checked]="selection.isSelected(product)"
              ></mat-checkbox>
            </td>
          </ng-container>

          <!-- Image Column -->
          <ng-container matColumnDef="image">
            <th mat-header-cell *matHeaderCellDef>Image</th>
            <td mat-cell *matCellDef="let product">
              <div class="product-image">
                <img
                  [src]="product.main_image_url || '/assets/images/placeholder-product.png'"
                  [alt]="product.name"
                  (error)="$event.target.src='/assets/images/placeholder-product.png'"
                >
              </div>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let product">
              <div class="product-name">
                <span class="name">{{ product.name }}</span>
                <span class="sku">SKU: {{ product.sku }}</span>
              </div>
            </td>
          </ng-container>

          <!-- SKU Column -->
          <ng-container matColumnDef="sku">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>SKU</th>
            <td mat-cell *matCellDef="let product">{{ product.sku }}</td>
          </ng-container>

          <!-- Category Column -->
          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef>Category</th>
            <td mat-cell *matCellDef="let product">
              {{ getCategoryName(product.category_id) }}
            </td>
          </ng-container>

          <!-- Price Column -->
          <ng-container matColumnDef="price">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Price</th>
            <td mat-cell *matCellDef="let product">
              <div class="price-info">
                <span class="current-price">{{ formatPrice(product.effective_price) }}</span>
                <span *ngIf="product.is_on_sale" class="original-price">
                  {{ formatPrice(product.price) }}
                </span>
                <mat-chip *ngIf="product.is_on_sale" class="sale-chip">
                  {{ product.sale_percentage }}% OFF
                </mat-chip>
              </div>
            </td>
          </ng-container>

          <!-- Stock Column -->
          <ng-container matColumnDef="stock">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Stock</th>
            <td mat-cell *matCellDef="let product">
              <div class="stock-info">
                <span class="stock-quantity">{{ product.stock_quantity }}</span>
                <mat-chip [color]="getStockStatusColor(product.stock_status)" class="stock-status">
                  {{ product.stock_status | titlecase }}
                </mat-chip>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let product">
              <mat-chip [color]="getStatusColor(product.status)">
                {{ product.status | titlecase }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Featured Column -->
          <ng-container matColumnDef="featured">
            <th mat-header-cell *matHeaderCellDef>Featured</th>
            <td mat-cell *matCellDef="let product">
              <mat-icon *ngIf="product.featured" color="primary">star</mat-icon>
              <mat-icon *ngIf="!product.featured" class="inactive-icon">star_border</mat-icon>
            </td>
          </ng-container>

          <!-- Created At Column -->
          <ng-container matColumnDef="created_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Created</th>
            <td mat-cell *matCellDef="let product">
              {{ product.created_at | date:'short' }}
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let product">
              <div class="action-buttons">
                <button
                  mat-icon-button
                  [matTooltip]="'View Product'"
                  (click)="viewProduct(product)"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  [matTooltip]="'Edit Product'"
                  (click)="editProduct(product)"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  [matTooltip]="'Duplicate Product'"
                  (click)="duplicateProduct(product)"
                >
                  <mat-icon>content_copy</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  [matTooltip]="'Delete Product'"
                  color="warn"
                  (click)="deleteProduct(product)"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data -->
        <div *ngIf="dataSource.data.length === 0" class="no-data">
          <mat-icon>inventory_2</mat-icon>
          <h3>No products found</h3>
          <p>Try adjusting your filters or create a new product.</p>
          <button mat-raised-button color="primary" (click)="createProduct()">
            <mat-icon>add</mat-icon>
            Create Product
          </button>
        </div>
      </div>

      <!-- Paginator -->
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        showFirstLastButtons
        *ngIf="!loading && dataSource.data.length > 0"
      ></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
