<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Commerce Admin Login</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .login-title {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }

        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .demo-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }

        .demo-info h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .demo-info p {
            color: #7f8c8d;
            font-size: 14px;
            margin: 5px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .backend-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }

        .backend-status.online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .backend-status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.online {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-indicator.offline {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🛒</div>
        <h2 class="login-title">Admin Login</h2>
        <p class="login-subtitle">E-Commerce Management System</p>
        
        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> Password
                </label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit" id="loginBtn" class="login-btn">
                <i class="fas fa-sign-in-alt"></i> Login to Dashboard
            </button>
        </form>
        
        <div class="demo-info">
            <h4><i class="fas fa-info-circle"></i> Demo Credentials</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> admin123</p>
        </div>
        
        <div id="backendStatus" class="backend-status">
            <span class="status-indicator"></span>
            Checking backend connection...
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        
        // Check backend status on load
        checkBackendStatus();
        
        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<span class="spinner"></span> Logging in...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Store authentication data
                    localStorage.setItem('auth_token', data.data.token);
                    localStorage.setItem('current_user', JSON.stringify(data.data.user));
                    
                    showSuccess('Login successful! Redirecting to dashboard...');
                    
                    // Redirect to admin dashboard
                    setTimeout(() => {
                        window.location.href = 'admin-panel.html';
                    }, 1500);
                } else {
                    showError(data.message || 'Login failed');
                }
            } catch (error) {
                showError('Connection error. Please make sure the backend server is running on port 8001.');
            }
            
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login to Dashboard';
        });

        async function checkBackendStatus() {
            const statusDiv = document.getElementById('backendStatus');
            const indicator = statusDiv.querySelector('.status-indicator');
            
            try {
                const response = await fetch(`${API_BASE}/auth/check`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    statusDiv.className = 'backend-status online';
                    indicator.className = 'status-indicator online';
                    statusDiv.innerHTML = '<span class="status-indicator online"></span> Backend server is online and ready';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                statusDiv.className = 'backend-status offline';
                indicator.className = 'status-indicator offline';
                statusDiv.innerHTML = '<span class="status-indicator offline"></span> Backend server is offline. Please start the server with: node mock-backend.js';
            }
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }

        // Check if already logged in
        const token = localStorage.getItem('auth_token');
        if (token) {
            // Verify token and redirect if valid
            fetch(`${API_BASE}/auth/check`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.authenticated) {
                    window.location.href = 'admin-panel.html';
                }
            })
            .catch(() => {
                // Token invalid, stay on login page
                localStorage.removeItem('auth_token');
                localStorage.removeItem('current_user');
            });
        }
    </script>
</body>
</html>
