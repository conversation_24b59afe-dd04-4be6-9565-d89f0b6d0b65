import { Component, OnInit } from '@angular/core';
import { AuthService, User } from '../../core/services/auth.service';
import { ApiService } from '../../core/services/api.service';
import { Observable } from 'rxjs';

interface DashboardStats {
  users: {
    total: number;
    active: number;
    new_today: number;
  };
  products: {
    total: number;
    published: number;
    out_of_stock: number;
    low_stock: number;
  };
  orders: {
    total: number;
    pending: number;
    processing: number;
    delivered: number;
    total_revenue: number;
    orders_today: number;
    revenue_today: number;
  };
  categories: {
    total: number;
    active: number;
    featured: number;
  };
}

interface RecentActivity {
  id: number;
  description: string;
  subject_type: string;
  subject_id: number;
  causer: {
    id: number;
    name: string;
  };
  created_at: string;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  currentUser$: Observable<User | null>;
  stats: DashboardStats | null = null;
  recentActivities: RecentActivity[] = [];
  loading = true;
  error: string | null = null;

  // Chart data
  salesChartData: any[] = [];
  ordersChartData: any[] = [];
  
  // Chart options
  chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  constructor(
    private authService: AuthService,
    private apiService: ApiService
  ) {
    this.currentUser$ = this.authService.currentUser$;
  }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    this.loading = true;
    this.error = null;

    // Load dashboard statistics
    Promise.all([
      this.loadStats(),
      this.loadRecentActivities(),
      this.loadChartData()
    ]).then(() => {
      this.loading = false;
    }).catch((error) => {
      this.error = 'Failed to load dashboard data';
      this.loading = false;
      console.error('Dashboard loading error:', error);
    });
  }

  private async loadStats(): Promise<void> {
    try {
      // Load user statistics
      const userStats = await this.apiService.get('/users/statistics').toPromise();
      
      // Load product statistics
      const productStats = await this.apiService.get('/products/statistics').toPromise();
      
      // Load order statistics
      const orderStats = await this.apiService.get('/orders/statistics').toPromise();
      
      // Load category statistics
      const categoryStats = await this.apiService.get('/categories/statistics').toPromise();

      this.stats = {
        users: userStats.data,
        products: productStats.data,
        orders: orderStats.data,
        categories: categoryStats.data
      };
    } catch (error) {
      console.error('Error loading stats:', error);
      throw error;
    }
  }

  private async loadRecentActivities(): Promise<void> {
    try {
      const response = await this.apiService.get('/activity-log?per_page=10').toPromise();
      this.recentActivities = response.data;
    } catch (error) {
      console.error('Error loading recent activities:', error);
      this.recentActivities = [];
    }
  }

  private async loadChartData(): Promise<void> {
    try {
      // Load sales data for the last 7 days
      const salesResponse = await this.apiService.get('/analytics/sales-chart?days=7').toPromise();
      this.salesChartData = [{
        data: salesResponse.data.values,
        label: 'Sales',
        backgroundColor: 'rgba(102, 126, 234, 0.2)',
        borderColor: 'rgba(102, 126, 234, 1)',
        borderWidth: 2,
        fill: true
      }];

      // Load orders data for the last 7 days
      const ordersResponse = await this.apiService.get('/analytics/orders-chart?days=7').toPromise();
      this.ordersChartData = [{
        data: ordersResponse.data.values,
        label: 'Orders',
        backgroundColor: 'rgba(118, 75, 162, 0.2)',
        borderColor: 'rgba(118, 75, 162, 1)',
        borderWidth: 2,
        fill: true
      }];
    } catch (error) {
      console.error('Error loading chart data:', error);
      // Set empty chart data
      this.salesChartData = [];
      this.ordersChartData = [];
    }
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 18) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getActivityIcon(subjectType: string): string {
    const iconMap: { [key: string]: string } = {
      'App\\Models\\User': 'person',
      'App\\Models\\Product': 'inventory',
      'App\\Models\\Order': 'shopping_cart',
      'App\\Models\\Category': 'category',
      'App\\Models\\Coupon': 'local_offer',
      'App\\Models\\Media': 'image',
      'default': 'info'
    };

    return iconMap[subjectType] || iconMap['default'];
  }

  getActivityColor(subjectType: string): string {
    const colorMap: { [key: string]: string } = {
      'App\\Models\\User': 'primary',
      'App\\Models\\Product': 'accent',
      'App\\Models\\Order': 'warn',
      'App\\Models\\Category': 'primary',
      'App\\Models\\Coupon': 'accent',
      'App\\Models\\Media': 'primary',
      'default': ''
    };

    return colorMap[subjectType] || colorMap['default'];
  }

  refreshDashboard(): void {
    this.loadDashboardData();
  }

  navigateToModule(module: string): void {
    // Navigation logic will be implemented when routing is set up
    console.log(`Navigate to ${module}`);
  }
}
