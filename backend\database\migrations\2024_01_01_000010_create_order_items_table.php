<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products');
            $table->foreignId('product_variation_id')->nullable()->constrained('product_variations');
            $table->string('product_name'); // Store product name at time of order
            $table->string('product_sku'); // Store SKU at time of order
            $table->string('product_image')->nullable(); // Store image URL at time of order
            $table->decimal('price', 10, 2); // Store price at time of order
            $table->integer('quantity');
            $table->decimal('total', 10, 2); // price * quantity
            $table->json('product_attributes')->nullable(); // Store selected attributes
            $table->timestamps();
            
            // Indexes
            $table->index(['order_id', 'product_id']);
            $table->index('product_id');
            $table->index('product_variation_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
