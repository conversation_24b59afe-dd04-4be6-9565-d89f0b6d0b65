<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WishlistItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'wishlist_id',
        'product_id',
        'product_variation_id',
        'notes',
        'price_when_added'
    ];

    protected $casts = [
        'price_when_added' => 'decimal:2',
    ];

    /**
     * Get the wishlist that owns the item.
     */
    public function wishlist(): BelongsTo
    {
        return $this->belongsTo(Wishlist::class);
    }

    /**
     * Get the product.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the product variation.
     */
    public function variation(): BelongsTo
    {
        return $this->belongsTo(ProductVariation::class, 'product_variation_id');
    }

    /**
     * Get the current price of the product.
     */
    public function getCurrentPriceAttribute(): float
    {
        if ($this->variation) {
            return $this->variation->current_price;
        }
        
        return $this->product->current_price;
    }

    /**
     * Get the price change since added.
     */
    public function getPriceChangeAttribute(): float
    {
        return $this->current_price - $this->price_when_added;
    }

    /**
     * Get the price change percentage.
     */
    public function getPriceChangePercentageAttribute(): float
    {
        if ($this->price_when_added <= 0) {
            return 0;
        }
        
        return (($this->current_price - $this->price_when_added) / $this->price_when_added) * 100;
    }

    /**
     * Check if price has dropped.
     */
    public function getIsPriceDropAttribute(): bool
    {
        return $this->current_price < $this->price_when_added;
    }

    /**
     * Check if product is in stock.
     */
    public function getIsInStockAttribute(): bool
    {
        if ($this->variation) {
            return $this->variation->stock_quantity > 0;
        }
        
        return $this->product->stock_quantity > 0 && $this->product->stock_status === 'in_stock';
    }

    /**
     * Get the display name (product + variation).
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->product->name;
        
        if ($this->variation) {
            $name .= ' - ' . $this->variation->name;
        }
        
        return $name;
    }

    /**
     * Move item to shopping cart.
     */
    public function moveToCart(int $quantity = 1): array
    {
        $user = $this->wishlist->user;
        $cart = $user->cart ?: ShoppingCart::create(['user_id' => $user->id]);
        
        try {
            $cartItem = $cart->addItem(
                $this->product_id,
                $quantity,
                $this->product_variation_id
            );
            
            // Remove from wishlist
            $this->delete();
            
            return [
                'success' => true,
                'message' => 'Item moved to cart successfully',
                'cart_item' => $cartItem
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to move item to cart: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update the price when added to current price.
     */
    public function updatePrice(): void
    {
        $this->update(['price_when_added' => $this->current_price]);
    }

    /**
     * Check if item is available for purchase.
     */
    public function isAvailable(): bool
    {
        if (!$this->product->is_active || $this->product->status !== 'published') {
            return false;
        }
        
        if ($this->variation && !$this->variation->is_active) {
            return false;
        }
        
        return $this->is_in_stock;
    }

    /**
     * Get availability status message.
     */
    public function getAvailabilityStatusAttribute(): string
    {
        if (!$this->product->is_active || $this->product->status !== 'published') {
            return 'Product no longer available';
        }
        
        if ($this->variation && !$this->variation->is_active) {
            return 'Variation no longer available';
        }
        
        if (!$this->is_in_stock) {
            return 'Out of stock';
        }
        
        return 'Available';
    }

    /**
     * Scope for available items.
     */
    public function scopeAvailable($query)
    {
        return $query->whereHas('product', function ($q) {
            $q->where('status', 'published')
              ->where('stock_status', 'in_stock');
        });
    }

    /**
     * Scope for items with price drops.
     */
    public function scopePriceDrops($query)
    {
        return $query->whereRaw('(SELECT current_price FROM products WHERE id = wishlist_items.product_id) < price_when_added');
    }
}
