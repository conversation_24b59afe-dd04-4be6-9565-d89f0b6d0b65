<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Wishlist;
use App\Models\WishlistItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    /**
     * Display a listing of user's wishlists.
     */
    public function index(): JsonResponse
    {
        $wishlists = Auth::user()->wishlists()
            ->withCount('items')
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Wishlists retrieved successfully',
            'data' => $wishlists
        ]);
    }

    /**
     * Store a newly created wishlist.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'boolean',
            'is_default' => 'boolean'
        ]);

        $validated['user_id'] = Auth::id();

        $wishlist = Wishlist::create($validated);

        if ($validated['is_default'] ?? false) {
            $wishlist->setAsDefault();
        }

        return response()->json([
            'success' => true,
            'message' => 'Wishlist created successfully',
            'data' => $wishlist->fresh()->loadCount('items')
        ], 201);
    }

    /**
     * Display the specified wishlist.
     */
    public function show(Wishlist $wishlist): JsonResponse
    {
        $this->authorize('view', $wishlist);

        $wishlist->load(['items.product.images', 'items.variation']);

        return response()->json([
            'success' => true,
            'message' => 'Wishlist retrieved successfully',
            'data' => [
                'wishlist' => $wishlist,
                'items' => $wishlist->items,
                'total_value' => $wishlist->total_value,
                'price_changes' => $wishlist->getItemsWithPriceChanges()
            ]
        ]);
    }

    /**
     * Update the specified wishlist.
     */
    public function update(Request $request, Wishlist $wishlist): JsonResponse
    {
        $this->authorize('update', $wishlist);

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'boolean',
            'is_default' => 'boolean'
        ]);

        $wishlist->update($validated);

        if ($validated['is_default'] ?? false) {
            $wishlist->setAsDefault();
        }

        return response()->json([
            'success' => true,
            'message' => 'Wishlist updated successfully',
            'data' => $wishlist->fresh()->loadCount('items')
        ]);
    }

    /**
     * Remove the specified wishlist.
     */
    public function destroy(Wishlist $wishlist): JsonResponse
    {
        $this->authorize('delete', $wishlist);

        $wishlist->delete();

        return response()->json([
            'success' => true,
            'message' => 'Wishlist deleted successfully'
        ]);
    }

    /**
     * Add item to wishlist.
     */
    public function addItem(Request $request, Wishlist $wishlist): JsonResponse
    {
        $this->authorize('update', $wishlist);

        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'product_variation_id' => 'nullable|exists:product_variations,id',
            'notes' => 'nullable|string|max:500'
        ]);

        // Check if item already exists
        if ($wishlist->hasProduct($validated['product_id'], $validated['product_variation_id'])) {
            return response()->json([
                'success' => false,
                'message' => 'Item already exists in wishlist'
            ], 422);
        }

        $item = $wishlist->addProduct(
            $validated['product_id'],
            $validated['product_variation_id'] ?? null,
            $validated['notes'] ?? null
        );

        return response()->json([
            'success' => true,
            'message' => 'Item added to wishlist successfully',
            'data' => $item->load(['product', 'variation'])
        ], 201);
    }

    /**
     * Remove item from wishlist.
     */
    public function removeItem(Wishlist $wishlist, WishlistItem $item): JsonResponse
    {
        $this->authorize('update', $wishlist);

        if ($item->wishlist_id !== $wishlist->id) {
            return response()->json([
                'success' => false,
                'message' => 'Item not found in this wishlist'
            ], 404);
        }

        $item->delete();

        return response()->json([
            'success' => true,
            'message' => 'Item removed from wishlist successfully'
        ]);
    }

    /**
     * Move item to cart.
     */
    public function moveToCart(Request $request, Wishlist $wishlist, WishlistItem $item): JsonResponse
    {
        $this->authorize('update', $wishlist);

        if ($item->wishlist_id !== $wishlist->id) {
            return response()->json([
                'success' => false,
                'message' => 'Item not found in this wishlist'
            ], 404);
        }

        $validated = $request->validate([
            'quantity' => 'integer|min:1|max:100'
        ]);

        $result = $item->moveToCart($validated['quantity'] ?? 1);

        return response()->json($result, $result['success'] ? 200 : 422);
    }

    /**
     * Move all items to cart.
     */
    public function moveAllToCart(Wishlist $wishlist): JsonResponse
    {
        $this->authorize('update', $wishlist);

        $results = $wishlist->moveAllToCart();
        
        $successCount = collect($results)->where('success', true)->count();
        $totalCount = count($results);

        return response()->json([
            'success' => $successCount > 0,
            'message' => "{$successCount} of {$totalCount} items moved to cart successfully",
            'data' => [
                'moved_count' => $successCount,
                'total_count' => $totalCount,
                'results' => $results
            ]
        ]);
    }

    /**
     * Clear all items from wishlist.
     */
    public function clearItems(Wishlist $wishlist): JsonResponse
    {
        $this->authorize('update', $wishlist);

        $itemCount = $wishlist->items()->count();
        $wishlist->clearItems();

        return response()->json([
            'success' => true,
            'message' => "{$itemCount} items removed from wishlist successfully"
        ]);
    }

    /**
     * Set wishlist as default.
     */
    public function setDefault(Wishlist $wishlist): JsonResponse
    {
        $this->authorize('update', $wishlist);

        $wishlist->setAsDefault();

        return response()->json([
            'success' => true,
            'message' => 'Wishlist set as default successfully',
            'data' => $wishlist->fresh()
        ]);
    }

    /**
     * Get default wishlist.
     */
    public function getDefault(): JsonResponse
    {
        $wishlist = Auth::user()->wishlists()
            ->where('is_default', true)
            ->with(['items.product.images', 'items.variation'])
            ->first();

        if (!$wishlist) {
            // Create default wishlist if none exists
            $wishlist = Auth::user()->wishlists()->create([
                'name' => 'My Wishlist',
                'is_default' => true
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Default wishlist retrieved successfully',
            'data' => $wishlist
        ]);
    }

    /**
     * Share wishlist.
     */
    public function share(Wishlist $wishlist): JsonResponse
    {
        $this->authorize('view', $wishlist);

        if (!$wishlist->is_public) {
            return response()->json([
                'success' => false,
                'message' => 'Wishlist must be public to share'
            ], 422);
        }

        $shareUrl = route('wishlists.public', $wishlist->id);

        return response()->json([
            'success' => true,
            'message' => 'Wishlist share URL generated successfully',
            'data' => [
                'share_url' => $shareUrl,
                'wishlist' => $wishlist
            ]
        ]);
    }

    /**
     * Get public wishlist.
     */
    public function getPublic(Wishlist $wishlist): JsonResponse
    {
        if (!$wishlist->is_public) {
            return response()->json([
                'success' => false,
                'message' => 'Wishlist is private'
            ], 403);
        }

        $wishlist->load(['items.product.images', 'items.variation', 'user:id,name']);

        return response()->json([
            'success' => true,
            'message' => 'Public wishlist retrieved successfully',
            'data' => $wishlist
        ]);
    }

    /**
     * Get wishlist statistics.
     */
    public function getStatistics(): JsonResponse
    {
        $user = Auth::user();
        
        $stats = [
            'total_wishlists' => $user->wishlists()->count(),
            'total_items' => $user->wishlists()->withCount('items')->get()->sum('items_count'),
            'total_value' => $user->wishlists()->get()->sum('total_value'),
            'public_wishlists' => $user->wishlists()->where('is_public', true)->count(),
            'items_with_price_drops' => WishlistItem::whereHas('wishlist', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->priceDrops()->count(),
            'most_popular_categories' => $this->getMostPopularCategories($user),
        ];

        return response()->json([
            'success' => true,
            'message' => 'Wishlist statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Get most popular categories in user's wishlists.
     */
    private function getMostPopularCategories($user): array
    {
        return WishlistItem::whereHas('wishlist', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })
        ->with('product.category')
        ->get()
        ->groupBy('product.category.name')
        ->map->count()
        ->sortDesc()
        ->take(5)
        ->toArray();
    }
}
