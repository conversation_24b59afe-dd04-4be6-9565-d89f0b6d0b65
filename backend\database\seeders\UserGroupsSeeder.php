<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UserGroup;
use App\Models\User;

class UserGroupsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the super admin user to set as creator
        $superAdmin = User::where('email', '<EMAIL>')->first();

        // Create default user groups
        $groups = [
            [
                'name' => 'VIP Customers',
                'description' => 'High-value customers with special privileges and discounts',
                'color' => '#FFD700',
                'discount_percentage' => 15.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Premium Members',
                'description' => 'Premium membership customers with enhanced benefits',
                'color' => '#9C27B0',
                'discount_percentage' => 10.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Regular Customers',
                'description' => 'Standard customers with basic benefits',
                'color' => '#2196F3',
                'discount_percentage' => 5.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Wholesale Buyers',
                'description' => 'Bulk purchase customers with wholesale pricing',
                'color' => '#4CAF50',
                'discount_percentage' => 25.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Corporate Clients',
                'description' => 'Business customers with corporate accounts',
                'color' => '#FF5722',
                'discount_percentage' => 20.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Student Discount',
                'description' => 'Students with valid ID for educational discounts',
                'color' => '#00BCD4',
                'discount_percentage' => 12.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Senior Citizens',
                'description' => 'Senior citizens with age-based discounts',
                'color' => '#795548',
                'discount_percentage' => 8.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Loyalty Program',
                'description' => 'Long-term customers in the loyalty program',
                'color' => '#E91E63',
                'discount_percentage' => 7.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'New Customers',
                'description' => 'First-time customers with welcome offers',
                'color' => '#8BC34A',
                'discount_percentage' => 3.00,
                'is_active' => true,
                'created_by' => $superAdmin->id,
            ],
            [
                'name' => 'Inactive Group',
                'description' => 'Example of an inactive group',
                'color' => '#9E9E9E',
                'discount_percentage' => 0.00,
                'is_active' => false,
                'created_by' => $superAdmin->id,
            ],
        ];

        foreach ($groups as $groupData) {
            UserGroup::create($groupData);
        }

        // Assign some customers to groups
        $customers = User::role('customer')->get();
        $createdGroups = UserGroup::all();

        if ($customers->count() > 0 && $createdGroups->count() > 0) {
            // Assign customers to random groups
            foreach ($customers as $customer) {
                // Randomly assign 0-3 groups to each customer
                $numberOfGroups = rand(0, 3);
                $randomGroups = $createdGroups->where('is_active', true)->random(min($numberOfGroups, $createdGroups->where('is_active', true)->count()));
                
                foreach ($randomGroups as $group) {
                    $group->addUser($customer);
                }
            }

            // Ensure some specific assignments for demo purposes
            if ($customers->count() >= 3) {
                $vipGroup = UserGroup::where('name', 'VIP Customers')->first();
                $premiumGroup = UserGroup::where('name', 'Premium Members')->first();
                $regularGroup = UserGroup::where('name', 'Regular Customers')->first();

                // Assign first customer to VIP
                if ($vipGroup) {
                    $vipGroup->addUser($customers->first());
                }

                // Assign second customer to Premium
                if ($premiumGroup && $customers->count() > 1) {
                    $premiumGroup->addUser($customers->skip(1)->first());
                }

                // Assign third customer to Regular
                if ($regularGroup && $customers->count() > 2) {
                    $regularGroup->addUser($customers->skip(2)->first());
                }
            }
        }

        $this->command->info('User groups seeded successfully!');
        $this->command->info('Created ' . count($groups) . ' user groups');
        
        if ($customers->count() > 0) {
            $this->command->info('Assigned customers to groups randomly');
        }
    }
}
