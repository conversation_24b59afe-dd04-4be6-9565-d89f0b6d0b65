<?php

namespace App\Http\Controllers\Api;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class CategoryController extends BaseController
{
    /**
     * Display a listing of categories.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $parentId = $request->get('parent_id');
        $activeOnly = $request->boolean('active_only', false);
        $tree = $request->boolean('tree', false);

        if ($tree) {
            // Return hierarchical tree structure
            $categories = Category::getTree($parentId, $activeOnly);
            return $this->sendResponse($categories);
        }

        $query = Category::with(['parent:id,name', 'creator:id,name'])
            ->withCount('products');

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%");
            });
        }

        // Filter by parent
        if ($parentId !== null) {
            if ($parentId === '0' || $parentId === 'null') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $parentId);
            }
        }

        // Filter active only
        if ($activeOnly) {
            $query->active();
        }

        // Sorting
        $query->orderBy('sort_order')->orderBy('name');

        $categories = $query->paginate($perPage);

        // Transform category data
        $categories->getCollection()->transform(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'parent' => $category->parent ? [
                    'id' => $category->parent->id,
                    'name' => $category->parent->name,
                ] : null,
                'image_url' => $category->image_url,
                'icon' => $category->icon,
                'is_active' => $category->is_active,
                'is_featured' => $category->is_featured,
                'products_count' => $category->products_count,
                'level' => $category->level,
                'has_children' => $category->has_children,
                'sort_order' => $category->sort_order,
                'creator' => $category->creator ? $category->creator->name : null,
                'created_at' => $category->created_at,
                'updated_at' => $category->updated_at,
            ];
        });

        return $this->sendPaginatedResponse($categories);
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:categories',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|integer|exists:categories,id',
            'image' => 'nullable|string|max:500',
            'icon' => 'nullable|string|max:50',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        // Validate parent category (prevent circular reference)
        if ($request->parent_id) {
            $parent = Category::find($request->parent_id);
            if (!$parent) {
                return $this->sendError('Parent category not found');
            }
        }

        try {
            $categoryData = $request->only([
                'name', 'slug', 'description', 'parent_id', 'image', 'icon',
                'meta_title', 'meta_description', 'meta_keywords',
                'is_active', 'is_featured', 'sort_order'
            ]);

            $categoryData['created_by'] = auth()->id();

            // Generate slug if not provided
            if (empty($categoryData['slug'])) {
                $categoryData['slug'] = Category::generateUniqueSlug($categoryData['name']);
            }

            $category = Category::create($categoryData);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($category)
                ->log('Category created');

            return $this->sendCreated([
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'parent_id' => $category->parent_id,
                'is_active' => $category->is_active,
                'is_featured' => $category->is_featured,
                'sort_order' => $category->sort_order,
            ], 'Category created successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to create category: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified category.
     */
    public function show(string $id): JsonResponse
    {
        $category = Category::with(['parent', 'children', 'creator'])
            ->withCount(['products', 'children'])
            ->find($id);

        if (!$category) {
            return $this->sendNotFound('Category not found');
        }

        return $this->sendResponse([
            'id' => $category->id,
            'name' => $category->name,
            'slug' => $category->slug,
            'description' => $category->description,
            'parent' => $category->parent ? [
                'id' => $category->parent->id,
                'name' => $category->parent->name,
                'slug' => $category->parent->slug,
            ] : null,
            'children' => $category->children->map(function ($child) {
                return [
                    'id' => $child->id,
                    'name' => $child->name,
                    'slug' => $child->slug,
                    'is_active' => $child->is_active,
                    'products_count' => $child->products_count,
                ];
            }),
            'image_url' => $category->image_url,
            'icon' => $category->icon,
            'meta_title' => $category->meta_title,
            'meta_description' => $category->meta_description,
            'meta_keywords' => $category->meta_keywords,
            'is_active' => $category->is_active,
            'is_featured' => $category->is_featured,
            'sort_order' => $category->sort_order,
            'products_count' => $category->products_count,
            'children_count' => $category->children_count,
            'total_products_count' => $category->total_products_count,
            'level' => $category->level,
            'path' => $category->path,
            'seo_data' => $category->seo_data,
            'creator' => $category->creator ? [
                'id' => $category->creator->id,
                'name' => $category->creator->name,
            ] : null,
            'created_at' => $category->created_at,
            'updated_at' => $category->updated_at,
        ]);
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $category = Category::find($id);

        if (!$category) {
            return $this->sendNotFound('Category not found');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'slug' => 'nullable|string|max:255|unique:categories,slug,' . $id,
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|integer|exists:categories,id',
            'image' => 'nullable|string|max:500',
            'icon' => 'nullable|string|max:50',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        // Validate parent category (prevent circular reference)
        if ($request->has('parent_id') && $request->parent_id) {
            if ($request->parent_id == $id) {
                return $this->sendError('Category cannot be its own parent');
            }

            $parent = Category::find($request->parent_id);
            if (!$parent) {
                return $this->sendError('Parent category not found');
            }

            // Check if the new parent is a descendant of this category
            $descendantIds = $category->getAllDescendantIds();
            if (in_array($request->parent_id, $descendantIds)) {
                return $this->sendError('Cannot set a descendant category as parent');
            }
        }

        try {
            $updateData = $request->only([
                'name', 'slug', 'description', 'parent_id', 'image', 'icon',
                'meta_title', 'meta_description', 'meta_keywords',
                'is_active', 'is_featured', 'sort_order'
            ]);

            // Generate slug if name changed and slug not provided
            if ($request->has('name') && !$request->has('slug')) {
                $updateData['slug'] = Category::generateUniqueSlug($request->name, $id);
            }

            $category->update($updateData);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($category)
                ->log('Category updated');

            return $this->sendUpdated([
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'parent_id' => $category->parent_id,
                'is_active' => $category->is_active,
                'is_featured' => $category->is_featured,
                'sort_order' => $category->sort_order,
            ], 'Category updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update category: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Remove the specified category.
     */
    public function destroy(string $id): JsonResponse
    {
        $category = Category::find($id);

        if (!$category) {
            return $this->sendNotFound('Category not found');
        }

        if (!$category->canBeDeleted()) {
            return $this->sendError('Cannot delete category that has products or subcategories');
        }

        try {
            // Log activity before deletion
            activity()
                ->causedBy(auth()->user())
                ->performedOn($category)
                ->withProperties(['category_name' => $category->name])
                ->log('Category deleted');

            $category->delete();

            return $this->sendDeleted('Category deleted successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to delete category: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get products in a category.
     */
    public function products(Request $request, string $id): JsonResponse
    {
        $category = Category::find($id);

        if (!$category) {
            return $this->sendNotFound('Category not found');
        }

        $perPage = $request->get('per_page', 20);
        $includeSubcategories = $request->boolean('include_subcategories', false);

        $query = $category->products()->with(['category:id,name', 'images']);

        // Include products from subcategories
        if ($includeSubcategories) {
            $categoryIds = $category->getAllDescendantIds();
            $query = \App\Models\Product::whereIn('category_id', $categoryIds)
                ->with(['category:id,name', 'images']);
        }

        $products = $query->published()->paginate($perPage);

        // Transform product data
        $products->getCollection()->transform(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'sku' => $product->sku,
                'price' => $product->price,
                'sale_price' => $product->sale_price,
                'effective_price' => $product->effective_price,
                'is_on_sale' => $product->is_on_sale,
                'main_image_url' => $product->main_image_url,
                'category' => [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                ],
                'is_in_stock' => $product->is_in_stock,
                'featured' => $product->featured,
                'created_at' => $product->created_at,
            ];
        });

        return $this->sendPaginatedResponse($products);
    }

    /**
     * Reorder categories.
     */
    public function reorder(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order' => 'required|array',
            'order.*' => 'integer|exists:categories,id',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            Category::reorder($request->order);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->log('Categories reordered');

            return $this->sendResponse(null, 'Categories reordered successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to reorder categories: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get category statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = Category::getStatistics();
            
            return $this->sendResponse($stats);

        } catch (\Exception $e) {
            return $this->sendError('Failed to get category statistics: ' . $e->getMessage(), [], 500);
        }
    }
}
