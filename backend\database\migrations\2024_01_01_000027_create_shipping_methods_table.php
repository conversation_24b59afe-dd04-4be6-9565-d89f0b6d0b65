<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['flat_rate', 'weight_based', 'price_based', 'free', 'pickup']);
            $table->decimal('base_cost', 10, 2)->default(0);
            $table->decimal('cost_per_kg', 10, 2)->nullable();
            $table->decimal('cost_per_item', 10, 2)->nullable();
            $table->decimal('free_shipping_threshold', 10, 2)->nullable();
            $table->integer('min_delivery_days')->nullable();
            $table->integer('max_delivery_days')->nullable();
            $table->json('available_countries')->nullable(); // Countries where this method is available
            $table->json('restricted_countries')->nullable(); // Countries where this method is not available
            $table->decimal('max_weight', 8, 2)->nullable();
            $table->decimal('max_dimensions', 8, 2)->nullable(); // JSON: {length, width, height}
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_signature')->default(false);
            $table->boolean('is_trackable')->default(false);
            $table->string('tracking_url')->nullable();
            $table->integer('sort_order')->default(0);
            $table->json('settings')->nullable(); // Additional settings
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_methods');
    }
};
