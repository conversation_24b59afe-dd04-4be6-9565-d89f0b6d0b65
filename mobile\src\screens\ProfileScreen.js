import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { launchImageLibrary } from 'react-native-image-picker';

import { AuthContext } from '../context/AuthContext';
import UserService from '../services/UserService';
import LoadingSpinner from '../components/LoadingSpinner';
import { colors, spacing, typography } from '../theme';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const { user, logout, updateUser } = useContext(AuthContext);

  // State
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await UserService.getProfile();
      
      if (response.success) {
        setProfile(response.data);
        updateUser(response.data);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      Alert.alert('Error', 'Failed to load profile. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadProfile();
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile', { profile });
  };

  const handleChangePassword = () => {
    navigation.navigate('ChangePassword');
  };

  const handleManageAddresses = () => {
    navigation.navigate('ManageAddresses');
  };

  const handleNotificationSettings = () => {
    navigation.navigate('NotificationSettings');
  };

  const handlePrivacySettings = () => {
    navigation.navigate('PrivacySettings');
  };

  const handleAppSettings = () => {
    navigation.navigate('AppSettings');
  };

  const handleActivityLog = () => {
    navigation.navigate('ActivityLog');
  };

  const handleHelpSupport = () => {
    navigation.navigate('HelpSupport');
  };

  const handleAbout = () => {
    navigation.navigate('About');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => logout()
        }
      ]
    );
  };

  const handleChangeProfileImage = () => {
    Alert.alert(
      'Change Profile Picture',
      'Choose an option',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Choose from Gallery', onPress: selectImageFromGallery },
        { text: 'Remove Photo', onPress: removeProfileImage, style: 'destructive' }
      ]
    );
  };

  const selectImageFromGallery = () => {
    const options = {
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 800,
      maxHeight: 800,
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel || response.error) {
        return;
      }

      if (response.assets && response.assets[0]) {
        uploadProfileImage(response.assets[0].uri);
      }
    });
  };

  const uploadProfileImage = async (imageUri) => {
    try {
      setUploadingImage(true);
      const response = await UserService.uploadProfileImage(imageUri);
      
      if (response.success) {
        setProfile(prev => ({ ...prev, profile_image_url: response.data.url }));
        updateUser({ ...profile, profile_image_url: response.data.url });
        Alert.alert('Success', 'Profile picture updated successfully');
      }
    } catch (error) {
      console.error('Error uploading profile image:', error);
      Alert.alert('Error', 'Failed to upload profile picture. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  const removeProfileImage = async () => {
    try {
      setUploadingImage(true);
      const response = await UserService.deleteProfileImage();
      
      if (response.success) {
        setProfile(prev => ({ ...prev, profile_image_url: null }));
        updateUser({ ...profile, profile_image_url: null });
        Alert.alert('Success', 'Profile picture removed successfully');
      }
    } catch (error) {
      console.error('Error removing profile image:', error);
      Alert.alert('Error', 'Failed to remove profile picture. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  const renderProfileHeader = () => (
    <View style={styles.profileHeader}>
      <TouchableOpacity
        style={styles.avatarContainer}
        onPress={handleChangeProfileImage}
        disabled={uploadingImage}
      >
        {profile?.profile_image_url ? (
          <Image
            source={{ uri: profile.profile_image_url }}
            style={styles.avatar}
          />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>
              {UserService.getUserInitials(profile || user)}
            </Text>
          </View>
        )}
        
        <View style={styles.cameraIcon}>
          {uploadingImage ? (
            <ActivityIndicator size="small" color={colors.background.primary} />
          ) : (
            <Icon name="camera-alt" size={16} color={colors.background.primary} />
          )}
        </View>
      </TouchableOpacity>

      <Text style={styles.userName}>
        {UserService.formatDisplayName(profile || user)}
      </Text>
      <Text style={styles.userEmail}>{profile?.email || user?.email}</Text>
      
      <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
        <Icon name="edit" size={16} color={colors.primary} />
        <Text style={styles.editButtonText}>Edit Profile</Text>
      </TouchableOpacity>
    </View>
  );

  const renderMenuSection = (title, items) => (
    <View style={styles.menuSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {items.map((item, index) => (
        <TouchableOpacity
          key={index}
          style={styles.menuItem}
          onPress={item.onPress}
        >
          <View style={styles.menuItemLeft}>
            <Icon name={item.icon} size={24} color={item.color || colors.text.secondary} />
            <Text style={styles.menuItemText}>{item.title}</Text>
          </View>
          <Icon name="chevron-right" size={24} color={colors.text.secondary} />
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  const accountMenuItems = [
    {
      title: 'Change Password',
      icon: 'lock',
      onPress: handleChangePassword
    },
    {
      title: 'Manage Addresses',
      icon: 'location-on',
      onPress: handleManageAddresses
    },
    {
      title: 'Activity Log',
      icon: 'history',
      onPress: handleActivityLog
    }
  ];

  const settingsMenuItems = [
    {
      title: 'Notifications',
      icon: 'notifications',
      onPress: handleNotificationSettings
    },
    {
      title: 'Privacy & Security',
      icon: 'security',
      onPress: handlePrivacySettings
    },
    {
      title: 'App Settings',
      icon: 'settings',
      onPress: handleAppSettings
    }
  ];

  const supportMenuItems = [
    {
      title: 'Help & Support',
      icon: 'help',
      onPress: handleHelpSupport
    },
    {
      title: 'About',
      icon: 'info',
      onPress: handleAbout
    },
    {
      title: 'Logout',
      icon: 'logout',
      color: colors.error,
      onPress: handleLogout
    }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Header */}
        {renderProfileHeader()}

        {/* Account Section */}
        {renderMenuSection('Account', accountMenuItems)}

        {/* Settings Section */}
        {renderMenuSection('Settings', settingsMenuItems)}

        {/* Support Section */}
        {renderMenuSection('Support', supportMenuItems)}

        {/* App Version */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    alignItems: 'center',
    padding: spacing.xl,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing.md,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: colors.background.primary,
  },
  cameraIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.background.primary,
  },
  userName: {
    fontSize: typography.h3.fontSize,
    fontWeight: typography.h3.fontWeight,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  userEmail: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
    marginBottom: spacing.md,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  editButtonText: {
    fontSize: typography.body.fontSize,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  menuSection: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.h4.fontSize,
    fontWeight: typography.h4.fontWeight,
    color: colors.text.primary,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    marginLeft: spacing.md,
  },
  versionContainer: {
    alignItems: 'center',
    padding: spacing.xl,
  },
  versionText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
  },
});

export default ProfileScreen;
