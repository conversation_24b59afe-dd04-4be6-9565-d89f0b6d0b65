<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

class Transaction extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transaction_id',
        'order_id',
        'user_id',
        'type',
        'status',
        'payment_method',
        'gateway',
        'gateway_transaction_id',
        'amount',
        'currency',
        'fee',
        'net_amount',
        'reference',
        'description',
        'gateway_response',
        'failure_reason',
        'processed_at',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'fee' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'gateway_response' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Transaction type constants.
     */
    const TYPE_PAYMENT = 'payment';
    const TYPE_REFUND = 'refund';
    const TYPE_PARTIAL_REFUND = 'partial_refund';
    const TYPE_CHARGEBACK = 'chargeback';
    const TYPE_ADJUSTMENT = 'adjustment';

    /**
     * Transaction status constants.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_PARTIALLY_REFUNDED = 'partially_refunded';

    /**
     * Payment method constants.
     */
    const METHOD_COD = 'cod';
    const METHOD_RAZORPAY = 'razorpay';
    const METHOD_GPAY = 'gpay';
    const METHOD_BANK_TRANSFER = 'bank_transfer';
    const METHOD_WALLET = 'wallet';
    const METHOD_UPI = 'upi';

    /**
     * Gateway constants.
     */
    const GATEWAY_RAZORPAY = 'razorpay';
    const GATEWAY_STRIPE = 'stripe';
    const GATEWAY_PAYPAL = 'paypal';
    const GATEWAY_MANUAL = 'manual';

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['transaction_id', 'type', 'status', 'amount', 'payment_method'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_id)) {
                $transaction->transaction_id = static::generateTransactionId();
            }
            
            if (empty($transaction->currency)) {
                $transaction->currency = config('app.currency', 'USD');
            }

            // Calculate net amount if not provided
            if (is_null($transaction->net_amount)) {
                $transaction->net_amount = $transaction->amount - ($transaction->fee ?? 0);
            }
        });
    }

    /**
     * Get the order associated with this transaction.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user associated with this transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created this transaction.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get transaction status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_PROCESSING => 'info',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_CANCELLED => 'secondary',
            self::STATUS_REFUNDED => 'secondary',
            self::STATUS_PARTIALLY_REFUNDED => 'info',
            default => 'secondary',
        };
    }

    /**
     * Get transaction type badge color.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            self::TYPE_PAYMENT => 'success',
            self::TYPE_REFUND => 'warning',
            self::TYPE_PARTIAL_REFUND => 'info',
            self::TYPE_CHARGEBACK => 'danger',
            self::TYPE_ADJUSTMENT => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * Check if transaction is successful.
     */
    public function getIsSuccessfulAttribute(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if transaction is pending.
     */
    public function getIsPendingAttribute(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PROCESSING]);
    }

    /**
     * Check if transaction has failed.
     */
    public function getIsFailedAttribute(): bool
    {
        return in_array($this->status, [self::STATUS_FAILED, self::STATUS_CANCELLED]);
    }

    /**
     * Check if transaction can be refunded.
     */
    public function getCanBeRefundedAttribute(): bool
    {
        return $this->type === self::TYPE_PAYMENT && 
               $this->status === self::STATUS_COMPLETED &&
               $this->amount > 0;
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted fee.
     */
    public function getFormattedFeeAttribute(): string
    {
        return number_format($this->fee ?? 0, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted net amount.
     */
    public function getFormattedNetAmountAttribute(): string
    {
        return number_format($this->net_amount, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Update transaction status.
     */
    public function updateStatus(string $status, ?string $reason = null, ?array $gatewayResponse = null): void
    {
        $oldStatus = $this->status;
        $this->status = $status;

        if ($status === self::STATUS_COMPLETED && !$this->processed_at) {
            $this->processed_at = now();
        }

        if ($reason) {
            $this->failure_reason = $reason;
        }

        if ($gatewayResponse) {
            $this->gateway_response = array_merge($this->gateway_response ?? [], $gatewayResponse);
        }

        $this->save();

        // Update related order payment status
        if ($this->order) {
            $this->updateOrderPaymentStatus();
        }

        // Log status change
        activity()
            ->causedBy(auth()->user())
            ->performedOn($this)
            ->withProperties([
                'old_status' => $oldStatus,
                'new_status' => $status,
                'reason' => $reason,
            ])
            ->log('Transaction status updated');
    }

    /**
     * Update related order payment status based on transaction status.
     */
    private function updateOrderPaymentStatus(): void
    {
        $order = $this->order;
        
        switch ($this->status) {
            case self::STATUS_COMPLETED:
                if ($this->type === self::TYPE_PAYMENT) {
                    $order->payment_status = Order::PAYMENT_PAID;
                } elseif ($this->type === self::TYPE_REFUND) {
                    $order->payment_status = Order::PAYMENT_REFUNDED;
                } elseif ($this->type === self::TYPE_PARTIAL_REFUND) {
                    $order->payment_status = Order::PAYMENT_PARTIALLY_REFUNDED;
                }
                break;

            case self::STATUS_FAILED:
            case self::STATUS_CANCELLED:
                if ($this->type === self::TYPE_PAYMENT) {
                    $order->payment_status = Order::PAYMENT_FAILED;
                }
                break;

            case self::STATUS_PENDING:
            case self::STATUS_PROCESSING:
                if ($this->type === self::TYPE_PAYMENT) {
                    $order->payment_status = Order::PAYMENT_PENDING;
                }
                break;
        }

        $order->save();
    }

    /**
     * Create a refund transaction.
     */
    public function createRefund(float $amount, ?string $reason = null): Transaction
    {
        if (!$this->can_be_refunded) {
            throw new \Exception('This transaction cannot be refunded');
        }

        if ($amount > $this->amount) {
            throw new \Exception('Refund amount cannot exceed original transaction amount');
        }

        $refundType = $amount < $this->amount ? self::TYPE_PARTIAL_REFUND : self::TYPE_REFUND;

        return static::create([
            'order_id' => $this->order_id,
            'user_id' => $this->user_id,
            'type' => $refundType,
            'status' => self::STATUS_PENDING,
            'payment_method' => $this->payment_method,
            'gateway' => $this->gateway,
            'amount' => $amount,
            'currency' => $this->currency,
            'reference' => $this->transaction_id,
            'description' => $reason ?? "Refund for transaction {$this->transaction_id}",
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * Scope for transactions by type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for transactions by status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for successful transactions.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for failed transactions.
     */
    public function scopeFailed($query)
    {
        return $query->whereIn('status', [self::STATUS_FAILED, self::STATUS_CANCELLED]);
    }

    /**
     * Scope for pending transactions.
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', [self::STATUS_PENDING, self::STATUS_PROCESSING]);
    }

    /**
     * Scope for transactions by payment method.
     */
    public function scopePaymentMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope for transactions by gateway.
     */
    public function scopeGateway($query, string $gateway)
    {
        return $query->where('gateway', $gateway);
    }

    /**
     * Scope for transactions within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for transactions with amount range.
     */
    public function scopeAmountRange($query, $minAmount, $maxAmount)
    {
        return $query->whereBetween('amount', [$minAmount, $maxAmount]);
    }

    /**
     * Generate unique transaction ID.
     */
    public static function generateTransactionId(): string
    {
        do {
            $transactionId = 'TXN-' . date('Y') . '-' . strtoupper(Str::random(10));
        } while (static::where('transaction_id', $transactionId)->exists());

        return $transactionId;
    }

    /**
     * Get transaction statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_transactions' => static::count(),
            'successful_transactions' => static::successful()->count(),
            'failed_transactions' => static::failed()->count(),
            'pending_transactions' => static::pending()->count(),
            'total_amount' => static::successful()->sum('amount'),
            'total_fees' => static::successful()->sum('fee'),
            'total_net_amount' => static::successful()->sum('net_amount'),
            'refund_amount' => static::successful()->whereIn('type', [self::TYPE_REFUND, self::TYPE_PARTIAL_REFUND])->sum('amount'),
            'transactions_today' => static::whereDate('created_at', today())->count(),
            'amount_today' => static::successful()->whereDate('created_at', today())->sum('amount'),
            'payment_methods' => static::successful()
                ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
                ->groupBy('payment_method')
                ->get()
                ->keyBy('payment_method'),
        ];
    }
}
