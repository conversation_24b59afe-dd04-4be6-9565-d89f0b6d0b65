import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Switch
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

import { useOffline } from '../context/OfflineContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { colors, spacing, typography } from '../theme';
import { formatBytes } from '../utils/helpers';

const OfflineSettingsScreen = () => {
  const navigation = useNavigation();
  const {
    isOnline,
    pendingRequests,
    syncInProgress,
    clearOfflineData,
    getStorageStats,
    preloadEssentialData,
    syncOfflineData,
    showOfflineStatus
  } = useOffline();

  // State
  const [loading, setLoading] = useState(true);
  const [storageStats, setStorageStats] = useState(null);
  const [settings, setSettings] = useState({
    autoSync: true,
    preloadData: true,
    offlineMode: true,
    syncOnWifi: false,
    compressData: true
  });

  useEffect(() => {
    loadStorageStats();
    loadSettings();
  }, []);

  const loadStorageStats = async () => {
    try {
      const stats = await getStorageStats();
      setStorageStats(stats);
    } catch (error) {
      console.error('Error loading storage stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSettings = async () => {
    // Load offline settings from storage
    // This would integrate with your settings service
    try {
      // For now, using default settings
      setSettings({
        autoSync: true,
        preloadData: true,
        offlineMode: true,
        syncOnWifi: false,
        compressData: true
      });
    } catch (error) {
      console.error('Error loading offline settings:', error);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      setSettings(newSettings);
      // Save to storage
      // await OfflineService.saveSettings(newSettings);
    } catch (error) {
      console.error('Error saving offline settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    }
  };

  const handleClearOfflineData = () => {
    Alert.alert(
      'Clear Offline Data',
      'This will remove all cached data and pending changes. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearOfflineData();
              await loadStorageStats();
            } catch (error) {
              Alert.alert('Error', 'Failed to clear offline data');
            }
          }
        }
      ]
    );
  };

  const handlePreloadData = async () => {
    if (!isOnline) {
      Alert.alert('Offline', 'Cannot preload data while offline');
      return;
    }

    try {
      await preloadEssentialData();
      await loadStorageStats();
    } catch (error) {
      Alert.alert('Error', 'Failed to preload data');
    }
  };

  const handleSyncNow = async () => {
    if (!isOnline) {
      Alert.alert('Offline', 'Cannot sync while offline');
      return;
    }

    try {
      await syncOfflineData();
      await loadStorageStats();
    } catch (error) {
      Alert.alert('Error', 'Failed to sync data');
    }
  };

  const renderStatusSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Status</Text>
      
      <TouchableOpacity style={styles.statusItem} onPress={showOfflineStatus}>
        <View style={styles.statusInfo}>
          <View style={styles.statusIndicator}>
            <Icon 
              name={isOnline ? 'wifi' : 'wifi-off'} 
              size={24} 
              color={isOnline ? colors.success : colors.error} 
            />
            <Text style={styles.statusText}>
              {isOnline ? 'Online' : 'Offline'}
            </Text>
          </View>
          {pendingRequests > 0 && (
            <Text style={styles.pendingText}>
              {pendingRequests} pending changes
            </Text>
          )}
        </View>
        <Icon name="chevron-right" size={24} color={colors.text.secondary} />
      </TouchableOpacity>

      {syncInProgress && (
        <View style={styles.syncingIndicator}>
          <Icon name="sync" size={20} color={colors.primary} />
          <Text style={styles.syncingText}>Syncing...</Text>
        </View>
      )}
    </View>
  );

  const renderStorageSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Storage</Text>
      
      {storageStats && (
        <>
          <View style={styles.storageItem}>
            <Text style={styles.storageLabel}>Cached Data</Text>
            <Text style={styles.storageValue}>
              {storageStats.offlineDataCount} items ({formatBytes(storageStats.offlineDataSize)})
            </Text>
          </View>
          
          <View style={styles.storageItem}>
            <Text style={styles.storageLabel}>Pending Requests</Text>
            <Text style={styles.storageValue}>
              {storageStats.pendingRequestsCount} items ({formatBytes(storageStats.pendingRequestsSize)})
            </Text>
          </View>
          
          <View style={styles.storageItem}>
            <Text style={styles.storageLabel}>Total Storage</Text>
            <Text style={[styles.storageValue, styles.totalStorage]}>
              {formatBytes(storageStats.totalSize)}
            </Text>
          </View>
        </>
      )}
    </View>
  );

  const renderActionsSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Actions</Text>
      
      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={handlePreloadData}
        disabled={!isOnline}
      >
        <Icon name="cloud-download" size={24} color={isOnline ? colors.primary : colors.text.disabled} />
        <Text style={[styles.actionText, !isOnline && styles.disabledText]}>
          Preload Essential Data
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={handleSyncNow}
        disabled={!isOnline || pendingRequests === 0}
      >
        <Icon 
          name="sync" 
          size={24} 
          color={isOnline && pendingRequests > 0 ? colors.primary : colors.text.disabled} 
        />
        <Text style={[
          styles.actionText, 
          (!isOnline || pendingRequests === 0) && styles.disabledText
        ]}>
          Sync Now ({pendingRequests} pending)
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={handleClearOfflineData}
      >
        <Icon name="delete" size={24} color={colors.error} />
        <Text style={[styles.actionText, { color: colors.error }]}>
          Clear All Offline Data
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderSettingsSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Settings</Text>
      
      <View style={styles.settingItem}>
        <View style={styles.settingInfo}>
          <Text style={styles.settingTitle}>Auto Sync</Text>
          <Text style={styles.settingDescription}>
            Automatically sync when connection is restored
          </Text>
        </View>
        <Switch
          value={settings.autoSync}
          onValueChange={(value) => saveSettings({ ...settings, autoSync: value })}
          trackColor={{ false: colors.border, true: colors.primary }}
          thumbColor={colors.background.primary}
        />
      </View>
      
      <View style={styles.settingItem}>
        <View style={styles.settingInfo}>
          <Text style={styles.settingTitle}>Preload Data</Text>
          <Text style={styles.settingDescription}>
            Automatically cache essential data for offline use
          </Text>
        </View>
        <Switch
          value={settings.preloadData}
          onValueChange={(value) => saveSettings({ ...settings, preloadData: value })}
          trackColor={{ false: colors.border, true: colors.primary }}
          thumbColor={colors.background.primary}
        />
      </View>
      
      <View style={styles.settingItem}>
        <View style={styles.settingInfo}>
          <Text style={styles.settingTitle}>Sync on WiFi Only</Text>
          <Text style={styles.settingDescription}>
            Only sync when connected to WiFi to save data
          </Text>
        </View>
        <Switch
          value={settings.syncOnWifi}
          onValueChange={(value) => saveSettings({ ...settings, syncOnWifi: value })}
          trackColor={{ false: colors.border, true: colors.primary }}
          thumbColor={colors.background.primary}
        />
      </View>
      
      <View style={styles.settingItem}>
        <View style={styles.settingInfo}>
          <Text style={styles.settingTitle}>Compress Data</Text>
          <Text style={styles.settingDescription}>
            Compress cached data to save storage space
          </Text>
        </View>
        <Switch
          value={settings.compressData}
          onValueChange={(value) => saveSettings({ ...settings, compressData: value })}
          trackColor={{ false: colors.border, true: colors.primary }}
          thumbColor={colors.background.primary}
        />
      </View>
    </View>
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderStatusSection()}
        {renderStorageSection()}
        {renderActionsSection()}
        {renderSettingsSection()}
        
        <View style={styles.infoSection}>
          <Text style={styles.infoText}>
            Offline mode allows you to browse cached content and queue actions when you don't have an internet connection. 
            Changes will be automatically synced when connection is restored.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.h4.fontSize,
    fontWeight: typography.h4.fontWeight,
    color: colors.text.primary,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    marginTop: spacing.md,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  statusInfo: {
    flex: 1,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: typography.body.fontSize,
    fontWeight: '500',
    color: colors.text.primary,
    marginLeft: spacing.sm,
  },
  pendingText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  syncingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background.secondary,
  },
  syncingText: {
    fontSize: typography.caption.fontSize,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  storageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  storageLabel: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
  },
  storageValue: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    fontWeight: '500',
  },
  totalStorage: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  actionText: {
    fontSize: typography.body.fontSize,
    color: colors.primary,
    marginLeft: spacing.md,
    fontWeight: '500',
  },
  disabledText: {
    color: colors.text.disabled,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  settingTitle: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  infoSection: {
    padding: spacing.lg,
    backgroundColor: colors.background.secondary,
    margin: spacing.lg,
    borderRadius: 8,
  },
  infoText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
    lineHeight: 18,
    textAlign: 'center',
  },
});

export default OfflineSettingsScreen;
