import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Share,
  Linking
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';

import OrderService from '../services/OrderService';
import OrderStatusTimeline from '../components/OrderStatusTimeline';
import OrderItemCard from '../components/OrderItemCard';
import LoadingSpinner from '../components/LoadingSpinner';
import { colors, spacing, typography } from '../theme';
import { formatCurrency, formatDate } from '../utils/helpers';

const OrderDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  
  const { orderId, order: initialOrder } = route.params;

  // State
  const [order, setOrder] = useState(initialOrder || null);
  const [loading, setLoading] = useState(!initialOrder);
  const [tracking, setTracking] = useState(null);
  const [invoice, setInvoice] = useState(null);
  const [notes, setNotes] = useState([]);
  const [activeTab, setActiveTab] = useState('details');

  useEffect(() => {
    if (!initialOrder) {
      loadOrder();
    }
    loadOrderTracking();
    loadOrderInvoice();
    loadOrderNotes();
  }, [orderId]);

  useEffect(() => {
    // Setup navigation header
    navigation.setOptions({
      title: order ? OrderService.formatOrderNumber(order.order_number) : 'Order Details',
      headerRight: () => (
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleShare}
          >
            <Icon name="share" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          
          {order && OrderService.canCancelOrder(order) && (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleCancelOrder}
            >
              <Icon name="cancel" size={24} color={colors.error} />
            </TouchableOpacity>
          )}
        </View>
      )
    });
  }, [navigation, order]);

  const loadOrder = async () => {
    try {
      setLoading(true);
      const response = await OrderService.getOrder(orderId);
      
      if (response.success) {
        setOrder(response.data);
      }
    } catch (error) {
      console.error('Error loading order:', error);
      Alert.alert('Error', 'Failed to load order details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadOrderTracking = async () => {
    try {
      const response = await OrderService.getOrderTracking(orderId);
      if (response.success) {
        setTracking(response.data);
      }
    } catch (error) {
      console.error('Error loading order tracking:', error);
    }
  };

  const loadOrderInvoice = async () => {
    try {
      const response = await OrderService.getOrderInvoice(orderId);
      if (response.success) {
        setInvoice(response.data);
      }
    } catch (error) {
      console.error('Error loading order invoice:', error);
    }
  };

  const loadOrderNotes = async () => {
    try {
      const response = await OrderService.getOrderNotes(orderId);
      if (response.success) {
        setNotes(response.data);
      }
    } catch (error) {
      console.error('Error loading order notes:', error);
    }
  };

  const handleCancelOrder = () => {
    Alert.alert(
      'Cancel Order',
      `Are you sure you want to cancel order ${OrderService.formatOrderNumber(order.order_number)}?`,
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              await OrderService.cancelOrder(orderId, 'Cancelled by customer');
              Alert.alert('Success', 'Order cancelled successfully');
              loadOrder();
            } catch (error) {
              console.error('Error cancelling order:', error);
              Alert.alert('Error', 'Failed to cancel order. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Order ${OrderService.formatOrderNumber(order.order_number)} - ${formatCurrency(order.total_amount)}`,
        title: 'Order Details'
      });
    } catch (error) {
      console.error('Error sharing order:', error);
    }
  };

  const handleTrackOrder = () => {
    navigation.navigate('OrderTracking', { orderId, order, tracking });
  };

  const handleDownloadInvoice = async () => {
    try {
      const blob = await OrderService.downloadInvoicePdf(orderId);
      // Handle PDF download/viewing
      Alert.alert('Invoice', 'Invoice download started');
    } catch (error) {
      console.error('Error downloading invoice:', error);
      Alert.alert('Error', 'Failed to download invoice. Please try again.');
    }
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'How would you like to contact support?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Email', onPress: () => Linking.openURL('mailto:<EMAIL>') },
        { text: 'Phone', onPress: () => Linking.openURL('tel:+1234567890') }
      ]
    );
  };

  const handleReorder = async () => {
    try {
      // Add all order items to cart
      for (const item of order.items) {
        await ProductService.addToCart(item.product_id, item.quantity);
      }
      
      Alert.alert(
        'Items Added',
        'All items from this order have been added to your cart.',
        [
          { text: 'Continue Shopping', style: 'cancel' },
          { text: 'View Cart', onPress: () => navigation.navigate('Cart') }
        ]
      );
    } catch (error) {
      console.error('Error reordering items:', error);
      Alert.alert('Error', 'Failed to add items to cart. Please try again.');
    }
  };

  const renderOrderSummary = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Order Summary</Text>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Order Number:</Text>
        <Text style={styles.summaryValue}>{OrderService.formatOrderNumber(order.order_number)}</Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Order Date:</Text>
        <Text style={styles.summaryValue}>{formatDate(order.created_at)}</Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Status:</Text>
        <View style={[styles.statusBadge, { backgroundColor: OrderService.getOrderStatusColor(order.status) }]}>
          <Text style={styles.statusText}>{order.status.toUpperCase()}</Text>
        </View>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Payment Status:</Text>
        <View style={[styles.statusBadge, { backgroundColor: OrderService.getPaymentStatusColor(order.payment_status) }]}>
          <Text style={styles.statusText}>{order.payment_status.toUpperCase()}</Text>
        </View>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Total Amount:</Text>
        <Text style={styles.totalAmount}>{formatCurrency(order.total_amount)}</Text>
      </View>
    </View>
  );

  const renderOrderItems = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Order Items ({order.items.length})</Text>
      
      {order.items.map((item, index) => (
        <OrderItemCard
          key={index}
          item={item}
          onProductPress={(productId) => navigation.navigate('ProductDetail', { productId })}
          style={styles.orderItem}
        />
      ))}
    </View>
  );

  const renderPricingBreakdown = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Pricing Breakdown</Text>
      
      <View style={styles.pricingRow}>
        <Text style={styles.pricingLabel}>Subtotal:</Text>
        <Text style={styles.pricingValue}>{formatCurrency(order.subtotal)}</Text>
      </View>
      
      {order.discount_amount > 0 && (
        <View style={styles.pricingRow}>
          <Text style={styles.pricingLabel}>Discount:</Text>
          <Text style={[styles.pricingValue, styles.discountValue]}>
            -{formatCurrency(order.discount_amount)}
          </Text>
        </View>
      )}
      
      {order.tax_amount > 0 && (
        <View style={styles.pricingRow}>
          <Text style={styles.pricingLabel}>Tax:</Text>
          <Text style={styles.pricingValue}>{formatCurrency(order.tax_amount)}</Text>
        </View>
      )}
      
      {order.shipping_amount > 0 && (
        <View style={styles.pricingRow}>
          <Text style={styles.pricingLabel}>Shipping:</Text>
          <Text style={styles.pricingValue}>{formatCurrency(order.shipping_amount)}</Text>
        </View>
      )}
      
      <View style={[styles.pricingRow, styles.totalRow]}>
        <Text style={styles.totalLabel}>Total:</Text>
        <Text style={styles.totalValue}>{formatCurrency(order.total_amount)}</Text>
      </View>
    </View>
  );

  const renderShippingInfo = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Shipping Information</Text>
      
      {order.shipping_address && (
        <View style={styles.addressContainer}>
          <Text style={styles.addressTitle}>Shipping Address:</Text>
          <Text style={styles.addressText}>
            {order.shipping_address.name}{'\n'}
            {order.shipping_address.address}{'\n'}
            {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postal_code}{'\n'}
            {order.shipping_address.country}
          </Text>
        </View>
      )}
      
      {order.shipping_method && (
        <View style={styles.shippingMethod}>
          <Text style={styles.shippingMethodLabel}>Shipping Method:</Text>
          <Text style={styles.shippingMethodValue}>{order.shipping_method}</Text>
        </View>
      )}
      
      {tracking && tracking.tracking_number && (
        <View style={styles.trackingInfo}>
          <Text style={styles.trackingLabel}>Tracking Number:</Text>
          <Text style={styles.trackingNumber}>{tracking.tracking_number}</Text>
          <TouchableOpacity style={styles.trackButton} onPress={handleTrackOrder}>
            <Icon name="local-shipping" size={20} color={colors.primary} />
            <Text style={styles.trackButtonText}>Track Package</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderActionButtons = () => (
    <View style={styles.actionButtons}>
      {OrderService.canCancelOrder(order) && (
        <TouchableOpacity style={styles.cancelButton} onPress={handleCancelOrder}>
          <Icon name="cancel" size={20} color={colors.background.primary} />
          <Text style={styles.cancelButtonText}>Cancel Order</Text>
        </TouchableOpacity>
      )}
      
      <TouchableOpacity style={styles.reorderButton} onPress={handleReorder}>
        <Icon name="refresh" size={20} color={colors.background.primary} />
        <Text style={styles.reorderButtonText}>Reorder Items</Text>
      </TouchableOpacity>
      
      {invoice && (
        <TouchableOpacity style={styles.invoiceButton} onPress={handleDownloadInvoice}>
          <Icon name="receipt" size={20} color={colors.primary} />
          <Text style={styles.invoiceButtonText}>Download Invoice</Text>
        </TouchableOpacity>
      )}
      
      <TouchableOpacity style={styles.supportButton} onPress={handleContactSupport}>
        <Icon name="support-agent" size={20} color={colors.primary} />
        <Text style={styles.supportButtonText}>Contact Support</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!order) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color={colors.text.secondary} />
          <Text style={styles.errorText}>Order not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Order Status Timeline */}
        {tracking && (
          <OrderStatusTimeline
            tracking={tracking}
            currentStatus={order.status}
            style={styles.timeline}
          />
        )}

        {/* Order Summary */}
        {renderOrderSummary()}

        {/* Order Items */}
        {renderOrderItems()}

        {/* Pricing Breakdown */}
        {renderPricingBreakdown()}

        {/* Shipping Information */}
        {renderShippingInfo()}

        {/* Action Buttons */}
        {renderActionButtons()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: spacing.sm,
    marginLeft: spacing.xs,
  },
  scrollView: {
    flex: 1,
  },
  timeline: {
    marginBottom: spacing.lg,
  },
  section: {
    backgroundColor: colors.background.primary,
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: typography.h4.fontSize,
    fontWeight: typography.h4.fontWeight,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
  },
  summaryValue: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 4,
  },
  statusText: {
    fontSize: typography.caption.fontSize,
    color: colors.background.primary,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: typography.h4.fontSize,
    fontWeight: 'bold',
    color: colors.primary,
  },
  orderItem: {
    marginBottom: spacing.sm,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  pricingLabel: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
  },
  pricingValue: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
  },
  discountValue: {
    color: colors.success,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.sm,
    marginTop: spacing.sm,
  },
  totalLabel: {
    fontSize: typography.h4.fontSize,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  totalValue: {
    fontSize: typography.h4.fontSize,
    fontWeight: 'bold',
    color: colors.primary,
  },
  addressContainer: {
    marginBottom: spacing.md,
  },
  addressTitle: {
    fontSize: typography.body.fontSize,
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  addressText: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  shippingMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  shippingMethodLabel: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
  },
  shippingMethodValue: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    fontWeight: '500',
  },
  trackingInfo: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.md,
  },
  trackingLabel: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  trackingNumber: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    fontWeight: '500',
    marginBottom: spacing.md,
  },
  trackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  trackButtonText: {
    fontSize: typography.body.fontSize,
    color: colors.primary,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },
  actionButtons: {
    padding: spacing.lg,
    gap: spacing.md,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.error,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: typography.button.fontSize,
    fontWeight: typography.button.fontWeight,
    color: colors.background.primary,
    marginLeft: spacing.xs,
  },
  reorderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  reorderButtonText: {
    fontSize: typography.button.fontSize,
    fontWeight: typography.button.fontWeight,
    color: colors.background.primary,
    marginLeft: spacing.xs,
  },
  invoiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background.secondary,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  invoiceButtonText: {
    fontSize: typography.button.fontSize,
    fontWeight: typography.button.fontWeight,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background.secondary,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  supportButtonText: {
    fontSize: typography.button.fontSize,
    fontWeight: typography.button.fontWeight,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  errorText: {
    fontSize: typography.h3.fontSize,
    color: colors.text.secondary,
    marginTop: spacing.md,
  },
});

export default OrderDetailScreen;
