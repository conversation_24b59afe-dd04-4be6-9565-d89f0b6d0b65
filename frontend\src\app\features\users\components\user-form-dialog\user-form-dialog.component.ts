import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

import { UserService, User, Role, CreateUserRequest, UpdateUserRequest } from '../../services/user.service';
import { LoadingService } from '../../../../core/services/loading.service';

export interface UserFormDialogData {
  mode: 'create' | 'edit';
  user?: User;
  roles: Role[];
}

@Component({
  selector: 'app-user-form-dialog',
  templateUrl: './user-form-dialog.component.html',
  styleUrls: ['./user-form-dialog.component.scss']
})
export class UserFormDialogComponent implements OnInit {
  userForm: FormGroup;
  loading = false;
  hidePassword = true;
  hideConfirmPassword = true;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private loadingService: LoadingService,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<UserFormDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: UserFormDialogData
  ) {
    this.userForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.data.mode === 'edit' && this.data.user) {
      this.populateForm(this.data.user);
    }
  }

  private createForm(): FormGroup {
    const form = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(255)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(255)]],
      phone: ['', [Validators.pattern(/^[\+]?[1-9][\d]{0,15}$/)]],
      role: ['customer', [Validators.required]],
      is_active: [true]
    });

    // Add password fields only for create mode
    if (this.data.mode === 'create') {
      form.addControl('password', this.fb.control('', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      ]));
      form.addControl('password_confirmation', this.fb.control('', [Validators.required]));

      // Add custom validator for password confirmation
      form.setValidators(this.passwordMatchValidator);
    }

    return form;
  }

  private populateForm(user: User): void {
    this.userForm.patchValue({
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      is_active: user.is_active
    });
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('password_confirmation');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    } else if (confirmPassword?.hasError('passwordMismatch')) {
      confirmPassword.setErrors(null);
    }
    
    return null;
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      this.loading = true;
      this.loadingService.setLoading(true);

      const formValue = this.userForm.value;

      if (this.data.mode === 'create') {
        this.createUser(formValue);
      } else {
        this.updateUser(formValue);
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private createUser(userData: CreateUserRequest): void {
    this.userService.createUser(userData).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('User created successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.dialogRef.close(true);
        }
        this.loading = false;
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error creating user:', error);
        
        if (error.error?.errors) {
          // Handle validation errors
          const errors = error.error.errors;
          Object.keys(errors).forEach(field => {
            const control = this.userForm.get(field);
            if (control) {
              control.setErrors({ serverError: errors[field][0] });
            }
          });
        } else {
          this.snackBar.open('Error creating user', 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
        
        this.loading = false;
        this.loadingService.setLoading(false);
      }
    });
  }

  private updateUser(userData: UpdateUserRequest): void {
    if (!this.data.user) return;

    this.userService.updateUser(this.data.user.id, userData).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('User updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.dialogRef.close(true);
        }
        this.loading = false;
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error updating user:', error);
        
        if (error.error?.errors) {
          // Handle validation errors
          const errors = error.error.errors;
          Object.keys(errors).forEach(field => {
            const control = this.userForm.get(field);
            if (control) {
              control.setErrors({ serverError: errors[field][0] });
            }
          });
        } else {
          this.snackBar.open('Error updating user', 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
        
        this.loading = false;
        this.loadingService.setLoading(false);
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  // Getter methods for form controls
  get name() { return this.userForm.get('name'); }
  get email() { return this.userForm.get('email'); }
  get phone() { return this.userForm.get('phone'); }
  get role() { return this.userForm.get('role'); }
  get password() { return this.userForm.get('password'); }
  get passwordConfirmation() { return this.userForm.get('password_confirmation'); }
  get isActive() { return this.userForm.get('is_active'); }

  // Error message methods
  getNameErrorMessage(): string {
    if (this.name?.hasError('required')) {
      return 'Name is required';
    }
    if (this.name?.hasError('minlength')) {
      return 'Name must be at least 2 characters long';
    }
    if (this.name?.hasError('maxlength')) {
      return 'Name cannot exceed 255 characters';
    }
    if (this.name?.hasError('serverError')) {
      return this.name.errors?.['serverError'];
    }
    return '';
  }

  getEmailErrorMessage(): string {
    if (this.email?.hasError('required')) {
      return 'Email is required';
    }
    if (this.email?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    if (this.email?.hasError('maxlength')) {
      return 'Email cannot exceed 255 characters';
    }
    if (this.email?.hasError('serverError')) {
      return this.email.errors?.['serverError'];
    }
    return '';
  }

  getPhoneErrorMessage(): string {
    if (this.phone?.hasError('pattern')) {
      return 'Please enter a valid phone number';
    }
    if (this.phone?.hasError('serverError')) {
      return this.phone.errors?.['serverError'];
    }
    return '';
  }

  getPasswordErrorMessage(): string {
    if (this.password?.hasError('required')) {
      return 'Password is required';
    }
    if (this.password?.hasError('minlength')) {
      return 'Password must be at least 8 characters long';
    }
    if (this.password?.hasError('pattern')) {
      return 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character';
    }
    if (this.password?.hasError('serverError')) {
      return this.password.errors?.['serverError'];
    }
    return '';
  }

  getPasswordConfirmationErrorMessage(): string {
    if (this.passwordConfirmation?.hasError('required')) {
      return 'Password confirmation is required';
    }
    if (this.passwordConfirmation?.hasError('passwordMismatch')) {
      return 'Passwords do not match';
    }
    return '';
  }

  getRoleOptions(): { value: string; label: string }[] {
    return this.data.roles.map(role => ({
      value: role.name,
      label: role.display_name
    }));
  }

  get dialogTitle(): string {
    return this.data.mode === 'create' ? 'Create User' : 'Edit User';
  }

  get submitButtonText(): string {
    return this.data.mode === 'create' ? 'Create User' : 'Update User';
  }
}
