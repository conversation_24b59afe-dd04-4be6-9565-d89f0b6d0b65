<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'profile_image',
        'bio',
        'website',
        'facebook',
        'twitter',
        'instagram',
        'linkedin',
        'timezone',
        'language',
        'currency',
        'preferences',
        'settings'
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'preferences' => 'array',
        'settings' => 'array',
    ];

    /**
     * Get the user that owns the profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get the profile image URL.
     */
    public function getProfileImageUrlAttribute(): ?string
    {
        if (!$this->profile_image) {
            return null;
        }

        if (filter_var($this->profile_image, FILTER_VALIDATE_URL)) {
            return $this->profile_image;
        }

        return asset('storage/' . $this->profile_image);
    }

    /**
     * Get the age.
     */
    public function getAgeAttribute(): ?int
    {
        if (!$this->date_of_birth) {
            return null;
        }

        return $this->date_of_birth->age;
    }

    /**
     * Get social media links.
     */
    public function getSocialLinksAttribute(): array
    {
        return array_filter([
            'facebook' => $this->facebook,
            'twitter' => $this->twitter,
            'instagram' => $this->instagram,
            'linkedin' => $this->linkedin,
            'website' => $this->website,
        ]);
    }

    /**
     * Get notification preferences.
     */
    public function getNotificationPreferences(): array
    {
        return $this->preferences['notifications'] ?? [
            'email_notifications' => true,
            'push_notifications' => true,
            'order_updates' => true,
            'promotions' => false,
            'newsletter' => false,
        ];
    }

    /**
     * Update notification preferences.
     */
    public function updateNotificationPreferences(array $preferences): void
    {
        $currentPreferences = $this->preferences ?? [];
        $currentPreferences['notifications'] = $preferences;
        
        $this->update(['preferences' => $currentPreferences]);
    }

    /**
     * Get app settings.
     */
    public function getAppSettings(): array
    {
        return $this->settings ?? [
            'theme' => 'light',
            'items_per_page' => 20,
            'default_view' => 'grid',
        ];
    }

    /**
     * Update app settings.
     */
    public function updateAppSettings(array $settings): void
    {
        $this->update(['settings' => array_merge($this->getAppSettings(), $settings)]);
    }
}
