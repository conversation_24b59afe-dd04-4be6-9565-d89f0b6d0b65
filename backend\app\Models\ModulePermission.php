<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ModulePermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'module_name',
        'page_name',
        'permission_name',
        'permission_group',
        'description',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get user group permissions for this permission.
     */
    public function userGroupPermissions(): HasMany
    {
        return $this->hasMany(UserGroupPermission::class, 'permission_id');
    }

    /**
     * Get user permissions for this permission.
     */
    public function userPermissions(): HasMany
    {
        return $this->hasMany(UserPermission::class, 'permission_id');
    }

    /**
     * Scope for active permissions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by module.
     */
    public function scopeByModule($query, string $module)
    {
        return $query->where('module_name', $module);
    }

    /**
     * Scope by permission group.
     */
    public function scopeByGroup($query, string $group)
    {
        return $query->where('permission_group', $group);
    }

    /**
     * Scope ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('module_name')->orderBy('page_name');
    }

    /**
     * Get all available modules.
     */
    public static function getAvailableModules(): array
    {
        return [
            'users' => 'User Management',
            'products' => 'Product Management',
            'categories' => 'Category Management',
            'orders' => 'Order Management',
            'invoices' => 'Invoice Management',
            'coupons' => 'Coupon Management',
            'transactions' => 'Transaction Management',
            'media' => 'Media Library',
            'settings' => 'System Settings',
            'design' => 'Design Settings',
            'analytics' => 'Analytics & Reports',
            'permissions' => 'Permission Management',
            'notifications' => 'Notification Management',
            'shipping' => 'Shipping Management',
            'taxes' => 'Tax Management',
            'reviews' => 'Review Management',
            'wishlists' => 'Wishlist Management',
            'cart' => 'Shopping Cart Management',
            'attributes' => 'Product Attributes',
            'variations' => 'Product Variations',
        ];
    }

    /**
     * Get all available pages for a module.
     */
    public static function getAvailablePages(string $module): array
    {
        $commonPages = [
            'list' => 'List/Index',
            'view' => 'View Details',
            'create' => 'Create New',
            'edit' => 'Edit/Update',
            'delete' => 'Delete',
            'export' => 'Export Data',
            'import' => 'Import Data',
        ];

        $moduleSpecificPages = [
            'users' => [
                'profile' => 'User Profile',
                'permissions' => 'User Permissions',
                'groups' => 'User Groups',
                'activity' => 'Activity Log',
            ],
            'products' => [
                'variations' => 'Product Variations',
                'attributes' => 'Product Attributes',
                'inventory' => 'Inventory Management',
                'duplicate' => 'Duplicate Product',
            ],
            'orders' => [
                'status' => 'Update Status',
                'contact' => 'Contact Customer',
                'tracking' => 'Tracking Information',
                'refund' => 'Process Refund',
            ],
            'media' => [
                'upload' => 'Upload Files',
                'organize' => 'Organize Files',
                'bulk' => 'Bulk Operations',
            ],
            'settings' => [
                'general' => 'General Settings',
                'payment' => 'Payment Settings',
                'shipping' => 'Shipping Settings',
                'email' => 'Email Settings',
            ],
            'design' => [
                'theme' => 'Theme Settings',
                'colors' => 'Color Settings',
                'typography' => 'Typography',
                'layout' => 'Layout Settings',
            ],
        ];

        return array_merge($commonPages, $moduleSpecificPages[$module] ?? []);
    }

    /**
     * Get permission groups.
     */
    public static function getPermissionGroups(): array
    {
        return [
            'general' => 'General Access',
            'admin' => 'Admin Access',
            'super_admin' => 'Super Admin Access',
            'editor' => 'Editor Access',
            'accountant' => 'Accountant Access',
            'data_entry' => 'Data Entry Access',
            'customer_service' => 'Customer Service Access',
            'inventory' => 'Inventory Management',
            'marketing' => 'Marketing Access',
        ];
    }

    /**
     * Create default permissions for a module.
     */
    public static function createDefaultPermissions(string $module): void
    {
        $pages = static::getAvailablePages($module);
        $actions = ['view', 'create', 'edit', 'delete'];

        foreach ($pages as $pageKey => $pageName) {
            foreach ($actions as $action) {
                static::updateOrCreate([
                    'module_name' => $module,
                    'page_name' => $pageKey,
                    'permission_name' => "{$action}_{$module}_{$pageKey}",
                ], [
                    'permission_group' => 'general',
                    'description' => "Can {$action} {$pageName} in {$module} module",
                    'is_active' => true,
                ]);
            }
        }
    }

    /**
     * Check if permission exists.
     */
    public static function permissionExists(string $module, string $page, string $action): bool
    {
        return static::where('module_name', $module)
            ->where('page_name', $page)
            ->where('permission_name', "{$action}_{$module}_{$page}")
            ->exists();
    }

    /**
     * Get formatted permission name.
     */
    public function getFormattedNameAttribute(): string
    {
        $modules = static::getAvailableModules();
        $moduleName = $modules[$this->module_name] ?? ucfirst($this->module_name);
        
        if ($this->page_name) {
            $pages = static::getAvailablePages($this->module_name);
            $pageName = $pages[$this->page_name] ?? ucfirst($this->page_name);
            return "{$moduleName} - {$pageName}";
        }
        
        return $moduleName;
    }
}
