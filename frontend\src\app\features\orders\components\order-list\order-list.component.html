<div class="order-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>Orders</h1>
      <p>Manage customer orders and track fulfillment</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="createOrder()">
        <mat-icon>add</mat-icon>
        Create Order
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-container">
        <!-- Search -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search orders</mat-label>
          <input
            matInput
            placeholder="Search by order number, customer name, or email"
            (input)="onSearch($event.target.value)"
            [value]="filters.search || ''"
          >
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Status Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(value)]="filters.status" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of statusOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Payment Status Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Payment Status</mat-label>
          <mat-select [(value)]="filters.payment_status" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of paymentStatusOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Payment Method Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Payment Method</mat-label>
          <mat-select [(value)]="filters.payment_method" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of paymentMethodOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Date Range -->
        <mat-form-field appearance="outline">
          <mat-label>From Date</mat-label>
          <input
            matInput
            type="date"
            [(ngModel)]="filters.date_from"
            (change)="onFilterChange()"
          >
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>To Date</mat-label>
          <input
            matInput
            type="date"
            [(ngModel)]="filters.date_to"
            (change)="onFilterChange()"
          >
        </mat-form-field>

        <!-- Clear Filters -->
        <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
          <mat-icon>clear</mat-icon>
          Clear Filters
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Bulk Actions -->
  <div class="bulk-actions" *ngIf="selection.selected.length > 0">
    <mat-card>
      <mat-card-content>
        <div class="bulk-actions-content">
          <span class="selection-count">
            {{ selection.selected.length }} order(s) selected
          </span>
          
          <div class="bulk-action-buttons">
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus('processing')"
            >
              <mat-icon>hourglass_empty</mat-icon>
              Mark Processing
            </button>
            
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus('shipped')"
            >
              <mat-icon>local_shipping</mat-icon>
              Mark Shipped
            </button>
            
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus('delivered')"
            >
              <mat-icon>check_circle</mat-icon>
              Mark Delivered
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Data Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <!-- Table Actions -->
      <div class="table-actions">
        <div class="table-info">
          <span>{{ totalItems }} orders found</span>
        </div>
        
        <div class="table-action-buttons">
          <button mat-icon-button (click)="loadOrders()" [disabled]="loading">
            <mat-icon>refresh</mat-icon>
          </button>
          
          <button mat-icon-button (click)="exportOrders()">
            <mat-icon>download</mat-icon>
          </button>
        </div>
      </div>

      <!-- Loading Indicator -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading orders...</p>
      </div>

      <!-- Data Table -->
      <div class="table-container" *ngIf="!loading">
        <table mat-table [dataSource]="dataSource" matSort class="orders-table">
          
          <!-- Select Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? masterToggle() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
              ></mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let order">
              <mat-checkbox
                (click)="$event.stopPropagation()"
                (change)="$event ? selection.toggle(order) : null"
                [checked]="selection.isSelected(order)"
              ></mat-checkbox>
            </td>
          </ng-container>

          <!-- Order Number Column -->
          <ng-container matColumnDef="order_number">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Order Number</th>
            <td mat-cell *matCellDef="let order">
              <div class="order-number">
                <span class="number">{{ order.order_number }}</span>
                <span class="date">{{ order.created_at | date:'short' }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Customer Column -->
          <ng-container matColumnDef="customer">
            <th mat-header-cell *matHeaderCellDef>Customer</th>
            <td mat-cell *matCellDef="let order">
              <div class="customer-info">
                <span class="name">{{ order.user.name }}</span>
                <span class="email">{{ order.user.email }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let order">
              <mat-chip [color]="getStatusColor(order.status)">
                {{ order.status | titlecase }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Payment Status Column -->
          <ng-container matColumnDef="payment_status">
            <th mat-header-cell *matHeaderCellDef>Payment</th>
            <td mat-cell *matCellDef="let order">
              <mat-chip [color]="getPaymentStatusColor(order.payment_status)">
                {{ order.payment_status | titlecase }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Payment Method Column -->
          <ng-container matColumnDef="payment_method">
            <th mat-header-cell *matHeaderCellDef>Method</th>
            <td mat-cell *matCellDef="let order">
              {{ getPaymentMethodLabel(order.payment_method) }}
            </td>
          </ng-container>

          <!-- Total Amount Column -->
          <ng-container matColumnDef="total_amount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Total</th>
            <td mat-cell *matCellDef="let order">
              <div class="amount-info">
                <span class="total">{{ formatCurrency(order.total_amount) }}</span>
                <span *ngIf="order.discount_amount > 0" class="discount">
                  -{{ formatCurrency(order.discount_amount) }}
                </span>
              </div>
            </td>
          </ng-container>

          <!-- Items Count Column -->
          <ng-container matColumnDef="items_count">
            <th mat-header-cell *matHeaderCellDef>Items</th>
            <td mat-cell *matCellDef="let order">
              <span class="items-count">{{ order.items_count }} item(s)</span>
            </td>
          </ng-container>

          <!-- Created At Column -->
          <ng-container matColumnDef="created_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Created</th>
            <td mat-cell *matCellDef="let order">
              {{ order.created_at | date:'short' }}
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let order">
              <div class="action-buttons">
                <button
                  mat-icon-button
                  [matTooltip]="'View Order'"
                  (click)="viewOrder(order)"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  [matTooltip]="'Edit Order'"
                  (click)="editOrder(order)"
                >
                  <mat-icon>edit</mat-icon>
                </button>

                <button
                  mat-icon-button
                  [matTooltip]="'Generate Invoice'"
                  (click)="generateInvoice(order)"
                  *ngIf="order.payment_status === 'paid'"
                >
                  <mat-icon>receipt</mat-icon>
                </button>

                <button
                  mat-icon-button
                  [matTooltip]="'Download Invoice'"
                  (click)="downloadInvoice(order)"
                  *ngIf="order.invoice"
                >
                  <mat-icon>download</mat-icon>
                </button>

                <button
                  mat-icon-button
                  [matTooltip]="'Send Confirmation Email'"
                  (click)="sendConfirmationEmail(order)"
                >
                  <mat-icon>email</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  [matTooltip]="'Cancel Order'"
                  color="warn"
                  (click)="cancelOrder(order)"
                  *ngIf="order.can_be_cancelled"
                >
                  <mat-icon>cancel</mat-icon>
                </button>

                <!-- Status Update Menu -->
                <button mat-icon-button [matMenuTriggerFor]="statusMenu">
                  <mat-icon>more_vert</mat-icon>
                </button>
                <mat-menu #statusMenu="matMenu">
                  <button mat-menu-item (click)="updateOrderStatus(order, 'processing')">
                    <mat-icon>hourglass_empty</mat-icon>
                    Mark Processing
                  </button>
                  <button mat-menu-item (click)="updateOrderStatus(order, 'shipped')">
                    <mat-icon>local_shipping</mat-icon>
                    Mark Shipped
                  </button>
                  <button mat-menu-item (click)="updateOrderStatus(order, 'delivered')">
                    <mat-icon>check_circle</mat-icon>
                    Mark Delivered
                  </button>
                </mat-menu>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data -->
        <div *ngIf="dataSource.data.length === 0" class="no-data">
          <mat-icon>shopping_cart</mat-icon>
          <h3>No orders found</h3>
          <p>Try adjusting your filters or create a new order.</p>
          <button mat-raised-button color="primary" (click)="createOrder()">
            <mat-icon>add</mat-icon>
            Create Order
          </button>
        </div>
      </div>

      <!-- Paginator -->
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        showFirstLastButtons
        *ngIf="!loading && dataSource.data.length > 0"
      ></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
