<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ProductAttribute extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'type',
        'description',
        'options',
        'is_required',
        'is_filterable',
        'is_searchable',
        'is_visible',
        'sort_order',
        'unit',
        'validation_rules'
    ];

    protected $casts = [
        'options' => 'array',
        'is_required' => 'boolean',
        'is_filterable' => 'boolean',
        'is_searchable' => 'boolean',
        'is_visible' => 'boolean',
        'validation_rules' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($attribute) {
            if (empty($attribute->slug)) {
                $attribute->slug = static::generateUniqueSlug($attribute->name);
            }
        });

        static::updating(function ($attribute) {
            if ($attribute->isDirty('name') && empty($attribute->slug)) {
                $attribute->slug = static::generateUniqueSlug($attribute->name, $attribute->id);
            }
        });
    }

    /**
     * Get the attribute values.
     */
    public function values(): HasMany
    {
        return $this->hasMany(ProductAttributeValue::class, 'attribute_id');
    }

    /**
     * Get products that have this attribute.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'product_attribute_values')
            ->withPivot(['value', 'display_value'])
            ->withTimestamps();
    }

    /**
     * Generate unique slug.
     */
    public static function generateUniqueSlug(string $name, int $excludeId = null): string
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)
            ->when($excludeId, function ($query) use ($excludeId) {
                $query->where('id', '!=', $excludeId);
            })
            ->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get formatted options for select/multiselect types.
     */
    public function getFormattedOptionsAttribute(): array
    {
        if (!in_array($this->type, ['select', 'multiselect']) || !$this->options) {
            return [];
        }

        return collect($this->options)->map(function ($option) {
            if (is_string($option)) {
                return ['value' => $option, 'label' => $option];
            }
            return $option;
        })->toArray();
    }

    /**
     * Validate attribute value.
     */
    public function validateValue($value): array
    {
        $errors = [];

        // Required validation
        if ($this->is_required && empty($value)) {
            $errors[] = "The {$this->name} field is required.";
        }

        // Type-specific validation
        switch ($this->type) {
            case 'number':
                if (!is_numeric($value)) {
                    $errors[] = "The {$this->name} must be a number.";
                }
                break;

            case 'select':
                if (!in_array($value, array_column($this->formatted_options, 'value'))) {
                    $errors[] = "The selected {$this->name} is invalid.";
                }
                break;

            case 'multiselect':
                if (!is_array($value)) {
                    $errors[] = "The {$this->name} must be an array.";
                } else {
                    $validOptions = array_column($this->formatted_options, 'value');
                    foreach ($value as $item) {
                        if (!in_array($item, $validOptions)) {
                            $errors[] = "One or more selected {$this->name} values are invalid.";
                            break;
                        }
                    }
                }
                break;

            case 'boolean':
                if (!is_bool($value) && !in_array($value, [0, 1, '0', '1', 'true', 'false'])) {
                    $errors[] = "The {$this->name} must be true or false.";
                }
                break;

            case 'date':
                if (!strtotime($value)) {
                    $errors[] = "The {$this->name} must be a valid date.";
                }
                break;

            case 'color':
                if (!preg_match('/^#[a-f0-9]{6}$/i', $value)) {
                    $errors[] = "The {$this->name} must be a valid hex color.";
                }
                break;
        }

        // Custom validation rules
        if ($this->validation_rules) {
            foreach ($this->validation_rules as $rule => $ruleValue) {
                switch ($rule) {
                    case 'min':
                        if (is_numeric($value) && $value < $ruleValue) {
                            $errors[] = "The {$this->name} must be at least {$ruleValue}.";
                        }
                        break;

                    case 'max':
                        if (is_numeric($value) && $value > $ruleValue) {
                            $errors[] = "The {$this->name} must not be greater than {$ruleValue}.";
                        }
                        break;

                    case 'min_length':
                        if (strlen($value) < $ruleValue) {
                            $errors[] = "The {$this->name} must be at least {$ruleValue} characters.";
                        }
                        break;

                    case 'max_length':
                        if (strlen($value) > $ruleValue) {
                            $errors[] = "The {$this->name} must not be greater than {$ruleValue} characters.";
                        }
                        break;
                }
            }
        }

        return $errors;
    }

    /**
     * Format value for display.
     */
    public function formatValue($value): string
    {
        switch ($this->type) {
            case 'boolean':
                return $value ? 'Yes' : 'No';

            case 'select':
            case 'multiselect':
                if (is_array($value)) {
                    return implode(', ', $value);
                }
                return (string) $value;

            case 'number':
                if ($this->unit) {
                    return $value . ' ' . $this->unit;
                }
                return (string) $value;

            case 'date':
                return date('Y-m-d', strtotime($value));

            case 'color':
                return strtoupper($value);

            default:
                return (string) $value;
        }
    }

    /**
     * Get unique values for this attribute.
     */
    public function getUniqueValues(): array
    {
        return $this->values()
            ->distinct('value')
            ->pluck('value')
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * Scope for visible attributes.
     */
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    /**
     * Scope for filterable attributes.
     */
    public function scopeFilterable($query)
    {
        return $query->where('is_filterable', true);
    }

    /**
     * Scope for searchable attributes.
     */
    public function scopeSearchable($query)
    {
        return $query->where('is_searchable', true);
    }

    /**
     * Scope for required attributes.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
