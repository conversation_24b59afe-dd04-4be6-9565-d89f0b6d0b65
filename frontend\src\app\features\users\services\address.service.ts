import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface UserAddress {
  id?: number;
  user_id?: number;
  type: 'shipping' | 'billing' | 'both';
  first_name: string;
  last_name: string;
  company?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
  is_default: boolean;
  created_at?: string;
  updated_at?: string;
  full_name?: string;
  formatted_address?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

@Injectable({
  providedIn: 'root'
})
export class AddressService {
  private apiUrl = `${environment.apiUrl}/user/addresses`;

  constructor(private http: HttpClient) {}

  /**
   * Get all user addresses
   */
  getAddresses(): Observable<ApiResponse<UserAddress[]>> {
    return this.http.get<ApiResponse<UserAddress[]>>(this.apiUrl);
  }

  /**
   * Get address by ID
   */
  getAddress(id: number): Observable<ApiResponse<UserAddress>> {
    return this.http.get<ApiResponse<UserAddress>>(`${this.apiUrl}/${id}`);
  }

  /**
   * Create new address
   */
  createAddress(address: Partial<UserAddress>): Observable<ApiResponse<UserAddress>> {
    return this.http.post<ApiResponse<UserAddress>>(this.apiUrl, address);
  }

  /**
   * Update address
   */
  updateAddress(id: number, address: Partial<UserAddress>): Observable<ApiResponse<UserAddress>> {
    return this.http.put<ApiResponse<UserAddress>>(`${this.apiUrl}/${id}`, address);
  }

  /**
   * Delete address
   */
  deleteAddress(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  /**
   * Set address as default
   */
  setAsDefault(id: number): Observable<ApiResponse<UserAddress>> {
    return this.http.post<ApiResponse<UserAddress>>(`${this.apiUrl}/${id}/set-default`, {});
  }

  /**
   * Get default addresses
   */
  getDefaultAddresses(): Observable<ApiResponse<{shipping: UserAddress, billing: UserAddress}>> {
    return this.http.get<ApiResponse<{shipping: UserAddress, billing: UserAddress}>>(`${this.apiUrl}/defaults`);
  }

  /**
   * Get addresses by type
   */
  getAddressesByType(type: string): Observable<ApiResponse<UserAddress[]>> {
    return this.http.get<ApiResponse<UserAddress[]>>(`${this.apiUrl}/type/${type}`);
  }

  /**
   * Validate address format
   */
  validateAddress(address: Partial<UserAddress>): string[] {
    const errors: string[] = [];

    if (!address.first_name?.trim()) {
      errors.push('First name is required');
    }

    if (!address.last_name?.trim()) {
      errors.push('Last name is required');
    }

    if (!address.address_line_1?.trim()) {
      errors.push('Address line 1 is required');
    }

    if (!address.city?.trim()) {
      errors.push('City is required');
    }

    if (!address.state?.trim()) {
      errors.push('State is required');
    }

    if (!address.postal_code?.trim()) {
      errors.push('Postal code is required');
    }

    if (!address.country?.trim()) {
      errors.push('Country is required');
    }

    if (!address.type || !['shipping', 'billing', 'both'].includes(address.type)) {
      errors.push('Valid address type is required');
    }

    // Validate postal code format (basic validation)
    if (address.postal_code && address.country === 'US') {
      const usZipRegex = /^\d{5}(-\d{4})?$/;
      if (!usZipRegex.test(address.postal_code)) {
        errors.push('Invalid US postal code format');
      }
    }

    // Validate phone number format (basic validation)
    if (address.phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(address.phone.replace(/[\s\-\(\)]/g, ''))) {
        errors.push('Invalid phone number format');
      }
    }

    return errors;
  }

  /**
   * Format address for display
   */
  formatAddress(address: UserAddress): string {
    const parts = [
      address.address_line_1,
      address.address_line_2,
      address.city,
      `${address.state} ${address.postal_code}`,
      address.country
    ].filter(part => part && part.trim());

    return parts.join(', ');
  }

  /**
   * Get full name from address
   */
  getFullName(address: UserAddress): string {
    return `${address.first_name} ${address.last_name}`.trim();
  }

  /**
   * Get countries list
   */
  getCountries(): {code: string, name: string}[] {
    return [
      { code: 'US', name: 'United States' },
      { code: 'CA', name: 'Canada' },
      { code: 'GB', name: 'United Kingdom' },
      { code: 'AU', name: 'Australia' },
      { code: 'DE', name: 'Germany' },
      { code: 'FR', name: 'France' },
      { code: 'IT', name: 'Italy' },
      { code: 'ES', name: 'Spain' },
      { code: 'NL', name: 'Netherlands' },
      { code: 'BE', name: 'Belgium' },
      { code: 'CH', name: 'Switzerland' },
      { code: 'AT', name: 'Austria' },
      { code: 'SE', name: 'Sweden' },
      { code: 'NO', name: 'Norway' },
      { code: 'DK', name: 'Denmark' },
      { code: 'FI', name: 'Finland' },
      { code: 'IE', name: 'Ireland' },
      { code: 'PT', name: 'Portugal' },
      { code: 'GR', name: 'Greece' },
      { code: 'PL', name: 'Poland' },
      { code: 'CZ', name: 'Czech Republic' },
      { code: 'HU', name: 'Hungary' },
      { code: 'SK', name: 'Slovakia' },
      { code: 'SI', name: 'Slovenia' },
      { code: 'HR', name: 'Croatia' },
      { code: 'BG', name: 'Bulgaria' },
      { code: 'RO', name: 'Romania' },
      { code: 'LT', name: 'Lithuania' },
      { code: 'LV', name: 'Latvia' },
      { code: 'EE', name: 'Estonia' },
      { code: 'MT', name: 'Malta' },
      { code: 'CY', name: 'Cyprus' },
      { code: 'LU', name: 'Luxembourg' }
    ];
  }

  /**
   * Get states for US
   */
  getUSStates(): {code: string, name: string}[] {
    return [
      { code: 'AL', name: 'Alabama' },
      { code: 'AK', name: 'Alaska' },
      { code: 'AZ', name: 'Arizona' },
      { code: 'AR', name: 'Arkansas' },
      { code: 'CA', name: 'California' },
      { code: 'CO', name: 'Colorado' },
      { code: 'CT', name: 'Connecticut' },
      { code: 'DE', name: 'Delaware' },
      { code: 'FL', name: 'Florida' },
      { code: 'GA', name: 'Georgia' },
      { code: 'HI', name: 'Hawaii' },
      { code: 'ID', name: 'Idaho' },
      { code: 'IL', name: 'Illinois' },
      { code: 'IN', name: 'Indiana' },
      { code: 'IA', name: 'Iowa' },
      { code: 'KS', name: 'Kansas' },
      { code: 'KY', name: 'Kentucky' },
      { code: 'LA', name: 'Louisiana' },
      { code: 'ME', name: 'Maine' },
      { code: 'MD', name: 'Maryland' },
      { code: 'MA', name: 'Massachusetts' },
      { code: 'MI', name: 'Michigan' },
      { code: 'MN', name: 'Minnesota' },
      { code: 'MS', name: 'Mississippi' },
      { code: 'MO', name: 'Missouri' },
      { code: 'MT', name: 'Montana' },
      { code: 'NE', name: 'Nebraska' },
      { code: 'NV', name: 'Nevada' },
      { code: 'NH', name: 'New Hampshire' },
      { code: 'NJ', name: 'New Jersey' },
      { code: 'NM', name: 'New Mexico' },
      { code: 'NY', name: 'New York' },
      { code: 'NC', name: 'North Carolina' },
      { code: 'ND', name: 'North Dakota' },
      { code: 'OH', name: 'Ohio' },
      { code: 'OK', name: 'Oklahoma' },
      { code: 'OR', name: 'Oregon' },
      { code: 'PA', name: 'Pennsylvania' },
      { code: 'RI', name: 'Rhode Island' },
      { code: 'SC', name: 'South Carolina' },
      { code: 'SD', name: 'South Dakota' },
      { code: 'TN', name: 'Tennessee' },
      { code: 'TX', name: 'Texas' },
      { code: 'UT', name: 'Utah' },
      { code: 'VT', name: 'Vermont' },
      { code: 'VA', name: 'Virginia' },
      { code: 'WA', name: 'Washington' },
      { code: 'WV', name: 'West Virginia' },
      { code: 'WI', name: 'Wisconsin' },
      { code: 'WY', name: 'Wyoming' }
    ];
  }
}
