<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'category_id',
        'mrp',
        'selling_price',
        'price',
        'sale_price',
        'cost_price',
        'discount_percentage',
        'stock_quantity',
        'manage_stock',
        'stock_status',
        'low_stock_threshold',
        'weight',
        'length',
        'width',
        'height',
        'status',
        'featured',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'purchase_note',
        'created_by',
        'attributes',
        'gallery',
        'main_image',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'mrp' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'weight' => 'decimal:2',
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'stock_quantity' => 'integer',
        'low_stock_threshold' => 'integer',
        'manage_stock' => 'boolean',
        'featured' => 'boolean',
        'attributes' => 'array',
        'gallery' => 'array',
    ];

    /**
     * Product status constants.
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_ARCHIVED = 'archived';

    /**
     * Stock status constants.
     */
    const STOCK_IN_STOCK = 'in_stock';
    const STOCK_OUT_OF_STOCK = 'out_of_stock';
    const STOCK_ON_BACKORDER = 'on_backorder';

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'sku', 'price', 'stock_quantity', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = static::generateUniqueSlug($product->name);
            }
            
            if (empty($product->sku)) {
                $product->sku = static::generateUniqueSku();
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = static::generateUniqueSlug($product->name, $product->id);
            }
        });
    }

    /**
     * Get the category that owns the product.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the user who created this product.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the product variations.
     */
    public function variations(): HasMany
    {
        return $this->hasMany(ProductVariation::class);
    }

    /**
     * Get the product images.
     */
    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class)->orderBy('sort_order');
    }

    /**
     * Get the main product image.
     */
    public function mainImage(): HasMany
    {
        return $this->images()->where('is_primary', true);
    }

    /**
     * Get the order items for this product.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the coupons that apply to this product.
     */
    public function coupons(): BelongsToMany
    {
        return $this->belongsToMany(Coupon::class, 'coupon_products');
    }

    /**
     * Get the upsell products.
     */
    public function upsells(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_upsells', 'product_id', 'upsell_product_id');
    }

    /**
     * Get the cross-sell products.
     */
    public function crossSells(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_cross_sells', 'product_id', 'cross_sell_product_id');
    }

    /**
     * Get the product reviews.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ProductReview::class);
    }

    /**
     * Get approved reviews only.
     */
    public function approvedReviews(): HasMany
    {
        return $this->reviews()->where('is_approved', true);
    }

    /**
     * Get the product attributes.
     */
    public function attributeValues(): HasMany
    {
        return $this->hasMany(ProductAttributeValue::class);
    }

    /**
     * Get wishlist items for this product.
     */
    public function wishlistItems(): HasMany
    {
        return $this->hasMany(WishlistItem::class);
    }

    /**
     * Get the main image URL.
     */
    public function getMainImageUrlAttribute(): ?string
    {
        $mainImage = $this->mainImage()->first();
        
        if ($mainImage) {
            return $mainImage->image_url;
        }

        // Fallback to gallery first image
        if (!empty($this->gallery) && is_array($this->gallery)) {
            $firstImage = $this->gallery[0] ?? null;
            if ($firstImage && isset($firstImage['url'])) {
                return $firstImage['url'];
            }
        }

        return null;
    }

    /**
     * Get the effective price (sale price if available, otherwise regular price).
     */
    public function getEffectivePriceAttribute(): float
    {
        return $this->sale_price && $this->sale_price > 0 ? $this->sale_price : $this->price;
    }

    /**
     * Check if product is on sale.
     */
    public function getIsOnSaleAttribute(): bool
    {
        return $this->sale_price && $this->sale_price > 0 && $this->sale_price < $this->price;
    }

    /**
     * Get the discount percentage.
     */
    public function getDiscountPercentageAttribute(): float
    {
        if (!$this->is_on_sale) {
            return 0;
        }

        return round((($this->price - $this->sale_price) / $this->price) * 100, 2);
    }

    /**
     * Check if product is in stock.
     */
    public function getIsInStockAttribute(): bool
    {
        if (!$this->manage_stock) {
            return $this->stock_status === self::STOCK_IN_STOCK;
        }

        return $this->stock_quantity > 0;
    }

    /**
     * Check if product is low stock.
     */
    public function getIsLowStockAttribute(): bool
    {
        if (!$this->manage_stock) {
            return false;
        }

        return $this->stock_quantity <= $this->low_stock_threshold;
    }

    /**
     * Get product dimensions as string.
     */
    public function getDimensionsAttribute(): string
    {
        $dimensions = [];
        
        if ($this->length) $dimensions[] = $this->length . 'L';
        if ($this->width) $dimensions[] = $this->width . 'W';
        if ($this->height) $dimensions[] = $this->height . 'H';
        
        return implode(' × ', $dimensions);
    }

    /**
     * Get SEO data.
     */
    public function getSeoDataAttribute(): array
    {
        return [
            'title' => $this->meta_title ?: $this->name,
            'description' => $this->meta_description ?: $this->short_description,
            'keywords' => $this->meta_keywords,
            'canonical_url' => route('products.show', $this->slug),
        ];
    }

    /**
     * Scope for published products.
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    /**
     * Scope for featured products.
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for in-stock products.
     */
    public function scopeInStock($query)
    {
        return $query->where(function ($q) {
            $q->where('manage_stock', false)
              ->where('stock_status', self::STOCK_IN_STOCK)
              ->orWhere(function ($q2) {
                  $q2->where('manage_stock', true)
                     ->where('stock_quantity', '>', 0);
              });
        });
    }

    /**
     * Scope for products on sale.
     */
    public function scopeOnSale($query)
    {
        return $query->whereNotNull('sale_price')
                    ->where('sale_price', '>', 0)
                    ->whereColumn('sale_price', '<', 'price');
    }

    /**
     * Scope for products in specific category.
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope for products with price range.
     */
    public function scopePriceBetween($query, $min, $max)
    {
        return $query->whereBetween('price', [$min, $max]);
    }

    /**
     * Generate unique slug.
     */
    public static function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)
            ->when($excludeId, fn($q) => $q->where('id', '!=', $excludeId))
            ->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate unique SKU.
     */
    public static function generateUniqueSku(): string
    {
        do {
            $sku = 'PROD-' . strtoupper(Str::random(8));
        } while (static::where('sku', $sku)->exists());

        return $sku;
    }

    /**
     * Update stock quantity.
     */
    public function updateStock(int $quantity, string $operation = 'set'): void
    {
        if (!$this->manage_stock) {
            return;
        }

        switch ($operation) {
            case 'increase':
                $this->increment('stock_quantity', $quantity);
                break;
            case 'decrease':
                $this->decrement('stock_quantity', $quantity);
                break;
            case 'set':
            default:
                $this->update(['stock_quantity' => $quantity]);
                break;
        }

        // Update stock status
        $this->update([
            'stock_status' => $this->stock_quantity > 0 
                ? self::STOCK_IN_STOCK 
                : self::STOCK_OUT_OF_STOCK
        ]);
    }

    /**
     * Duplicate product.
     */
    public function duplicate(): self
    {
        $duplicate = $this->replicate();
        $duplicate->name = $this->name . ' (Copy)';
        $duplicate->slug = static::generateUniqueSlug($duplicate->name);
        $duplicate->sku = static::generateUniqueSku();
        $duplicate->status = self::STATUS_DRAFT;
        $duplicate->created_by = auth()->id();
        $duplicate->save();

        // Copy images
        foreach ($this->images as $image) {
            $duplicateImage = $image->replicate();
            $duplicateImage->product_id = $duplicate->id;
            $duplicateImage->save();
        }

        // Copy variations
        foreach ($this->variations as $variation) {
            $duplicateVariation = $variation->replicate();
            $duplicateVariation->product_id = $duplicate->id;
            $duplicateVariation->sku = static::generateUniqueSku();
            $duplicateVariation->save();
        }

        return $duplicate;
    }

    /**
     * Get product statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_products' => static::count(),
            'published_products' => static::published()->count(),
            'draft_products' => static::where('status', self::STATUS_DRAFT)->count(),
            'featured_products' => static::featured()->count(),
            'in_stock_products' => static::inStock()->count(),
            'out_of_stock_products' => static::where('stock_status', self::STOCK_OUT_OF_STOCK)->count(),
            'on_sale_products' => static::onSale()->count(),
            'low_stock_products' => static::where('manage_stock', true)
                ->whereColumn('stock_quantity', '<=', 'low_stock_threshold')
                ->count(),
        ];
    }

    /**
     * Update the average rating and review count.
     */
    public function updateAverageRating(): void
    {
        $approvedReviews = $this->approvedReviews();
        $averageRating = $approvedReviews->avg('rating') ?? 0;
        $reviewCount = $approvedReviews->count();

        $this->update([
            'average_rating' => round($averageRating, 2),
            'review_count' => $reviewCount
        ]);
    }

    /**
     * Check if user can review this product.
     */
    public function canBeReviewedBy(User $user): bool
    {
        // Check if user already reviewed this product
        $existingReview = $this->reviews()->where('user_id', $user->id)->exists();

        if ($existingReview) {
            return false;
        }

        // Check if user has purchased this product
        return $user->orders()
            ->whereHas('items', function ($query) {
                $query->where('product_id', $this->id);
            })
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Get the main image URL.
     */
    public function getMainImageUrlAttribute(): ?string
    {
        if ($this->main_image) {
            return Storage::url($this->main_image);
        }

        // Fallback to first gallery image
        if ($this->gallery && count($this->gallery) > 0) {
            return Storage::url($this->gallery[0]);
        }

        return null;
    }

    /**
     * Get calculated discount percentage.
     */
    public function getCalculatedDiscountPercentageAttribute(): float
    {
        if ($this->mrp > 0 && $this->selling_price < $this->mrp) {
            return round((($this->mrp - $this->selling_price) / $this->mrp) * 100, 2);
        }

        return $this->discount_percentage ?? 0;
    }

    /**
     * Get savings amount.
     */
    public function getSavingsAmountAttribute(): float
    {
        return max(0, $this->mrp - $this->selling_price);
    }

    /**
     * Check if product is on sale.
     */
    public function getIsOnSaleAttribute(): bool
    {
        return $this->sale_price && $this->sale_price < $this->selling_price;
    }

    /**
     * Get current effective price.
     */
    public function getCurrentPriceAttribute(): float
    {
        return $this->sale_price ?: $this->selling_price;
    }
}
