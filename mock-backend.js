const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8001;

// Middleware
app.use(cors());
app.use(express.json());

// Simple session storage (in production, use Redis or database)
const sessions = new Map();

// Admin credentials (in production, use hashed passwords from database)
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123',
  name: 'Admin User',
  role: 'admin'
};

// Helper function to generate session token
function generateToken() {
  return 'token_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

// Middleware to check authentication
function requireAuth(req, res, next) {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !sessions.has(token)) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required. Please login first.',
      code: 'UNAUTHORIZED'
    });
  }

  req.user = sessions.get(token);
  next();
}

// Enhanced Mock data with comprehensive fields
const mockStats = {
  users: {
    total: 150,
    active: 120,
    inactive: 30,
    new_today: 5,
    new_this_week: 23,
    new_this_month: 87,
    verified: 135,
    unverified: 15,
    premium: 45,
    regular: 105,
    last_login_today: 89,
    countries: 12,
    avg_session_duration: '8m 32s',
    bounce_rate: '23.5%'
  },
  products: {
    total: 500,
    published: 450,
    draft: 35,
    archived: 15,
    out_of_stock: 10,
    low_stock: 25,
    in_stock: 465,
    featured: 67,
    on_sale: 89,
    new_arrivals: 34,
    top_rated: 156,
    categories_count: 25,
    brands_count: 78,
    avg_rating: 4.2,
    total_reviews: 2847,
    avg_price: 89.99,
    highest_price: 1299.99,
    lowest_price: 9.99
  },
  orders: {
    total: 1200,
    pending: 15,
    processing: 8,
    shipped: 45,
    delivered: 1177,
    cancelled: 23,
    refunded: 12,
    returned: 8,
    total_revenue: 45000,
    orders_today: 12,
    revenue_today: 850,
    orders_this_week: 89,
    revenue_this_week: 6780,
    orders_this_month: 234,
    revenue_this_month: 18900,
    avg_order_value: 37.50,
    highest_order_value: 567.89,
    repeat_customers: 456,
    first_time_buyers: 744,
    conversion_rate: '3.2%',
    cart_abandonment_rate: '68.5%'
  },
  categories: {
    total: 25,
    active: 22,
    inactive: 3,
    featured: 8,
    with_products: 22,
    empty: 3,
    top_selling: 12,
    seasonal: 6,
    new_categories: 2
  },
  inventory: {
    total_items: 15678,
    total_value: 234567.89,
    low_stock_alerts: 25,
    out_of_stock_alerts: 10,
    overstock_items: 45,
    reorder_points: 67,
    suppliers: 23,
    warehouses: 4,
    avg_stock_level: 156.7
  },
  sales: {
    today: 850.00,
    yesterday: 923.45,
    this_week: 6780.00,
    last_week: 7234.56,
    this_month: 18900.00,
    last_month: 21456.78,
    this_year: 156789.00,
    growth_rate: '+12.5%',
    best_selling_product: 'Wireless Headphones',
    peak_hour: '2:00 PM - 3:00 PM',
    avg_items_per_order: 2.3
  },
  marketing: {
    email_subscribers: 2345,
    newsletter_open_rate: '24.5%',
    social_followers: 15678,
    active_campaigns: 5,
    coupon_usage: 234,
    affiliate_sales: 5678.90,
    referral_traffic: '18.2%',
    seo_ranking_keywords: 156,
    ad_spend: 2345.67,
    roas: '4.2x'
  },
  support: {
    open_tickets: 23,
    resolved_tickets: 456,
    avg_response_time: '2h 15m',
    satisfaction_rating: 4.6,
    knowledge_base_articles: 234,
    live_chat_sessions: 89,
    phone_calls: 45,
    email_inquiries: 123
  }
};

const mockUser = {
  id: 1,
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin'
};

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'E-Commerce Backend API Mock Server',
    version: '1.0.0',
    status: 'running',
    endpoints: [
      'GET /api/auth/user',
      'POST /api/auth/login',
      'GET /api/dashboard/stats',
      'GET /api/users/statistics',
      'GET /api/products/statistics',
      'GET /api/orders/statistics',
      'GET /api/categories/statistics'
    ]
  });
});

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  // Validate input
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required',
      code: 'MISSING_CREDENTIALS'
    });
  }

  // Check credentials
  if (email === adminCredentials.email && password === adminCredentials.password) {
    // Generate session token
    const token = generateToken();
    const user = {
      id: 1,
      name: adminCredentials.name,
      email: adminCredentials.email,
      role: adminCredentials.role
    };

    // Store session
    sessions.set(token, user);

    // Set session expiry (24 hours)
    setTimeout(() => {
      sessions.delete(token);
    }, 24 * 60 * 60 * 1000);

    res.json({
      success: true,
      message: 'Login successful! Welcome to admin dashboard.',
      data: {
        user: user,
        token: token,
        expiresIn: '24h'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid email or password. Please try again.',
      code: 'INVALID_CREDENTIALS'
    });
  }
});

app.get('/api/auth/user', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: req.user
  });
});

app.post('/api/auth/logout', requireAuth, (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (token && sessions.has(token)) {
    sessions.delete(token);
  }

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Check authentication status
app.get('/api/auth/check', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (token && sessions.has(token)) {
    res.json({
      success: true,
      authenticated: true,
      data: sessions.get(token)
    });
  } else {
    res.json({
      success: true,
      authenticated: false,
      message: 'Not authenticated'
    });
  }
});

// Dashboard routes (protected)
app.get('/api/dashboard/stats', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats
  });
});

// Admin menu structure (protected)
app.get('/api/admin/menu', requireAuth, (req, res) => {
  const adminMenu = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: 'dashboard',
      route: '/dashboard',
      children: []
    },
    {
      id: 'users',
      title: 'User Management',
      icon: 'people',
      route: '/users',
      children: [
        { id: 'users-list', title: 'All Users', route: '/users' },
        { id: 'users-create', title: 'Add User', route: '/users/create' },
        { id: 'user-groups', title: 'User Groups', route: '/users/groups' },
        { id: 'user-roles', title: 'Roles & Permissions', route: '/users/roles' }
      ]
    },
    {
      id: 'products',
      title: 'Product Management',
      icon: 'inventory',
      route: '/products',
      children: [
        { id: 'products-list', title: 'All Products', route: '/products' },
        { id: 'products-create', title: 'Add Product', route: '/products/create' },
        { id: 'categories', title: 'Categories', route: '/products/categories' },
        { id: 'product-variations', title: 'Variations', route: '/products/variations' }
      ]
    },
    {
      id: 'orders',
      title: 'Order Management',
      icon: 'shopping_cart',
      route: '/orders',
      children: [
        { id: 'orders-list', title: 'All Orders', route: '/orders' },
        { id: 'orders-pending', title: 'Pending Orders', route: '/orders?status=pending' },
        { id: 'orders-processing', title: 'Processing', route: '/orders?status=processing' },
        { id: 'orders-shipped', title: 'Shipped', route: '/orders?status=shipped' }
      ]
    },
    {
      id: 'transactions',
      title: 'Transactions',
      icon: 'payment',
      route: '/transactions',
      children: [
        { id: 'transactions-list', title: 'All Transactions', route: '/transactions' },
        { id: 'transactions-summary', title: 'Summary', route: '/transactions/summary' }
      ]
    },
    {
      id: 'invoices',
      title: 'Invoices',
      icon: 'receipt',
      route: '/invoices',
      children: [
        { id: 'invoices-list', title: 'All Invoices', route: '/invoices' },
        { id: 'invoices-create', title: 'Create Invoice', route: '/invoices/create' }
      ]
    },
    {
      id: 'coupons',
      title: 'Coupons & Discounts',
      icon: 'local_offer',
      route: '/coupons',
      children: [
        { id: 'coupons-list', title: 'All Coupons', route: '/coupons' },
        { id: 'coupons-create', title: 'Create Coupon', route: '/coupons/create' }
      ]
    },
    {
      id: 'media',
      title: 'Media Library',
      icon: 'photo_library',
      route: '/media',
      children: []
    },
    {
      id: 'analytics',
      title: 'Analytics & Reports',
      icon: 'analytics',
      route: '/analytics',
      children: [
        { id: 'analytics-dashboard', title: 'Analytics Dashboard', route: '/analytics' },
        { id: 'analytics-users', title: 'User Analytics', route: '/analytics/users' },
        { id: 'analytics-products', title: 'Product Analytics', route: '/analytics/products' },
        { id: 'analytics-orders', title: 'Order Analytics', route: '/analytics/orders' },
        { id: 'analytics-revenue', title: 'Revenue Analytics', route: '/analytics/revenue' }
      ]
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings',
      route: '/settings',
      children: [
        { id: 'settings-general', title: 'General Settings', route: '/settings/general' },
        { id: 'settings-design', title: 'Design Settings', route: '/settings/design' },
        { id: 'settings-payment', title: 'Payment Settings', route: '/settings/payment' },
        { id: 'settings-email', title: 'Email Settings', route: '/settings/email' },
        { id: 'settings-system', title: 'System Settings', route: '/settings/system' }
      ]
    }
  ];

  res.json({
    success: true,
    data: adminMenu
  });
});

// Dashboard overview with detailed stats (protected)
app.get('/api/dashboard/overview', requireAuth, (req, res) => {
  const overview = {
    stats: mockStats,
    recent_activities: [
      {
        id: 1,
        type: 'order',
        title: 'New order received',
        description: 'Order #ORD-001234 from John Doe',
        time: '2 minutes ago',
        icon: 'shopping_cart',
        color: 'success'
      },
      {
        id: 2,
        type: 'user',
        title: 'New user registered',
        description: 'Jane Smith joined the platform',
        time: '15 minutes ago',
        icon: 'person_add',
        color: 'info'
      },
      {
        id: 3,
        type: 'product',
        title: 'Product updated',
        description: 'iPhone 15 Pro stock updated',
        time: '1 hour ago',
        icon: 'inventory',
        color: 'warning'
      },
      {
        id: 4,
        type: 'payment',
        title: 'Payment received',
        description: '$299.99 payment for Order #ORD-001230',
        time: '2 hours ago',
        icon: 'payment',
        color: 'success'
      }
    ],
    quick_actions: [
      {
        id: 'add-product',
        title: 'Add Product',
        description: 'Add a new product to your catalog',
        icon: 'add_box',
        route: '/products/create',
        color: 'primary'
      },
      {
        id: 'view-orders',
        title: 'View Orders',
        description: 'Check pending and recent orders',
        icon: 'list_alt',
        route: '/orders',
        color: 'accent'
      },
      {
        id: 'create-coupon',
        title: 'Create Coupon',
        description: 'Set up discount coupons',
        icon: 'local_offer',
        route: '/coupons/create',
        color: 'warn'
      },
      {
        id: 'view-analytics',
        title: 'View Analytics',
        description: 'Check sales and performance',
        icon: 'trending_up',
        route: '/analytics',
        color: 'primary'
      }
    ],
    notifications: [
      {
        id: 1,
        title: 'Low Stock Alert',
        message: '5 products are running low on stock',
        type: 'warning',
        time: '30 minutes ago',
        read: false
      },
      {
        id: 2,
        title: 'New Order',
        message: 'You have 3 new orders to process',
        type: 'info',
        time: '1 hour ago',
        read: false
      },
      {
        id: 3,
        title: 'Payment Received',
        message: 'Payment of $1,250.00 received',
        type: 'success',
        time: '2 hours ago',
        read: true
      }
    ]
  };

  res.json({
    success: true,
    data: overview
  });
});

// Statistics routes (protected)
app.get('/api/users/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.users
  });
});

app.get('/api/products/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.products
  });
});

app.get('/api/orders/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.orders
  });
});

app.get('/api/categories/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.categories
  });
});

// Additional comprehensive endpoints
app.get('/api/inventory/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.inventory
  });
});

app.get('/api/sales/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.sales
  });
});

app.get('/api/marketing/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.marketing
  });
});

app.get('/api/support/statistics', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: mockStats.support
  });
});

// User management endpoints
app.get('/api/users', requireAuth, (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const search = req.query.search || '';

  const allUsers = [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'customer',
      status: 'active',
      verified: true,
      premium: false,
      registration_date: '2024-01-15',
      last_login: '2025-01-04',
      total_orders: 12,
      total_spent: 567.89,
      country: 'USA',
      city: 'New York',
      phone: '******-0123',
      avatar: 'https://via.placeholder.com/50'
    },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'customer',
      status: 'active',
      verified: true,
      premium: true,
      registration_date: '2024-02-20',
      last_login: '2025-01-03',
      total_orders: 8,
      total_spent: 234.56,
      country: 'Canada',
      city: 'Toronto',
      phone: '******-0124',
      avatar: 'https://via.placeholder.com/50'
    },
    {
      id: 3,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      role: 'customer',
      status: 'inactive',
      verified: false,
      premium: false,
      registration_date: '2024-03-10',
      last_login: '2024-12-15',
      total_orders: 3,
      total_spent: 89.99,
      country: 'UK',
      city: 'London',
      phone: '+44-555-0125',
      avatar: 'https://via.placeholder.com/50'
    },
    {
      id: 4,
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      verified: true,
      premium: true,
      registration_date: '2024-01-01',
      last_login: '2025-01-04',
      total_orders: 0,
      total_spent: 0,
      country: 'USA',
      city: 'San Francisco',
      phone: '******-0126',
      avatar: 'https://via.placeholder.com/50'
    },
    {
      id: 5,
      name: 'David Brown',
      email: '<EMAIL>',
      role: 'customer',
      status: 'suspended',
      verified: true,
      premium: false,
      registration_date: '2024-04-15',
      last_login: '2024-12-20',
      total_orders: 15,
      total_spent: 890.45,
      country: 'Australia',
      city: 'Sydney',
      phone: '+61-555-0127',
      avatar: 'https://via.placeholder.com/50'
    }
  ];

  // Filter users based on search
  let filteredUsers = allUsers;
  if (search) {
    filteredUsers = allUsers.filter(user =>
      user.name.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase())
    );
  }

  // Paginate results
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: paginatedUsers,
    pagination: {
      current_page: page,
      per_page: limit,
      total: filteredUsers.length,
      total_pages: Math.ceil(filteredUsers.length / limit)
    }
  });
});

app.post('/api/users', requireAuth, (req, res) => {
  const { name, email, role, status } = req.body;

  // Simulate user creation
  const newUser = {
    id: Date.now(),
    name,
    email,
    role: role || 'customer',
    status: status || 'active',
    verified: false,
    premium: false,
    registration_date: new Date().toISOString().split('T')[0],
    last_login: null,
    total_orders: 0,
    total_spent: 0,
    country: '',
    city: '',
    phone: '',
    avatar: 'https://via.placeholder.com/50'
  };

  res.json({
    success: true,
    message: 'User created successfully',
    data: newUser
  });
});

app.put('/api/users/:id', requireAuth, (req, res) => {
  const userId = req.params.id;
  const updates = req.body;

  // Simulate user update
  res.json({
    success: true,
    message: `User ${userId} updated successfully`,
    data: { id: userId, ...updates }
  });
});

app.delete('/api/users/:id', requireAuth, (req, res) => {
  const userId = req.params.id;

  res.json({
    success: true,
    message: `User ${userId} deleted successfully`
  });
});

// Detailed user data
app.get('/api/users/detailed', requireAuth, (req, res) => {
  const detailedUsers = [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'customer',
      status: 'active',
      verified: true,
      premium: false,
      registration_date: '2024-01-15',
      last_login: '2025-01-04',
      total_orders: 12,
      total_spent: 567.89,
      country: 'USA',
      city: 'New York'
    },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'customer',
      status: 'active',
      verified: true,
      premium: true,
      registration_date: '2024-02-20',
      last_login: '2025-01-03',
      total_orders: 8,
      total_spent: 234.56,
      country: 'Canada',
      city: 'Toronto'
    },
    {
      id: 3,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      role: 'customer',
      status: 'inactive',
      verified: false,
      premium: false,
      registration_date: '2024-03-10',
      last_login: '2024-12-15',
      total_orders: 3,
      total_spent: 89.99,
      country: 'UK',
      city: 'London'
    }
  ];

  res.json({
    success: true,
    data: detailedUsers,
    pagination: {
      current_page: 1,
      per_page: 20,
      total: 150,
      total_pages: 8
    }
  });
});

// Activity log (protected)
app.get('/api/activity-log', requireAuth, (req, res) => {
  const activities = [
    {
      id: 1,
      description: 'New user registered',
      subject_type: 'App\\Models\\User',
      subject_id: 123,
      causer: { id: 1, name: 'System' },
      created_at: new Date().toISOString()
    },
    {
      id: 2,
      description: 'Product updated',
      subject_type: 'App\\Models\\Product',
      subject_id: 456,
      causer: { id: 2, name: 'Admin' },
      created_at: new Date(Date.now() - 3600000).toISOString()
    }
  ];
  
  res.json({
    success: true,
    data: activities
  });
});

// Analytics routes (protected)
app.get('/api/analytics/sales-chart', requireAuth, (req, res) => {
  const days = parseInt(req.query.days) || 7;
  const values = Array.from({ length: days }, () => Math.floor(Math.random() * 1000) + 500);
  
  res.json({
    success: true,
    data: {
      labels: Array.from({ length: days }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (days - 1 - i));
        return date.toLocaleDateString();
      }),
      values: values
    }
  });
});

app.get('/api/analytics/orders-chart', requireAuth, (req, res) => {
  const days = parseInt(req.query.days) || 7;
  const values = Array.from({ length: days }, () => Math.floor(Math.random() * 50) + 10);
  
  res.json({
    success: true,
    data: {
      labels: Array.from({ length: days }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (days - 1 - i));
        return date.toLocaleDateString();
      }),
      values: values
    }
  });
});

// Product management endpoints
app.get('/api/products', requireAuth, (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const search = req.query.search || '';
  const category = req.query.category || '';
  const status = req.query.status || '';

  const allProducts = [
    {
      id: 1,
      name: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      price: 99.99,
      sale_price: 79.99,
      cost: 45.00,
      stock: 150,
      category: 'Electronics',
      status: 'published',
      featured: true,
      rating: 4.5,
      reviews: 89,
      image: 'https://via.placeholder.com/100',
      created_at: '2024-01-15',
      updated_at: '2025-01-04'
    },
    {
      id: 2,
      name: 'Organic Cotton T-Shirt',
      sku: 'OCT-002',
      price: 29.99,
      sale_price: null,
      cost: 12.00,
      stock: 75,
      category: 'Clothing',
      status: 'published',
      featured: false,
      rating: 4.2,
      reviews: 34,
      image: 'https://via.placeholder.com/100',
      created_at: '2024-02-20',
      updated_at: '2025-01-03'
    },
    {
      id: 3,
      name: 'Smart Fitness Watch',
      sku: 'SFW-003',
      price: 199.99,
      sale_price: 149.99,
      cost: 89.00,
      stock: 0,
      category: 'Electronics',
      status: 'published',
      featured: true,
      rating: 4.7,
      reviews: 156,
      image: 'https://via.placeholder.com/100',
      created_at: '2024-03-10',
      updated_at: '2025-01-02'
    },
    {
      id: 4,
      name: 'Eco-Friendly Water Bottle',
      sku: 'EWB-004',
      price: 24.99,
      sale_price: null,
      cost: 8.50,
      stock: 200,
      category: 'Home & Garden',
      status: 'draft',
      featured: false,
      rating: 4.0,
      reviews: 12,
      image: 'https://via.placeholder.com/100',
      created_at: '2024-04-15',
      updated_at: '2024-12-20'
    },
    {
      id: 5,
      name: 'Premium Leather Wallet',
      sku: 'PLW-005',
      price: 79.99,
      sale_price: 59.99,
      cost: 25.00,
      stock: 45,
      category: 'Accessories',
      status: 'published',
      featured: true,
      rating: 4.8,
      reviews: 67,
      image: 'https://via.placeholder.com/100',
      created_at: '2024-05-01',
      updated_at: '2025-01-01'
    }
  ];

  // Filter products
  let filteredProducts = allProducts;

  if (search) {
    filteredProducts = filteredProducts.filter(product =>
      product.name.toLowerCase().includes(search.toLowerCase()) ||
      product.sku.toLowerCase().includes(search.toLowerCase())
    );
  }

  if (category) {
    filteredProducts = filteredProducts.filter(product =>
      product.category.toLowerCase() === category.toLowerCase()
    );
  }

  if (status) {
    filteredProducts = filteredProducts.filter(product =>
      product.status === status
    );
  }

  // Paginate results
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: paginatedProducts,
    pagination: {
      current_page: page,
      per_page: limit,
      total: filteredProducts.length,
      total_pages: Math.ceil(filteredProducts.length / limit)
    }
  });
});

app.post('/api/products', requireAuth, (req, res) => {
  const { name, sku, price, cost, stock, category, status } = req.body;

  const newProduct = {
    id: Date.now(),
    name,
    sku,
    price: parseFloat(price),
    sale_price: null,
    cost: parseFloat(cost),
    stock: parseInt(stock),
    category,
    status: status || 'draft',
    featured: false,
    rating: 0,
    reviews: 0,
    image: 'https://via.placeholder.com/100',
    created_at: new Date().toISOString().split('T')[0],
    updated_at: new Date().toISOString().split('T')[0]
  };

  res.json({
    success: true,
    message: 'Product created successfully',
    data: newProduct
  });
});

app.put('/api/products/:id', requireAuth, (req, res) => {
  const productId = req.params.id;
  const updates = req.body;

  res.json({
    success: true,
    message: `Product ${productId} updated successfully`,
    data: { id: productId, ...updates }
  });
});

app.delete('/api/products/:id', requireAuth, (req, res) => {
  const productId = req.params.id;

  res.json({
    success: true,
    message: `Product ${productId} deleted successfully`
  });
});

// Order management endpoints
app.get('/api/orders', requireAuth, (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const status = req.query.status || '';
  const search = req.query.search || '';

  const allOrders = [
    {
      id: 1001,
      order_number: 'ORD-2025-001001',
      customer: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40'
      },
      status: 'pending',
      payment_status: 'paid',
      total: 156.99,
      items_count: 3,
      shipping_address: '123 Main St, New York, NY 10001',
      created_at: '2025-01-04T10:30:00Z',
      updated_at: '2025-01-04T10:30:00Z'
    },
    {
      id: 1002,
      order_number: 'ORD-2025-001002',
      customer: {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40'
      },
      status: 'processing',
      payment_status: 'paid',
      total: 89.50,
      items_count: 2,
      shipping_address: '456 Oak Ave, Toronto, ON M5V 3A8',
      created_at: '2025-01-03T14:15:00Z',
      updated_at: '2025-01-04T09:20:00Z'
    },
    {
      id: 1003,
      order_number: 'ORD-2025-001003',
      customer: {
        id: 3,
        name: 'Mike Johnson',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40'
      },
      status: 'shipped',
      payment_status: 'paid',
      total: 234.75,
      items_count: 5,
      shipping_address: '789 Pine Rd, London, UK SW1A 1AA',
      created_at: '2025-01-02T16:45:00Z',
      updated_at: '2025-01-03T11:30:00Z'
    },
    {
      id: 1004,
      order_number: 'ORD-2025-001004',
      customer: {
        id: 4,
        name: 'Sarah Wilson',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40'
      },
      status: 'delivered',
      payment_status: 'paid',
      total: 67.25,
      items_count: 1,
      shipping_address: '321 Elm St, San Francisco, CA 94102',
      created_at: '2025-01-01T12:00:00Z',
      updated_at: '2025-01-02T16:45:00Z'
    },
    {
      id: 1005,
      order_number: 'ORD-2025-001005',
      customer: {
        id: 5,
        name: 'David Brown',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40'
      },
      status: 'cancelled',
      payment_status: 'refunded',
      total: 145.00,
      items_count: 4,
      shipping_address: '654 Maple Dr, Sydney, NSW 2000',
      created_at: '2024-12-30T09:15:00Z',
      updated_at: '2024-12-31T14:20:00Z'
    }
  ];

  // Filter orders
  let filteredOrders = allOrders;

  if (status) {
    filteredOrders = filteredOrders.filter(order => order.status === status);
  }

  if (search) {
    filteredOrders = filteredOrders.filter(order =>
      order.order_number.toLowerCase().includes(search.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(search.toLowerCase()) ||
      order.customer.email.toLowerCase().includes(search.toLowerCase())
    );
  }

  // Paginate results
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: paginatedOrders,
    pagination: {
      current_page: page,
      per_page: limit,
      total: filteredOrders.length,
      total_pages: Math.ceil(filteredOrders.length / limit)
    }
  });
});

app.get('/api/orders/:id', requireAuth, (req, res) => {
  const orderId = parseInt(req.params.id);

  const orderDetails = {
    id: orderId,
    order_number: `ORD-2025-00${orderId}`,
    customer: {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      avatar: 'https://via.placeholder.com/40'
    },
    status: 'pending',
    payment_status: 'paid',
    payment_method: 'Credit Card',
    subtotal: 140.00,
    tax: 11.20,
    shipping: 5.79,
    total: 156.99,
    items: [
      {
        id: 1,
        name: 'Wireless Bluetooth Headphones',
        sku: 'WBH-001',
        price: 79.99,
        quantity: 1,
        total: 79.99,
        image: 'https://via.placeholder.com/60'
      },
      {
        id: 2,
        name: 'Organic Cotton T-Shirt',
        sku: 'OCT-002',
        price: 29.99,
        quantity: 2,
        total: 59.98,
        image: 'https://via.placeholder.com/60'
      }
    ],
    shipping_address: {
      name: 'John Doe',
      address_line_1: '123 Main St',
      address_line_2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA'
    },
    billing_address: {
      name: 'John Doe',
      address_line_1: '123 Main St',
      address_line_2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA'
    },
    tracking_number: 'TRK123456789',
    notes: 'Customer requested expedited shipping',
    created_at: '2025-01-04T10:30:00Z',
    updated_at: '2025-01-04T10:30:00Z'
  };

  res.json({
    success: true,
    data: orderDetails
  });
});

app.put('/api/orders/:id/status', requireAuth, (req, res) => {
  const orderId = req.params.id;
  const { status } = req.body;

  res.json({
    success: true,
    message: `Order ${orderId} status updated to ${status}`,
    data: { id: orderId, status }
  });
});

// Analytics endpoints
app.get('/api/analytics/overview', requireAuth, (req, res) => {
  const analyticsData = {
    revenue: {
      today: 2456.78,
      yesterday: 2134.56,
      this_week: 15678.90,
      last_week: 14234.67,
      this_month: 67890.12,
      last_month: 59876.54,
      this_year: 456789.01,
      last_year: 398765.43
    },
    orders: {
      today: 23,
      yesterday: 19,
      this_week: 156,
      last_week: 142,
      this_month: 678,
      last_month: 612,
      pending: 45,
      processing: 23,
      shipped: 89,
      delivered: 234
    },
    customers: {
      total: 1234,
      new_today: 5,
      new_this_week: 34,
      new_this_month: 156,
      active_customers: 567,
      returning_customers: 345
    },
    products: {
      total: 89,
      published: 76,
      draft: 13,
      out_of_stock: 8,
      low_stock: 15
    },
    traffic: {
      page_views: 12345,
      unique_visitors: 3456,
      bounce_rate: 45.6,
      avg_session_duration: 245,
      conversion_rate: 3.2
    }
  };

  res.json({
    success: true,
    data: analyticsData
  });
});

app.get('/api/analytics/sales-chart', requireAuth, (req, res) => {
  const period = req.query.period || '7days';

  let chartData = [];

  if (period === '7days') {
    chartData = [
      { date: '2025-01-04', sales: 2456.78, orders: 23 },
      { date: '2025-01-03', sales: 2134.56, orders: 19 },
      { date: '2025-01-02', sales: 1876.43, orders: 17 },
      { date: '2025-01-01', sales: 2987.65, orders: 28 },
      { date: '2024-12-31', sales: 3456.78, orders: 32 },
      { date: '2024-12-30', sales: 2234.56, orders: 21 },
      { date: '2024-12-29', sales: 1987.65, orders: 18 }
    ];
  } else if (period === '30days') {
    // Generate 30 days of sample data
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      chartData.push({
        date: date.toISOString().split('T')[0],
        sales: Math.random() * 3000 + 1000,
        orders: Math.floor(Math.random() * 30) + 10
      });
    }
  }

  res.json({
    success: true,
    data: chartData.reverse()
  });
});

app.get('/api/analytics/top-products', requireAuth, (req, res) => {
  const topProducts = [
    {
      id: 1,
      name: 'Wireless Bluetooth Headphones',
      sales: 156,
      revenue: 12467.44,
      image: 'https://via.placeholder.com/50'
    },
    {
      id: 2,
      name: 'Smart Fitness Watch',
      sales: 89,
      revenue: 13349.11,
      image: 'https://via.placeholder.com/50'
    },
    {
      id: 3,
      name: 'Premium Leather Wallet',
      sales: 67,
      revenue: 4019.33,
      image: 'https://via.placeholder.com/50'
    },
    {
      id: 4,
      name: 'Organic Cotton T-Shirt',
      sales: 234,
      revenue: 7016.66,
      image: 'https://via.placeholder.com/50'
    },
    {
      id: 5,
      name: 'Eco-Friendly Water Bottle',
      sales: 123,
      revenue: 3073.77,
      image: 'https://via.placeholder.com/50'
    }
  ];

  res.json({
    success: true,
    data: topProducts
  });
});

// Settings endpoints
app.get('/api/settings', requireAuth, (req, res) => {
  const settings = {
    general: {
      site_name: 'E-Commerce Store',
      site_description: 'Your premier online shopping destination',
      site_url: 'https://yourstore.com',
      admin_email: '<EMAIL>',
      timezone: 'UTC',
      date_format: 'Y-m-d',
      time_format: '24',
      language: 'en',
      currency: 'USD',
      currency_symbol: '$',
      currency_position: 'left'
    },
    email: {
      smtp_host: 'smtp.gmail.com',
      smtp_port: 587,
      smtp_username: '<EMAIL>',
      smtp_password: '••••••••',
      smtp_encryption: 'tls',
      from_name: 'E-Commerce Store',
      from_email: '<EMAIL>'
    },
    payment: {
      stripe_enabled: true,
      stripe_public_key: 'pk_test_••••••••',
      stripe_secret_key: 'sk_test_••••••••',
      paypal_enabled: true,
      paypal_client_id: 'your-paypal-client-id',
      paypal_secret: '••••••••',
      payment_currency: 'USD',
      tax_rate: 8.5,
      shipping_enabled: true,
      free_shipping_threshold: 50.00
    },
    notifications: {
      email_notifications: true,
      sms_notifications: false,
      push_notifications: true,
      order_notifications: true,
      inventory_alerts: true,
      low_stock_threshold: 10,
      newsletter_enabled: true,
      marketing_emails: true
    },
    security: {
      two_factor_auth: false,
      session_timeout: 24,
      password_min_length: 8,
      require_email_verification: true,
      login_attempts_limit: 5,
      account_lockout_duration: 30,
      ssl_enabled: true,
      force_https: true
    },
    seo: {
      meta_title: 'E-Commerce Store - Shop Online',
      meta_description: 'Discover amazing products at great prices',
      meta_keywords: 'ecommerce, shopping, online store',
      google_analytics_id: 'GA-XXXXXXXXX',
      facebook_pixel_id: '',
      sitemap_enabled: true,
      robots_txt_enabled: true
    }
  };

  res.json({
    success: true,
    data: settings
  });
});

app.put('/api/settings', requireAuth, (req, res) => {
  const { category, settings } = req.body;

  // In a real app, you would save to database
  console.log(`Updating ${category} settings:`, settings);

  res.json({
    success: true,
    message: `${category} settings updated successfully`,
    data: settings
  });
});

app.post('/api/settings/test-email', requireAuth, (req, res) => {
  const { email } = req.body;

  // Simulate email test
  setTimeout(() => {
    res.json({
      success: true,
      message: `Test email sent successfully to ${email}`
    });
  }, 1000);
});

// Catch all other routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    requested_url: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Mock Backend Server running on http://localhost:${PORT}`);
  console.log(`📊 Dashboard API available at http://localhost:${PORT}/api/dashboard/stats`);
  console.log(`🔐 Auth API available at http://localhost:${PORT}/api/auth/login`);
  console.log(`📈 Analytics API available at http://localhost:${PORT}/api/analytics/sales-chart`);
  console.log('');
  console.log('✅ Backend is ready! Now start the frontend with: cd frontend && npm start');
});

module.exports = app;
