{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { provideHttpClient } from '@angular/common/http';\nimport { importProvidersFrom } from '@angular/core';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nconst routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./app/features/dashboard/dashboard.component').then(m => m.DashboardComponent)\n}, {\n  path: 'login',\n  loadComponent: () => import('./app/features/auth/login/login.component').then(m => m.LoginComponent)\n}];\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideAnimations(), provideHttpClient(), importProvidersFrom(MatSnackBarModule)]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "provideRouter", "provideAnimations", "provideHttpClient", "importProvidersFrom", "MatSnackBarModule", "routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "DashboardComponent", "LoginComponent", "providers", "catch", "err", "console", "error"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\amit\\frontend\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { provideHttpClient } from '@angular/common/http';\nimport { importProvidersFrom } from '@angular/core';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\n\nconst routes = [\n  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n  { path: 'dashboard', loadComponent: () => import('./app/features/dashboard/dashboard.component').then(m => m.DashboardComponent) },\n  { path: 'login', loadComponent: () => import('./app/features/auth/login/login.component').then(m => m.LoginComponent) }\n];\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes),\n    provideAnimations(),\n    provideHttpClient(),\n    importProvidersFrom(MatSnackBarModule)\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D,MAAMC,MAAM,GAAG,CACb;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,YAAY;EAAEC,SAAS,EAAE;AAAM,CAAE,EACzD;EAAEF,IAAI,EAAE,WAAW;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB;AAAC,CAAE,EAClI;EAAEN,IAAI,EAAE,OAAO;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,cAAc;AAAC,CAAE,CACxH;AAEDf,oBAAoB,CAACC,YAAY,EAAE;EACjCe,SAAS,EAAE,CACTd,aAAa,CAACK,MAAM,CAAC,EACrBJ,iBAAiB,EAAE,EACnBC,iBAAiB,EAAE,EACnBC,mBAAmB,CAACC,iBAAiB,CAAC;CAEzC,CAAC,CAACW,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}