import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './core/services/auth.service';
import { ThemeService } from './core/services/theme.service';
import { LoadingService } from './core/services/loading.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'E-Commerce Admin Dashboard';
  isLoading$ = this.loadingService.loading$;

  constructor(
    private authService: AuthService,
    private themeService: ThemeService,
    private loadingService: LoadingService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Initialize theme
    this.themeService.initializeTheme();
    
    // Check authentication status
    this.checkAuthStatus();
  }

  private checkAuthStatus(): void {
    if (this.authService.isAuthenticated()) {
      // User is authenticated, load user data
      this.authService.getCurrentUser().subscribe({
        next: (user) => {
          console.log('User authenticated:', user);
        },
        error: (error) => {
          console.error('Authentication check failed:', error);
          this.authService.logout();
          this.router.navigate(['/auth/login']);
        }
      });
    } else {
      // User is not authenticated, redirect to login
      if (!this.router.url.includes('/auth')) {
        this.router.navigate(['/auth/login']);
      }
    }
  }
}
