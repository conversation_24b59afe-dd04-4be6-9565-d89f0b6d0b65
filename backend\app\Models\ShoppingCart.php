<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ShoppingCart extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total_amount',
        'coupon_id',
        'applied_taxes',
        'shipping_method',
        'expires_at'
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'applied_taxes' => 'array',
        'shipping_method' => 'array',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user that owns the cart.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the applied coupon.
     */
    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    /**
     * Get the cart items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(ShoppingCartItem::class, 'cart_id');
    }

    /**
     * Get the total items count.
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items()->sum('quantity');
    }

    /**
     * Get the total unique items count.
     */
    public function getTotalUniqueItemsAttribute(): int
    {
        return $this->items()->count();
    }

    /**
     * Add item to cart.
     */
    public function addItem(int $productId, int $quantity = 1, int $variationId = null, array $options = []): ShoppingCartItem
    {
        $product = Product::findOrFail($productId);
        $variation = $variationId ? ProductVariation::findOrFail($variationId) : null;
        
        // Check if item already exists
        $existingItem = $this->items()
            ->where('product_id', $productId)
            ->where('product_variation_id', $variationId)
            ->first();
        
        if ($existingItem) {
            $existingItem->increment('quantity', $quantity);
            $existingItem->update([
                'total_price' => $existingItem->quantity * $existingItem->unit_price
            ]);
            $item = $existingItem;
        } else {
            $unitPrice = $variation ? $variation->current_price : $product->current_price;
            
            $item = $this->items()->create([
                'product_id' => $productId,
                'product_variation_id' => $variationId,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $unitPrice * $quantity,
                'product_options' => $options,
            ]);
        }
        
        $this->recalculateTotal();
        
        return $item;
    }

    /**
     * Update item quantity.
     */
    public function updateItemQuantity(int $itemId, int $quantity): bool
    {
        $item = $this->items()->findOrFail($itemId);
        
        if ($quantity <= 0) {
            return $this->removeItem($itemId);
        }
        
        $item->update([
            'quantity' => $quantity,
            'total_price' => $quantity * $item->unit_price
        ]);
        
        $this->recalculateTotal();
        
        return true;
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(int $itemId): bool
    {
        $item = $this->items()->findOrFail($itemId);
        $item->delete();
        
        $this->recalculateTotal();
        
        return true;
    }

    /**
     * Clear all items from cart.
     */
    public function clearItems(): void
    {
        $this->items()->delete();
        $this->recalculateTotal();
    }

    /**
     * Apply coupon to cart.
     */
    public function applyCoupon(string $couponCode): array
    {
        $coupon = Coupon::where('code', $couponCode)
            ->where('is_active', true)
            ->where('starts_at', '<=', now())
            ->where('expires_at', '>=', now())
            ->first();
        
        if (!$coupon) {
            return ['success' => false, 'message' => 'Invalid or expired coupon'];
        }
        
        if (!$coupon->canBeUsedBy($this->user)) {
            return ['success' => false, 'message' => 'Coupon cannot be used by this user'];
        }
        
        if (!$coupon->isValidForCart($this)) {
            return ['success' => false, 'message' => 'Coupon is not valid for items in cart'];
        }
        
        $this->update(['coupon_id' => $coupon->id]);
        $this->recalculateTotal();
        
        return ['success' => true, 'message' => 'Coupon applied successfully'];
    }

    /**
     * Remove coupon from cart.
     */
    public function removeCoupon(): void
    {
        $this->update(['coupon_id' => null]);
        $this->recalculateTotal();
    }

    /**
     * Recalculate cart totals.
     */
    public function recalculateTotal(): void
    {
        $subtotal = $this->items()->sum('total_price');
        $discountAmount = 0;
        $taxAmount = 0;
        $shippingAmount = $this->shipping_amount ?? 0;
        
        // Apply coupon discount
        if ($this->coupon) {
            $discountAmount = $this->coupon->calculateDiscount($subtotal);
        }
        
        // Calculate tax (simplified - you might want more complex tax calculation)
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = $taxableAmount * 0.1; // 10% tax rate (configurable)
        
        $totalAmount = $subtotal - $discountAmount + $taxAmount + $shippingAmount;
        
        $this->update([
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }

    /**
     * Check if cart is empty.
     */
    public function isEmpty(): bool
    {
        return $this->items()->count() === 0;
    }

    /**
     * Check if cart has expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Extend cart expiration.
     */
    public function extendExpiration(int $hours = 24): void
    {
        $this->update(['expires_at' => now()->addHours($hours)]);
    }

    /**
     * Convert cart to order.
     */
    public function convertToOrder(array $orderData): Order
    {
        $order = Order::create(array_merge($orderData, [
            'user_id' => $this->user_id,
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'shipping_amount' => $this->shipping_amount,
            'discount_amount' => $this->discount_amount,
            'total_amount' => $this->total_amount,
            'coupon_code' => $this->coupon?->code,
            'coupon_discount' => $this->discount_amount,
        ]));
        
        // Create order items
        foreach ($this->items as $cartItem) {
            $order->items()->create([
                'product_id' => $cartItem->product_id,
                'product_variation_id' => $cartItem->product_variation_id,
                'quantity' => $cartItem->quantity,
                'unit_price' => $cartItem->unit_price,
                'total_price' => $cartItem->total_price,
                'product_name' => $cartItem->product->name,
                'product_sku' => $cartItem->product->sku,
                'product_options' => $cartItem->product_options,
            ]);
        }
        
        // Clear cart after order creation
        $this->clearItems();
        
        return $order;
    }

    /**
     * Scope for active carts.
     */
    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope for expired carts.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }
}
