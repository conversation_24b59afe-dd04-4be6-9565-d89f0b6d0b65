<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('module_permissions', function (Blueprint $table) {
            $table->id();
            $table->string('module_name'); // users, products, orders, etc.
            $table->string('page_name')->nullable(); // list, create, edit, view, etc.
            $table->string('permission_name'); // view_users, create_products, etc.
            $table->string('permission_group')->default('general'); // general, admin, super_admin
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            // Indexes
            $table->index(['module_name', 'is_active']);
            $table->index(['permission_group', 'is_active']);
            $table->index('permission_name');
            $table->unique(['module_name', 'page_name', 'permission_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('module_permissions');
    }
};

