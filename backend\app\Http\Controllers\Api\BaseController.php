<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class BaseController extends Controller
{
    /**
     * Success response method.
     *
     * @param mixed $result
     * @param string $message
     * @param int $code
     * @return JsonResponse
     */
    public function sendResponse($result, string $message = 'Success', int $code = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $result,
        ];

        return response()->json($response, $code);
    }

    /**
     * Error response method.
     *
     * @param string $error
     * @param array $errorMessages
     * @param int $code
     * @return JsonResponse
     */
    public function sendError(string $error, array $errorMessages = [], int $code = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $error,
        ];

        if (!empty($errorMessages)) {
            $response['errors'] = $errorMessages;
        }

        return response()->json($response, $code);
    }

    /**
     * Validation error response method.
     *
     * @param array $errors
     * @param string $message
     * @return JsonResponse
     */
    public function sendValidationError(array $errors, string $message = 'Validation Error'): JsonResponse
    {
        return $this->sendError($message, $errors, 422);
    }

    /**
     * Unauthorized response method.
     *
     * @param string $message
     * @return JsonResponse
     */
    public function sendUnauthorized(string $message = 'Unauthorized'): JsonResponse
    {
        return $this->sendError($message, [], 401);
    }

    /**
     * Forbidden response method.
     *
     * @param string $message
     * @return JsonResponse
     */
    public function sendForbidden(string $message = 'Forbidden'): JsonResponse
    {
        return $this->sendError($message, [], 403);
    }

    /**
     * Not found response method.
     *
     * @param string $message
     * @return JsonResponse
     */
    public function sendNotFound(string $message = 'Resource not found'): JsonResponse
    {
        return $this->sendError($message, [], 404);
    }

    /**
     * Paginated response method.
     *
     * @param mixed $data
     * @param string $message
     * @return JsonResponse
     */
    public function sendPaginatedResponse($data, string $message = 'Success'): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data->items(),
            'pagination' => [
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'per_page' => $data->perPage(),
                'total' => $data->total(),
                'from' => $data->firstItem(),
                'to' => $data->lastItem(),
                'has_more_pages' => $data->hasMorePages(),
            ],
        ];

        return response()->json($response, 200);
    }

    /**
     * Created response method.
     *
     * @param mixed $result
     * @param string $message
     * @return JsonResponse
     */
    public function sendCreated($result, string $message = 'Resource created successfully'): JsonResponse
    {
        return $this->sendResponse($result, $message, 201);
    }

    /**
     * Updated response method.
     *
     * @param mixed $result
     * @param string $message
     * @return JsonResponse
     */
    public function sendUpdated($result, string $message = 'Resource updated successfully'): JsonResponse
    {
        return $this->sendResponse($result, $message, 200);
    }

    /**
     * Deleted response method.
     *
     * @param string $message
     * @return JsonResponse
     */
    public function sendDeleted(string $message = 'Resource deleted successfully'): JsonResponse
    {
        return $this->sendResponse(null, $message, 200);
    }
}
