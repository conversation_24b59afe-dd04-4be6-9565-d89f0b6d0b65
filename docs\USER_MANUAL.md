# E-Commerce Management System - User Manual

## Table of Contents

1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [User Management](#user-management)
4. [Product Management](#product-management)
5. [Order Management](#order-management)
6. [Coupon Management](#coupon-management)
7. [Media Library](#media-library)
8. [Settings](#settings)
9. [Analytics](#analytics)
10. [Mobile App](#mobile-app)

## Getting Started

### System Requirements

**Admin Dashboard (Web)**
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection
- Screen resolution: 1024x768 or higher

**Mobile App**
- iOS 12.0+ or Android 8.0+
- Internet connection
- Camera access (for barcode scanning)
- Storage access (for image uploads)

### First Login

1. Open your web browser and navigate to the admin dashboard URL
2. Enter your login credentials provided by the system administrator
3. Click "Login" to access the dashboard

**Default Admin Credentials:**
- Email: <EMAIL>
- Password: admin123 (change immediately after first login)

### Changing Your Password

1. Click on your profile icon in the top-right corner
2. Select "Profile Settings"
3. Click "Change Password"
4. Enter your current password and new password
5. Click "Update Password"

## Dashboard Overview

The main dashboard provides an overview of your e-commerce system:

### Key Metrics
- **Total Users**: Number of registered customers
- **Total Orders**: All orders in the system
- **Total Revenue**: Sum of all completed orders
- **Total Products**: Number of products in catalog

### Quick Actions
- Add New Product
- Create User
- View Recent Orders
- Generate Reports

### Recent Activity
- Latest orders
- New user registrations
- Product updates
- System notifications

## User Management

### Viewing Users

1. Navigate to "Users" in the main menu
2. Use the search bar to find specific users
3. Apply filters to narrow down results:
   - Role (Customer, Editor, Admin)
   - Status (Active, Inactive)
   - Registration date

### Creating a New User

1. Click "Add New User" button
2. Fill in the required information:
   - **Name**: Full name of the user
   - **Email**: Valid email address (must be unique)
   - **Phone**: Contact number
   - **Address**: Physical address
   - **Password**: Secure password
   - **Role**: Select appropriate role
   - **Groups**: Assign to user groups (optional)
   - **Discount**: User-specific discount percentage

3. Click "Create User" to save

### User Roles and Permissions

**Super Admin**
- Full system access
- Can manage all users and settings
- Access to system configuration

**Admin**
- Manage products, orders, and customers
- View analytics and reports
- Cannot modify system settings

**Editor**
- Manage products and categories
- View orders (read-only)
- Limited user management

**Data Entry**
- Add and edit products
- Basic order viewing
- No user management access

**Accountant**
- View financial reports
- Manage transactions and invoices
- Read-only access to orders

### User Groups

User groups allow you to organize customers and apply group-specific settings:

1. Navigate to "Users" > "Groups"
2. Click "Create Group"
3. Enter group details:
   - **Name**: Group identifier
   - **Description**: Purpose of the group
   - **Color**: Visual identifier
   - **Discount**: Group-wide discount percentage

4. Assign users to groups during user creation or editing

## Product Management

### Product Catalog Structure

The system supports hierarchical categories:
- Main Categories (e.g., Electronics, Clothing)
- Sub-categories (e.g., Smartphones, Laptops)
- Sub-sub-categories (unlimited depth)

### Creating Categories

1. Navigate to "Products" > "Categories"
2. Click "Add Category"
3. Fill in category information:
   - **Name**: Category name
   - **Slug**: URL-friendly identifier (auto-generated)
   - **Description**: Category description
   - **Parent Category**: Select if this is a sub-category
   - **Image**: Category thumbnail
   - **SEO Settings**: Meta title, description, keywords

### Adding Products

1. Navigate to "Products" > "All Products"
2. Click "Add New Product"
3. Complete the product form:

#### Basic Information
- **Product Name**: Clear, descriptive name
- **Description**: Detailed product description
- **Short Description**: Brief summary for listings
- **SKU**: Unique product identifier
- **Category**: Select appropriate category

#### Pricing
- **Regular Price**: Standard selling price
- **Sale Price**: Discounted price (optional)
- **Cost Price**: Your cost (for profit calculations)

#### Inventory
- **Manage Stock**: Enable/disable stock tracking
- **Stock Quantity**: Current inventory level
- **Low Stock Threshold**: Alert level
- **Stock Status**: In Stock/Out of Stock

#### Images
- **Main Image**: Primary product photo
- **Gallery**: Additional product images
- Drag and drop to reorder images
- Add alt text for accessibility

#### Attributes and Variations
- **Color**: Available color options
- **Size**: Available sizes
- **Material**: Product material
- **Custom Attributes**: Add any additional attributes

The system automatically generates product variations based on attributes.

#### SEO Settings
- **Meta Title**: Search engine title
- **Meta Description**: Search engine description
- **Keywords**: Relevant search terms
- **URL Slug**: Product page URL

#### Additional Settings
- **Dimensions**: Length, width, height, weight
- **Upsells**: Related products to promote
- **Purchase Notes**: Special instructions
- **Status**: Draft or Published

### Product Operations

#### Duplicating Products
1. Find the product in the product list
2. Click the "Duplicate" button
3. The system creates a copy with "(Copy)" appended to the name
4. Edit the duplicated product as needed

#### Bulk Operations
1. Select multiple products using checkboxes
2. Choose an action from the "Bulk Actions" dropdown:
   - Update Status
   - Change Category
   - Update Prices
   - Delete Products
3. Click "Apply" to execute

## Order Management

### Order Workflow

1. **Pending**: New order received
2. **Processing**: Order being prepared
3. **Shipped**: Order dispatched
4. **Delivered**: Order completed
5. **Cancelled**: Order cancelled
6. **Refunded**: Payment refunded

### Viewing Orders

1. Navigate to "Orders"
2. Use filters to find specific orders:
   - Order status
   - Date range
   - Customer name
   - Payment method

### Processing Orders

1. Click on an order to view details
2. Review order information:
   - Customer details
   - Shipping address
   - Order items
   - Payment information

3. Update order status as needed
4. Add internal notes for tracking
5. Communicate with customer if required

### Order Communication

1. Open the order details page
2. Scroll to the "Communication" section
3. Type your message to the customer
4. Click "Send Message"
5. Customer receives email notification

### Printing Orders

1. Open order details
2. Click "Print Order" or "Print Invoice"
3. Choose format (PDF or direct print)
4. Print or save the document

## Coupon Management

### Creating Coupons

1. Navigate to "Marketing" > "Coupons"
2. Click "Create Coupon"
3. Configure coupon settings:

#### Basic Information
- **Title**: Coupon name
- **Code**: Unique coupon code
- **Description**: Internal description
- **Expiry Date**: When coupon expires

#### Discount Settings
- **Type**: Fixed amount or percentage
- **Amount**: Discount value
- **Free Shipping**: Include free shipping

#### Usage Restrictions
- **Minimum Amount**: Minimum cart value
- **Maximum Amount**: Maximum discount amount
- **Usage Limit**: Total usage limit
- **Usage Limit per User**: Per-customer limit

#### Targeting
- **Specific Users**: Target individual users
- **User Groups**: Target user groups
- **Products**: Apply to specific products
- **Categories**: Apply to product categories

#### Exclusions
- **Exclude Products**: Products not eligible
- **Exclude Categories**: Categories not eligible
- **Exclude User Groups**: Groups not eligible

### Managing Active Coupons

1. View all coupons in the coupon list
2. Toggle coupon status (Active/Inactive)
3. Monitor usage statistics
4. Edit or delete coupons as needed

## Media Library

### Uploading Files

1. Navigate to "Media" > "Library"
2. Click "Upload Files" or drag files to the upload area
3. Supported formats:
   - Images: JPG, PNG, GIF, WebP
   - Documents: PDF, DOC, DOCX
   - Maximum file size: 10MB

### Organizing Media

1. Create folders to organize files
2. Add alt text to images for accessibility
3. Use descriptive filenames
4. Tag files for easy searching

### Using Media in Products

1. When adding/editing products, click "Select Image"
2. Choose from existing media or upload new files
3. Crop or resize images as needed
4. Set as main image or add to gallery

## Settings

### General Settings

1. Navigate to "Settings" > "General"
2. Configure basic site information:
   - Site title and tagline
   - Admin email address
   - Default timezone
   - Date and time formats
   - Currency settings

### Design Settings

1. Navigate to "Settings" > "Design"
2. Customize appearance:
   - Upload logos (header, footer, mobile, favicon)
   - Set primary and secondary colors
   - Choose fonts
   - Configure theme settings

### Payment Settings

1. Navigate to "Settings" > "Payments"
2. Configure payment methods:
   - Google Pay UPI ID
   - Razorpay API keys
   - Cash on Delivery settings
   - Payment gateway webhooks

### Email Settings

1. Navigate to "Settings" > "Email"
2. Configure email delivery:
   - SMTP server settings
   - Email templates
   - Notification preferences

### Social Media

1. Navigate to "Settings" > "Social Media"
2. Add social media links:
   - Facebook, Twitter, Instagram
   - WhatsApp business number
   - YouTube channel

## Analytics

### Dashboard Analytics

The analytics dashboard provides insights into:
- Sales performance
- User behavior
- Product popularity
- Revenue trends

### User Analytics

Track user engagement:
- Session duration
- Page views
- User journey
- Registration trends

### Product Analytics

Monitor product performance:
- View counts
- Time spent on product pages
- Conversion rates
- Popular products

### Sales Reports

Generate detailed reports:
1. Navigate to "Analytics" > "Reports"
2. Select report type and date range
3. Apply filters as needed
4. Export reports in PDF or Excel format

## Mobile App

### Customer Features

**Product Browsing**
- Browse categories
- Search products
- View product details
- Add to favorites

**Shopping Cart**
- Add/remove items
- Apply coupons
- Calculate shipping
- Proceed to checkout

**Order Management**
- Place orders
- Track order status
- View order history
- Contact support

**User Profile**
- Update personal information
- Manage addresses
- Change password
- View purchase history

### Offline Support

The mobile app provides limited offline functionality:
- Browse previously viewed products
- View cached product images
- Access order history
- Sync data when connection restored

## Troubleshooting

### Common Issues

**Login Problems**
- Verify email and password
- Check caps lock
- Clear browser cache
- Contact administrator

**Image Upload Issues**
- Check file size (max 10MB)
- Verify file format
- Ensure stable internet connection
- Try different browser

**Performance Issues**
- Clear browser cache
- Disable browser extensions
- Check internet connection
- Use recommended browsers

### Getting Help

1. Check the FAQ section
2. Contact system administrator
3. Submit support ticket
4. Email: <EMAIL>
5. Phone: ******-567-8900

## Best Practices

### Product Management
- Use high-quality images
- Write detailed descriptions
- Keep inventory updated
- Use consistent naming conventions

### Order Processing
- Process orders promptly
- Communicate with customers
- Update order status regularly
- Handle returns professionally

### User Management
- Assign appropriate roles
- Regular security audits
- Monitor user activity
- Maintain user data privacy

### System Maintenance
- Regular backups
- Update passwords
- Monitor system performance
- Keep software updated

---

**Web Design and developed By RekTech**

For technical support, contact your system administrator or RekTech support team.
