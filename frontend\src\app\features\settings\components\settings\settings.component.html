<div class="settings-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>Settings</h1>
      <p>Configure your application settings and preferences</p>
    </div>
    <div class="header-actions">
      <button
        mat-stroked-button
        (click)="clearCache()"
        [disabled]="!canClearCache"
        matTooltip="Clear application cache"
      >
        <mat-icon>cached</mat-icon>
        Clear Cache
      </button>
      
      <button
        mat-stroked-button
        (click)="exportSettings()"
        [disabled]="!canExportSettings"
        matTooltip="Export settings to file"
      >
        <mat-icon>download</mat-icon>
        Export
      </button>
      
      <input
        #fileInput
        type="file"
        accept=".json"
        style="display: none"
        (change)="onFileSelected($event)"
      >
      <button
        mat-stroked-button
        (click)="fileInput.click()"
        [disabled]="!canImportSettings"
        matTooltip="Import settings from file"
      >
        <mat-icon>upload</mat-icon>
        Import
      </button>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading settings...</p>
  </div>

  <!-- Settings Content -->
  <div *ngIf="!loading" class="settings-content">
    <div class="settings-layout">
      <!-- Sidebar Navigation -->
      <div class="settings-sidebar">
        <mat-nav-list class="settings-nav">
          <mat-list-item
            *ngFor="let group of settingGroups"
            [class.selected]="isGroupSelected(group)"
            (click)="selectGroup(group)"
          >
            <mat-icon matListIcon [color]="getGroupColor(group.name)">
              {{ getGroupIcon(group.name) }}
            </mat-icon>
            <div matLine class="group-title">{{ group.label }}</div>
            <div matLine class="group-description">{{ group.description }}</div>
          </mat-list-item>
        </mat-nav-list>
      </div>

      <!-- Main Content -->
      <div class="settings-main">
        <div *ngIf="selectedGroup" class="settings-panel">
          <!-- Panel Header -->
          <div class="panel-header">
            <div class="panel-title">
              <mat-icon [color]="getGroupColor(selectedGroup.name)">
                {{ getGroupIcon(selectedGroup.name) }}
              </mat-icon>
              <div class="title-content">
                <h2>{{ selectedGroup.label }}</h2>
                <p>{{ selectedGroup.description }}</p>
              </div>
            </div>
            <div class="panel-actions">
              <button
                mat-stroked-button
                color="warn"
                (click)="resetGroupSettings()"
                [disabled]="!canResetSettings"
                matTooltip="Reset to default values"
              >
                <mat-icon>restore</mat-icon>
                Reset
              </button>
            </div>
          </div>

          <!-- Settings Form -->
          <div class="panel-content">
            <ng-container [ngSwitch]="selectedGroup.name">
              <!-- General Settings -->
              <app-general-settings
                *ngSwitchCase="'general'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-general-settings>

              <!-- E-commerce Settings -->
              <app-ecommerce-settings
                *ngSwitchCase="'ecommerce'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-ecommerce-settings>

              <!-- Email Settings -->
              <app-email-settings
                *ngSwitchCase="'email'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-email-settings>

              <!-- Payment Settings -->
              <app-payment-settings
                *ngSwitchCase="'payment'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-payment-settings>

              <!-- Shipping Settings -->
              <app-shipping-settings
                *ngSwitchCase="'shipping'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-shipping-settings>

              <!-- Tax Settings -->
              <app-tax-settings
                *ngSwitchCase="'tax'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-tax-settings>

              <!-- Security Settings -->
              <app-security-settings
                *ngSwitchCase="'security'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-security-settings>

              <!-- Advanced Settings -->
              <app-advanced-settings
                *ngSwitchCase="'advanced'"
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-advanced-settings>

              <!-- Default/Generic Settings -->
              <app-generic-settings
                *ngSwitchDefault
                [settings]="selectedGroup.settings"
                (settingsChange)="onSettingsUpdate($event)"
                [saving]="saving"
              ></app-generic-settings>
            </ng-container>
          </div>
        </div>

        <!-- No Group Selected -->
        <div *ngIf="!selectedGroup" class="no-selection">
          <mat-icon>settings</mat-icon>
          <h3>Select a Settings Group</h3>
          <p>Choose a settings group from the sidebar to configure your application.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Unsaved Changes Warning -->
  <div *ngIf="hasUnsavedChanges" class="unsaved-changes-warning">
    <mat-card class="warning-card">
      <mat-card-content>
        <div class="warning-content">
          <mat-icon color="warn">warning</mat-icon>
          <span>You have unsaved changes. Don't forget to save your settings.</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
