.dashboard-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  
  .welcome-section {
    h1 {
      margin: 0 0 8px 0;
      color: #333;
      font-weight: 600;
      font-size: 28px;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 16px;
    }
  }
  
  .header-actions {
    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  text-align: center;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }
  
  p {
    margin: 16px 0;
    color: #666;
    font-size: 16px;
  }
}

.dashboard-content {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
    
    .stat-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
      
      .stat-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        
        &.users-avatar {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.products-avatar {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.orders-avatar {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.revenue-avatar {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      mat-card-content {
        .stat-number {
          font-size: 32px;
          font-weight: 700;
          color: #333;
          margin-bottom: 16px;
        }
        
        .stat-details {
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .stat-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #666;
            
            &.warning {
              color: #ff9800;
            }
            
            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
    
    .chart-container {
      mat-card {
        height: 400px;
        
        mat-card-content {
          height: calc(100% - 80px);
          display: flex;
          align-items: center;
          justify-content: center;
          
          .chart-wrapper {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          .chart-placeholder {
            color: #666;
            font-style: italic;
            text-align: center;
          }
          
          .no-data {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            
            mat-icon {
              font-size: 48px;
              width: 48px;
              height: 48px;
              margin-bottom: 16px;
            }
            
            p {
              margin: 0;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  
  .activities-section {
    mat-card {
      .activities-list {
        max-height: 400px;
        overflow-y: auto;
        
        .activity-item {
          display: flex;
          align-items: flex-start;
          padding: 16px 0;
          border-bottom: 1px solid #eee;
          
          &:last-child {
            border-bottom: none;
          }
          
          .activity-icon {
            margin-right: 16px;
            margin-top: 4px;
            
            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
            }
          }
          
          .activity-content {
            flex: 1;
            
            .activity-description {
              margin: 0 0 8px 0;
              color: #333;
              font-size: 14px;
              line-height: 1.4;
            }
            
            .activity-meta {
              display: flex;
              gap: 16px;
              font-size: 12px;
              color: #999;
              
              .activity-user {
                font-weight: 500;
              }
            }
          }
        }
      }
      
      .no-activities {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px;
        color: #999;
        
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }
        
        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .welcome-section h1 {
      font-size: 24px;
    }
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
    
    .chart-container mat-card {
      height: 300px;
    }
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .stats-grid {
    gap: 16px;
  }
  
  .charts-section {
    gap: 16px;
  }
}
