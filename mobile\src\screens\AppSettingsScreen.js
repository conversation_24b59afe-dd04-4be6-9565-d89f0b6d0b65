import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  Modal
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

import UserService from '../services/UserService';
import LoadingSpinner from '../components/LoadingSpinner';
import PickerModal from '../components/PickerModal';
import { colors, spacing, typography } from '../theme';

const AppSettingsScreen = () => {
  const navigation = useNavigation();

  // State
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showThemePicker, setShowThemePicker] = useState(false);
  const [showLanguagePicker, setShowLanguagePicker] = useState(false);
  const [showCurrencyPicker, setShowCurrencyPicker] = useState(false);

  // Options
  const themeOptions = UserService.getThemeOptions();
  const languageOptions = UserService.getSupportedLanguages();
  const currencyOptions = UserService.getSupportedCurrencies();

  useEffect(() => {
    loadSettings();
  }, []);

  useEffect(() => {
    navigation.setOptions({
      title: 'App Settings',
      headerRight: () => (
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSettings}
          disabled={saving}
        >
          <Text style={styles.saveButtonText}>
            {saving ? 'Saving...' : 'Save'}
          </Text>
        </TouchableOpacity>
      )
    });
  }, [navigation, saving, settings]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const appSettings = await UserService.getAppSettings();
      setSettings(appSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Error', 'Failed to load settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      await UserService.saveAppSettings(settings);
      Alert.alert('Success', 'Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (path, value) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newSettings;
    });
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default values?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            setSettings(UserService.getDefaultAppSettings());
            Alert.alert('Success', 'Settings reset to defaults');
          }
        }
      ]
    );
  };

  const renderSection = (title, children) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderSwitchItem = (title, description, value, onValueChange) => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingTitle}>{title}</Text>
        {description && <Text style={styles.settingDescription}>{description}</Text>}
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: colors.border, true: colors.primary }}
        thumbColor={colors.background.primary}
      />
    </View>
  );

  const renderPickerItem = (title, description, value, onPress) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingTitle}>{title}</Text>
        {description && <Text style={styles.settingDescription}>{description}</Text>}
        <Text style={styles.settingValue}>{value}</Text>
      </View>
      <Icon name="chevron-right" size={24} color={colors.text.secondary} />
    </TouchableOpacity>
  );

  const getThemeLabel = (theme) => {
    const option = themeOptions.find(opt => opt.value === theme);
    return option ? option.label : theme;
  };

  const getLanguageLabel = (code) => {
    const option = languageOptions.find(opt => opt.code === code);
    return option ? option.name : code;
  };

  const getCurrencyLabel = (code) => {
    const option = currencyOptions.find(opt => opt.code === code);
    return option ? `${option.name} (${option.symbol})` : code;
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!settings) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color={colors.text.secondary} />
          <Text style={styles.errorText}>Failed to load settings</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Appearance */}
        {renderSection('Appearance', (
          <>
            {renderPickerItem(
              'Theme',
              'Choose your preferred app theme',
              getThemeLabel(settings.theme),
              () => setShowThemePicker(true)
            )}
            
            {renderPickerItem(
              'Language',
              'Select your preferred language',
              getLanguageLabel(settings.language),
              () => setShowLanguagePicker(true)
            )}
            
            {renderPickerItem(
              'Currency',
              'Choose your preferred currency',
              getCurrencyLabel(settings.currency),
              () => setShowCurrencyPicker(true)
            )}
          </>
        ))}

        {/* Notifications */}
        {renderSection('Notifications', (
          <>
            {renderSwitchItem(
              'Push Notifications',
              'Receive push notifications on your device',
              settings.notifications.push_enabled,
              (value) => updateSetting('notifications.push_enabled', value)
            )}
            
            {renderSwitchItem(
              'Email Notifications',
              'Receive notifications via email',
              settings.notifications.email_enabled,
              (value) => updateSetting('notifications.email_enabled', value)
            )}
            
            {renderSwitchItem(
              'Order Updates',
              'Get notified about order status changes',
              settings.notifications.order_updates,
              (value) => updateSetting('notifications.order_updates', value)
            )}
            
            {renderSwitchItem(
              'Promotions',
              'Receive promotional offers and deals',
              settings.notifications.promotions,
              (value) => updateSetting('notifications.promotions', value)
            )}
            
            {renderSwitchItem(
              'New Products',
              'Get notified about new product arrivals',
              settings.notifications.new_products,
              (value) => updateSetting('notifications.new_products', value)
            )}
            
            {renderSwitchItem(
              'Price Alerts',
              'Receive alerts when prices drop',
              settings.notifications.price_alerts,
              (value) => updateSetting('notifications.price_alerts', value)
            )}
          </>
        ))}

        {/* Privacy */}
        {renderSection('Privacy', (
          <>
            {renderSwitchItem(
              'Analytics',
              'Help improve the app by sharing usage data',
              settings.privacy.analytics,
              (value) => updateSetting('privacy.analytics', value)
            )}
            
            {renderSwitchItem(
              'Personalization',
              'Allow personalized recommendations',
              settings.privacy.personalization,
              (value) => updateSetting('privacy.personalization', value)
            )}
            
            {renderSwitchItem(
              'Location Tracking',
              'Use location for better shopping experience',
              settings.privacy.location_tracking,
              (value) => updateSetting('privacy.location_tracking', value)
            )}
            
            {renderSwitchItem(
              'Crash Reporting',
              'Automatically report app crashes',
              settings.privacy.crash_reporting,
              (value) => updateSetting('privacy.crash_reporting', value)
            )}
          </>
        ))}

        {/* Display */}
        {renderSection('Display', (
          <>
            {renderSwitchItem(
              'Show Prices',
              'Display product prices in listings',
              settings.display.show_prices,
              (value) => updateSetting('display.show_prices', value)
            )}
            
            {renderSwitchItem(
              'Show Stock',
              'Display stock availability',
              settings.display.show_stock,
              (value) => updateSetting('display.show_stock', value)
            )}
            
            {renderSwitchItem(
              'Grid View',
              'Use grid layout for product listings',
              settings.display.grid_view,
              (value) => updateSetting('display.grid_view', value)
            )}
            
            {renderSwitchItem(
              'Auto-play Videos',
              'Automatically play product videos',
              settings.display.auto_play_videos,
              (value) => updateSetting('display.auto_play_videos', value)
            )}
          </>
        ))}

        {/* Security */}
        {renderSection('Security', (
          <>
            {renderSwitchItem(
              'Biometric Login',
              'Use fingerprint or face recognition',
              settings.security.biometric_login,
              (value) => updateSetting('security.biometric_login', value)
            )}
            
            {renderSwitchItem(
              'Require Password for Purchases',
              'Ask for password when making purchases',
              settings.security.require_password_for_purchases,
              (value) => updateSetting('security.require_password_for_purchases', value)
            )}
          </>
        ))}

        {/* Reset Button */}
        <View style={styles.resetSection}>
          <TouchableOpacity style={styles.resetButton} onPress={resetToDefaults}>
            <Icon name="restore" size={20} color={colors.error} />
            <Text style={styles.resetButtonText}>Reset to Defaults</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Theme Picker Modal */}
      <PickerModal
        visible={showThemePicker}
        title="Select Theme"
        options={themeOptions.map(option => ({
          label: option.label,
          value: option.value,
          description: option.description
        }))}
        selectedValue={settings.theme}
        onSelect={(value) => {
          updateSetting('theme', value);
          setShowThemePicker(false);
        }}
        onClose={() => setShowThemePicker(false)}
      />

      {/* Language Picker Modal */}
      <PickerModal
        visible={showLanguagePicker}
        title="Select Language"
        options={languageOptions.map(option => ({
          label: option.name,
          value: option.code,
          description: option.nativeName
        }))}
        selectedValue={settings.language}
        onSelect={(value) => {
          updateSetting('language', value);
          setShowLanguagePicker(false);
        }}
        onClose={() => setShowLanguagePicker(false)}
      />

      {/* Currency Picker Modal */}
      <PickerModal
        visible={showCurrencyPicker}
        title="Select Currency"
        options={currencyOptions.map(option => ({
          label: `${option.name} (${option.symbol})`,
          value: option.code,
          description: option.code
        }))}
        selectedValue={settings.currency}
        onSelect={(value) => {
          updateSetting('currency', value);
          setShowCurrencyPicker(false);
        }}
        onClose={() => setShowCurrencyPicker(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  saveButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  saveButtonText: {
    fontSize: typography.body.fontSize,
    color: colors.primary,
    fontWeight: '500',
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.h4.fontSize,
    fontWeight: typography.h4.fontWeight,
    color: colors.text.primary,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    marginTop: spacing.md,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  settingTitle: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  settingValue: {
    fontSize: typography.caption.fontSize,
    color: colors.primary,
    marginTop: spacing.xs,
  },
  resetSection: {
    padding: spacing.lg,
    alignItems: 'center',
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.error,
  },
  resetButtonText: {
    fontSize: typography.body.fontSize,
    color: colors.error,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  errorText: {
    fontSize: typography.h3.fontSize,
    color: colors.text.secondary,
    marginTop: spacing.md,
  },
});

export default AppSettingsScreen;
