.address-list-container {
  padding: 24px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }

    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }

  .content {
    .address-section {
      margin-bottom: 32px;

      h3 {
        color: #666;
        font-weight: 500;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e0e0e0;
      }

      .cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 16px;
      }
    }

    .address-card {
      position: relative;
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      mat-card-header {
        mat-card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 500;

          .default-badge {
            background: #4caf50;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            letter-spacing: 0.5px;
          }
        }

        mat-card-subtitle {
          margin-top: 4px;
        }
      }

      mat-card-content {
        .address-details {
          p {
            margin: 4px 0;
            line-height: 1.4;

            &.company {
              font-weight: 500;
              color: #666;
            }

            &.phone {
              display: flex;
              align-items: center;
              gap: 4px;
              color: #666;

              mat-icon {
                font-size: 16px;
                width: 16px;
                height: 16px;
              }
            }
          }
        }
      }

      mat-card-actions {
        padding: 8px 16px 16px;
        display: flex;
        gap: 8px;

        button {
          mat-icon {
            margin-right: 4px;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }

    .type-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.badge-primary {
        background: #2196f3;
        color: white;
      }

      &.badge-success {
        background: #4caf50;
        color: white;
      }

      &.badge-info {
        background: #00bcd4;
        color: white;
      }

      &.badge-secondary {
        background: #9e9e9e;
        color: white;
      }
    }

    .empty-state {
      text-align: center;
      padding: 64px 24px;
      color: #666;

      .empty-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #ccc;
        margin-bottom: 16px;
      }

      h3 {
        margin: 16px 0 8px;
        color: #333;
      }

      p {
        margin-bottom: 24px;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
      }

      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }

    .table-view {
      .addresses-table {
        width: 100%;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .name-cell {
          strong {
            display: block;
            font-weight: 500;
          }

          small {
            color: #666;
            font-size: 12px;
          }
        }

        .address-cell {
          max-width: 300px;
          line-height: 1.4;
        }

        th {
          background: #f5f5f5;
          font-weight: 600;
          color: #333;
        }

        td {
          padding: 12px 16px;
        }

        tr:hover {
          background: #f9f9f9;
        }
      }
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 24px;
    color: #666;

    mat-spinner {
      margin-bottom: 16px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .address-list-container {
    padding: 16px;

    .header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      h2 {
        text-align: center;
      }
    }

    .content {
      .address-section {
        .cards-grid {
          grid-template-columns: 1fr;
        }
      }

      .address-card {
        mat-card-actions {
          flex-wrap: wrap;
          justify-content: center;

          button {
            flex: 1;
            min-width: 80px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .address-list-container {
    padding: 12px;

    .content {
      .address-card {
        mat-card-content {
          .address-details {
            font-size: 14px;
          }
        }

        mat-card-actions {
          button {
            font-size: 12px;
            padding: 8px 12px;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }
}
