{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor(router) {\n    this.router = router;\n    this.title = 'E-Commerce Admin Dashboard';\n  }\n  ngOnInit() {\n    console.log('App component initialized');\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 38,\n      vars: 1,\n      consts: [[1, \"app-container\"], [1, \"dashboard\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-number\"], [1, \"welcome-section\"], [\"href\", \"http://localhost:8001\", \"target\", \"_blank\"], [\"href\", \"http://localhost:4200\", \"target\", \"_blank\"], [\"onclick\", \"testBackend()\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 2)(5, \"div\", 3)(6, \"h3\");\n          i0.ɵɵtext(7, \"Total Products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 4);\n          i0.ɵɵtext(9, \"1,234\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 3)(11, \"h3\");\n          i0.ɵɵtext(12, \"Total Orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4);\n          i0.ɵɵtext(14, \"567\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 3)(16, \"h3\");\n          i0.ɵɵtext(17, \"Total Users\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 4);\n          i0.ɵɵtext(19, \"890\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 3)(21, \"h3\");\n          i0.ɵɵtext(22, \"Revenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 4);\n          i0.ɵɵtext(24, \"$12,345\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 5)(26, \"h2\");\n          i0.ɵɵtext(27, \"\\uD83C\\uDF89 Your E-Commerce System is Live!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"p\");\n          i0.ɵɵtext(29, \"Backend API: \");\n          i0.ɵɵelementStart(30, \"a\", 6);\n          i0.ɵɵtext(31, \"http://localhost:8001\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"p\");\n          i0.ɵɵtext(33, \"Frontend Dashboard: \");\n          i0.ɵɵelementStart(34, \"a\", 7);\n          i0.ɵɵtext(35, \"http://localhost:4200\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"button\", 8);\n          i0.ɵɵtext(37, \"Test Backend Connection\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.title);\n        }\n      },\n      dependencies: [CommonModule, MatToolbarModule, MatButtonModule, MatIconModule, MatProgressSpinnerModule],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 64px);\\n  background-color: #f5f5f5;\\n  padding: 20px;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsOEJBQUE7RUFDQSx5QkFBQTtFQUNBLGFBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5hcHAtY29udGFpbmVyIHtcbiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDY0cHgpO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xuICBwYWRkaW5nOiAyMHB4O1xufVxuXG4uc3BhY2VyIHtcbiAgZmxleDogMSAxIGF1dG87XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "AppComponent", "constructor", "router", "title", "ngOnInit", "console", "log", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\amit\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\amit\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, RouterOutlet } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'E-Commerce Admin Dashboard';\n\n  constructor(private router: Router) {}\n\n  ngOnInit(): void {\n    console.log('App component initialized');\n  }\n}\n", "<div class=\"app-container\">\n  <h1>{{title}}</h1>\n  <div class=\"dashboard\">\n    <div class=\"stats-grid\">\n      <div class=\"stat-card\">\n        <h3>Total Products</h3>\n        <div class=\"stat-number\">1,234</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Total Orders</h3>\n        <div class=\"stat-number\">567</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Total Users</h3>\n        <div class=\"stat-number\">890</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Revenue</h3>\n        <div class=\"stat-number\">$12,345</div>\n      </div>\n    </div>\n\n    <div class=\"welcome-section\">\n      <h2>🎉 Your E-Commerce System is Live!</h2>\n      <p>Backend API: <a href=\"http://localhost:8001\" target=\"_blank\">http://localhost:8001</a></p>\n      <p>Frontend Dashboard: <a href=\"http://localhost:4200\" target=\"_blank\">http://localhost:4200</a></p>\n      <button onclick=\"testBackend()\">Test Backend Connection</button>\n    </div>\n  </div>\n</div>\n\n<script>\nfunction testBackend() {\n  fetch('http://localhost:8001/api/dashboard/stats')\n    .then(response => response.json())\n    .then(data => {\n      alert('Backend connection successful! ' + JSON.stringify(data, null, 2));\n    })\n    .catch(error => {\n      alert('Backend connection failed: ' + error.message);\n    });\n}\n</script>\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;;;AAgB7E,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAF1B,KAAAC,KAAK,GAAG,4BAA4B;EAEC;EAErCC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAC1C;;;uBAPWN,YAAY,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZV,YAAY;MAAAW,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBvBb,EADF,CAAAe,cAAA,aAA2B,SACrB;UAAAf,EAAA,CAAAgB,MAAA,GAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAIZjB,EAHN,CAAAe,cAAA,aAAuB,aACG,aACC,SACjB;UAAAf,EAAA,CAAAgB,MAAA,qBAAc;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACvBjB,EAAA,CAAAe,cAAA,aAAyB;UAAAf,EAAA,CAAAgB,MAAA,YAAK;UAChChB,EADgC,CAAAiB,YAAA,EAAM,EAChC;UAEJjB,EADF,CAAAe,cAAA,cAAuB,UACjB;UAAAf,EAAA,CAAAgB,MAAA,oBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACrBjB,EAAA,CAAAe,cAAA,cAAyB;UAAAf,EAAA,CAAAgB,MAAA,WAAG;UAC9BhB,EAD8B,CAAAiB,YAAA,EAAM,EAC9B;UAEJjB,EADF,CAAAe,cAAA,cAAuB,UACjB;UAAAf,EAAA,CAAAgB,MAAA,mBAAW;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACpBjB,EAAA,CAAAe,cAAA,cAAyB;UAAAf,EAAA,CAAAgB,MAAA,WAAG;UAC9BhB,EAD8B,CAAAiB,YAAA,EAAM,EAC9B;UAEJjB,EADF,CAAAe,cAAA,cAAuB,UACjB;UAAAf,EAAA,CAAAgB,MAAA,eAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAChBjB,EAAA,CAAAe,cAAA,cAAyB;UAAAf,EAAA,CAAAgB,MAAA,eAAO;UAEpChB,EAFoC,CAAAiB,YAAA,EAAM,EAClC,EACF;UAGJjB,EADF,CAAAe,cAAA,cAA6B,UACvB;UAAAf,EAAA,CAAAgB,MAAA,oDAAkC;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC3CjB,EAAA,CAAAe,cAAA,SAAG;UAAAf,EAAA,CAAAgB,MAAA,qBAAa;UAAAhB,EAAA,CAAAe,cAAA,YAAgD;UAAAf,EAAA,CAAAgB,MAAA,6BAAqB;UAAIhB,EAAJ,CAAAiB,YAAA,EAAI,EAAI;UAC7FjB,EAAA,CAAAe,cAAA,SAAG;UAAAf,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAe,cAAA,YAAgD;UAAAf,EAAA,CAAAgB,MAAA,6BAAqB;UAAIhB,EAAJ,CAAAiB,YAAA,EAAI,EAAI;UACpGjB,EAAA,CAAAe,cAAA,iBAAgC;UAAAf,EAAA,CAAAgB,MAAA,+BAAuB;UAG7DhB,EAH6D,CAAAiB,YAAA,EAAS,EAC5D,EACF,EACF;;;UA5BAjB,EAAA,CAAAkB,SAAA,GAAS;UAATlB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAAlB,KAAA,CAAS;;;qBDWXR,YAAY,EAEZC,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,wBAAwB;MAAA4B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}