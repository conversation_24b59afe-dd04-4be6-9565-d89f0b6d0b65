<?php

namespace App\Http\Controllers\Api;

use App\Models\Transaction;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class TransactionController extends BaseController
{
    /**
     * Display a listing of transactions.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $type = $request->get('type');
        $status = $request->get('status');
        $paymentMethod = $request->get('payment_method');
        $gateway = $request->get('gateway');
        $userId = $request->get('user_id');
        $orderId = $request->get('order_id');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $amountMin = $request->get('amount_min');
        $amountMax = $request->get('amount_max');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Transaction::with([
            'order:id,order_number,total_amount',
            'user:id,name,email',
            'creator:id,name'
        ]);

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('gateway_transaction_id', 'like', "%{$search}%")
                  ->orWhere('reference', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('order', function ($orderQuery) use ($search) {
                      $orderQuery->where('order_number', 'like', "%{$search}%");
                  })
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by type
        if ($type) {
            $query->type($type);
        }

        // Filter by status
        if ($status) {
            $query->status($status);
        }

        // Filter by payment method
        if ($paymentMethod) {
            $query->paymentMethod($paymentMethod);
        }

        // Filter by gateway
        if ($gateway) {
            $query->gateway($gateway);
        }

        // Filter by user
        if ($userId) {
            $query->where('user_id', $userId);
        }

        // Filter by order
        if ($orderId) {
            $query->where('order_id', $orderId);
        }

        // Filter by date range
        if ($dateFrom && $dateTo) {
            $query->dateRange($dateFrom, $dateTo);
        } elseif ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        } elseif ($dateTo) {
            $query->where('created_at', '<=', $dateTo);
        }

        // Filter by amount range
        if ($amountMin && $amountMax) {
            $query->amountRange($amountMin, $amountMax);
        } elseif ($amountMin) {
            $query->where('amount', '>=', $amountMin);
        } elseif ($amountMax) {
            $query->where('amount', '<=', $amountMax);
        }

        // Sorting
        $allowedSortFields = ['transaction_id', 'type', 'status', 'amount', 'payment_method', 'gateway', 'created_at', 'processed_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $transactions = $query->paginate($perPage);

        // Transform transaction data
        $transactions->getCollection()->transform(function ($transaction) {
            return [
                'id' => $transaction->id,
                'transaction_id' => $transaction->transaction_id,
                'order' => $transaction->order ? [
                    'id' => $transaction->order->id,
                    'order_number' => $transaction->order->order_number,
                    'total_amount' => $transaction->order->total_amount,
                ] : null,
                'user' => $transaction->user ? [
                    'id' => $transaction->user->id,
                    'name' => $transaction->user->name,
                    'email' => $transaction->user->email,
                ] : null,
                'type' => $transaction->type,
                'type_color' => $transaction->type_color,
                'status' => $transaction->status,
                'status_color' => $transaction->status_color,
                'payment_method' => $transaction->payment_method,
                'gateway' => $transaction->gateway,
                'gateway_transaction_id' => $transaction->gateway_transaction_id,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'fee' => $transaction->fee,
                'net_amount' => $transaction->net_amount,
                'formatted_amount' => $transaction->formatted_amount,
                'formatted_fee' => $transaction->formatted_fee,
                'formatted_net_amount' => $transaction->formatted_net_amount,
                'reference' => $transaction->reference,
                'description' => $transaction->description,
                'failure_reason' => $transaction->failure_reason,
                'is_successful' => $transaction->is_successful,
                'is_pending' => $transaction->is_pending,
                'is_failed' => $transaction->is_failed,
                'can_be_refunded' => $transaction->can_be_refunded,
                'processed_at' => $transaction->processed_at,
                'creator' => $transaction->creator ? $transaction->creator->name : null,
                'created_at' => $transaction->created_at,
                'updated_at' => $transaction->updated_at,
            ];
        });

        return $this->sendPaginatedResponse($transactions);
    }

    /**
     * Store a newly created transaction.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'nullable|integer|exists:orders,id',
            'user_id' => 'nullable|integer|exists:users,id',
            'type' => 'required|in:payment,refund,partial_refund,chargeback,adjustment',
            'status' => 'required|in:pending,processing,completed,failed,cancelled',
            'payment_method' => 'required|in:cod,razorpay,gpay,bank_transfer,wallet,upi',
            'gateway' => 'required|in:razorpay,stripe,paypal,manual',
            'gateway_transaction_id' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'fee' => 'nullable|numeric|min:0',
            'reference' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'gateway_response' => 'nullable|array',
            'failure_reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $transaction = Transaction::create([
                'order_id' => $request->order_id,
                'user_id' => $request->user_id,
                'type' => $request->type,
                'status' => $request->status,
                'payment_method' => $request->payment_method,
                'gateway' => $request->gateway,
                'gateway_transaction_id' => $request->gateway_transaction_id,
                'amount' => $request->amount,
                'currency' => $request->currency ?? config('app.currency', 'USD'),
                'fee' => $request->fee ?? 0,
                'reference' => $request->reference,
                'description' => $request->description,
                'gateway_response' => $request->gateway_response,
                'failure_reason' => $request->failure_reason,
                'created_by' => auth()->id(),
            ]);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($transaction)
                ->log('Transaction created');

            return $this->sendCreated([
                'id' => $transaction->id,
                'transaction_id' => $transaction->transaction_id,
                'type' => $transaction->type,
                'status' => $transaction->status,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
            ], 'Transaction created successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to create transaction: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified transaction.
     */
    public function show(string $id): JsonResponse
    {
        $transaction = Transaction::with([
            'order.items.product:id,name,slug',
            'user:id,name,email,phone',
            'creator:id,name'
        ])->find($id);

        if (!$transaction) {
            return $this->sendNotFound('Transaction not found');
        }

        return $this->sendResponse([
            'id' => $transaction->id,
            'transaction_id' => $transaction->transaction_id,
            'order' => $transaction->order ? [
                'id' => $transaction->order->id,
                'order_number' => $transaction->order->order_number,
                'total_amount' => $transaction->order->total_amount,
                'status' => $transaction->order->status,
                'payment_status' => $transaction->order->payment_status,
                'items_count' => $transaction->order->items->count(),
            ] : null,
            'user' => $transaction->user ? [
                'id' => $transaction->user->id,
                'name' => $transaction->user->name,
                'email' => $transaction->user->email,
                'phone' => $transaction->user->phone,
            ] : null,
            'type' => $transaction->type,
            'type_color' => $transaction->type_color,
            'status' => $transaction->status,
            'status_color' => $transaction->status_color,
            'payment_method' => $transaction->payment_method,
            'gateway' => $transaction->gateway,
            'gateway_transaction_id' => $transaction->gateway_transaction_id,
            'amount' => $transaction->amount,
            'currency' => $transaction->currency,
            'fee' => $transaction->fee,
            'net_amount' => $transaction->net_amount,
            'formatted_amount' => $transaction->formatted_amount,
            'formatted_fee' => $transaction->formatted_fee,
            'formatted_net_amount' => $transaction->formatted_net_amount,
            'reference' => $transaction->reference,
            'description' => $transaction->description,
            'gateway_response' => $transaction->gateway_response,
            'failure_reason' => $transaction->failure_reason,
            'is_successful' => $transaction->is_successful,
            'is_pending' => $transaction->is_pending,
            'is_failed' => $transaction->is_failed,
            'can_be_refunded' => $transaction->can_be_refunded,
            'processed_at' => $transaction->processed_at,
            'creator' => $transaction->creator ? [
                'id' => $transaction->creator->id,
                'name' => $transaction->creator->name,
            ] : null,
            'created_at' => $transaction->created_at,
            'updated_at' => $transaction->updated_at,
        ]);
    }

    /**
     * Update the specified transaction.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $transaction = Transaction::find($id);

        if (!$transaction) {
            return $this->sendNotFound('Transaction not found');
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,processing,completed,failed,cancelled',
            'gateway_transaction_id' => 'nullable|string|max:255',
            'fee' => 'nullable|numeric|min:0',
            'description' => 'nullable|string|max:1000',
            'gateway_response' => 'nullable|array',
            'failure_reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $transaction->update([
                'status' => $request->status,
                'gateway_transaction_id' => $request->gateway_transaction_id,
                'fee' => $request->fee ?? $transaction->fee,
                'description' => $request->description,
                'gateway_response' => $request->gateway_response ?
                    array_merge($transaction->gateway_response ?? [], $request->gateway_response) :
                    $transaction->gateway_response,
                'failure_reason' => $request->failure_reason,
            ]);

            // Recalculate net amount if fee changed
            if ($request->has('fee')) {
                $transaction->net_amount = $transaction->amount - $transaction->fee;
                $transaction->save();
            }

            // Update processed_at if status is completed
            if ($request->status === Transaction::STATUS_COMPLETED && !$transaction->processed_at) {
                $transaction->processed_at = now();
                $transaction->save();
            }

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($transaction)
                ->log('Transaction updated');

            return $this->sendUpdated([
                'id' => $transaction->id,
                'transaction_id' => $transaction->transaction_id,
                'status' => $transaction->status,
                'amount' => $transaction->amount,
                'net_amount' => $transaction->net_amount,
            ], 'Transaction updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update transaction: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Update transaction status.
     */
    public function updateStatus(Request $request, string $id): JsonResponse
    {
        $transaction = Transaction::find($id);

        if (!$transaction) {
            return $this->sendNotFound('Transaction not found');
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,processing,completed,failed,cancelled',
            'reason' => 'nullable|string|max:500',
            'gateway_response' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $transaction->updateStatus(
                $request->status,
                $request->reason,
                $request->gateway_response
            );

            return $this->sendResponse([
                'id' => $transaction->id,
                'transaction_id' => $transaction->transaction_id,
                'status' => $transaction->status,
                'status_color' => $transaction->status_color,
            ], 'Transaction status updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update transaction status: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Create a refund for the transaction.
     */
    public function refund(Request $request, string $id): JsonResponse
    {
        $transaction = Transaction::find($id);

        if (!$transaction) {
            return $this->sendNotFound('Transaction not found');
        }

        if (!$transaction->can_be_refunded) {
            return $this->sendError('This transaction cannot be refunded');
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01|max:' . $transaction->amount,
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        DB::beginTransaction();
        try {
            $refund = $transaction->createRefund(
                $request->amount,
                $request->reason
            );

            // Mark refund as completed (in real implementation, this would be done by payment gateway)
            $refund->updateStatus(Transaction::STATUS_COMPLETED);

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($transaction)
                ->withProperties([
                    'refund_id' => $refund->id,
                    'refund_amount' => $request->amount,
                    'reason' => $request->reason,
                ])
                ->log('Transaction refunded');

            return $this->sendCreated([
                'refund_transaction' => [
                    'id' => $refund->id,
                    'transaction_id' => $refund->transaction_id,
                    'type' => $refund->type,
                    'status' => $refund->status,
                    'amount' => $refund->amount,
                ],
                'original_transaction' => [
                    'id' => $transaction->id,
                    'transaction_id' => $transaction->transaction_id,
                    'status' => $transaction->status,
                ],
            ], 'Refund created successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to create refund: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get transaction statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = Transaction::getStatistics();

            return $this->sendResponse($stats);

        } catch (\Exception $e) {
            return $this->sendError('Failed to get transaction statistics: ' . $e->getMessage(), [], 500);
        }
    }
}
