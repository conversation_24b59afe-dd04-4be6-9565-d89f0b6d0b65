<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Coupon extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'usage_limit_per_user',
        'used_count',
        'is_active',
        'starts_at',
        'expires_at',
        'created_by',
        'applies_to',
        'exclude_sale_items',
        'individual_use',
        'free_shipping',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'usage_limit' => 'integer',
        'usage_limit_per_user' => 'integer',
        'used_count' => 'integer',
        'is_active' => 'boolean',
        'exclude_sale_items' => 'boolean',
        'individual_use' => 'boolean',
        'free_shipping' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Coupon type constants.
     */
    const TYPE_PERCENTAGE = 'percentage';
    const TYPE_FIXED_CART = 'fixed_cart';
    const TYPE_FIXED_PRODUCT = 'fixed_product';

    /**
     * Applies to constants.
     */
    const APPLIES_TO_ALL = 'all';
    const APPLIES_TO_SPECIFIC_PRODUCTS = 'specific_products';
    const APPLIES_TO_SPECIFIC_CATEGORIES = 'specific_categories';

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'code', 'type', 'value', 'is_active'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($coupon) {
            if (empty($coupon->code)) {
                $coupon->code = static::generateUniqueCode();
            }
        });
    }

    /**
     * Get the user who created this coupon.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the orders that used this coupon.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'coupon_code', 'code');
    }

    /**
     * Get the products this coupon applies to.
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'coupon_products');
    }

    /**
     * Get the categories this coupon applies to.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'coupon_categories');
    }

    /**
     * Get the coupon usages.
     */
    public function usages(): HasMany
    {
        return $this->hasMany(CouponUsage::class);
    }

    /**
     * Check if coupon is currently valid.
     */
    public function getIsValidAttribute(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();

        // Check start date
        if ($this->starts_at && $now->lt($this->starts_at)) {
            return false;
        }

        // Check expiry date
        if ($this->expires_at && $now->gt($this->expires_at)) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Check if coupon has expired.
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && now()->gt($this->expires_at);
    }

    /**
     * Check if coupon has reached usage limit.
     */
    public function getIsUsageLimitReachedAttribute(): bool
    {
        return $this->usage_limit && $this->used_count >= $this->usage_limit;
    }

    /**
     * Get remaining usage count.
     */
    public function getRemainingUsageAttribute(): ?int
    {
        if (!$this->usage_limit) {
            return null;
        }

        return max(0, $this->usage_limit - $this->used_count);
    }

    /**
     * Get coupon status.
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->is_expired) {
            return 'expired';
        }

        if ($this->is_usage_limit_reached) {
            return 'used_up';
        }

        $now = now();
        if ($this->starts_at && $now->lt($this->starts_at)) {
            return 'scheduled';
        }

        return 'active';
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'success',
            'scheduled' => 'info',
            'expired' => 'warning',
            'used_up' => 'secondary',
            'inactive' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Validate coupon for a specific user and cart.
     */
    public function validateForUser(?int $userId = null, array $cartItems = []): array
    {
        $errors = [];

        // Check if coupon is valid
        if (!$this->is_valid) {
            if (!$this->is_active) {
                $errors[] = 'This coupon is not active';
            } elseif ($this->is_expired) {
                $errors[] = 'This coupon has expired';
            } elseif ($this->is_usage_limit_reached) {
                $errors[] = 'This coupon has reached its usage limit';
            } elseif ($this->starts_at && now()->lt($this->starts_at)) {
                $errors[] = 'This coupon is not yet active';
            }
        }

        // Check user-specific usage limit
        if ($userId && $this->usage_limit_per_user) {
            $userUsageCount = $this->usages()->where('user_id', $userId)->count();
            if ($userUsageCount >= $this->usage_limit_per_user) {
                $errors[] = 'You have reached the usage limit for this coupon';
            }
        }

        // Check minimum amount
        if ($this->minimum_amount && !empty($cartItems)) {
            $cartTotal = collect($cartItems)->sum(function ($item) {
                return $item['price'] * $item['quantity'];
            });

            if ($cartTotal < $this->minimum_amount) {
                $errors[] = "Minimum order amount of {$this->minimum_amount} required";
            }
        }

        // Check if coupon applies to cart items
        if (!empty($cartItems) && $this->applies_to !== self::APPLIES_TO_ALL) {
            $hasApplicableItems = $this->hasApplicableItems($cartItems);
            if (!$hasApplicableItems) {
                $errors[] = 'This coupon does not apply to any items in your cart';
            }
        }

        return $errors;
    }

    /**
     * Check if coupon has applicable items in cart.
     */
    private function hasApplicableItems(array $cartItems): bool
    {
        foreach ($cartItems as $item) {
            if ($this->appliesToItem($item)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if coupon applies to a specific item.
     */
    public function appliesToItem(array $item): bool
    {
        // If exclude sale items and item is on sale
        if ($this->exclude_sale_items && isset($item['is_on_sale']) && $item['is_on_sale']) {
            return false;
        }

        switch ($this->applies_to) {
            case self::APPLIES_TO_ALL:
                return true;

            case self::APPLIES_TO_SPECIFIC_PRODUCTS:
                return $this->products()->where('products.id', $item['product_id'])->exists();

            case self::APPLIES_TO_SPECIFIC_CATEGORIES:
                $product = Product::find($item['product_id']);
                return $product && $this->categories()->where('categories.id', $product->category_id)->exists();

            default:
                return false;
        }
    }

    /**
     * Calculate discount amount for given subtotal.
     */
    public function calculateDiscount(float $subtotal, array $cartItems = []): float
    {
        if ($subtotal <= 0) {
            return 0;
        }

        $discount = 0;

        switch ($this->type) {
            case self::TYPE_PERCENTAGE:
                $discount = ($subtotal * $this->value) / 100;
                break;

            case self::TYPE_FIXED_CART:
                $discount = $this->value;
                break;

            case self::TYPE_FIXED_PRODUCT:
                // Calculate discount for applicable items only
                $applicableTotal = 0;
                foreach ($cartItems as $item) {
                    if ($this->appliesToItem($item)) {
                        $applicableTotal += $item['price'] * $item['quantity'];
                    }
                }
                $discount = min($this->value, $applicableTotal);
                break;
        }

        // Apply maximum discount limit
        if ($this->maximum_discount && $discount > $this->maximum_discount) {
            $discount = $this->maximum_discount;
        }

        // Ensure discount doesn't exceed subtotal
        return min($discount, $subtotal);
    }

    /**
     * Record coupon usage.
     */
    public function recordUsage(?int $userId = null, ?int $orderId = null): void
    {
        // Create usage record
        $this->usages()->create([
            'user_id' => $userId,
            'order_id' => $orderId,
            'used_at' => now(),
        ]);

        // Increment usage count
        $this->increment('used_count');

        // Log activity
        activity()
            ->causedBy($userId ? User::find($userId) : null)
            ->performedOn($this)
            ->withProperties(['order_id' => $orderId])
            ->log('Coupon used');
    }

    /**
     * Scope for active coupons.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for valid coupons (active and not expired).
     */
    public function scopeValid($query)
    {
        $now = now();
        return $query->active()
            ->where(function ($q) use ($now) {
                $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>', $now);
            })
            ->where(function ($q) {
                $q->whereNull('usage_limit')->orWhereColumn('used_count', '<', 'usage_limit');
            });
    }

    /**
     * Scope for expired coupons.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Generate unique coupon code.
     */
    public static function generateUniqueCode(int $length = 8): string
    {
        do {
            $code = strtoupper(Str::random($length));
        } while (static::where('code', $code)->exists());

        return $code;
    }

    /**
     * Get coupon statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_coupons' => static::count(),
            'active_coupons' => static::active()->count(),
            'expired_coupons' => static::expired()->count(),
            'used_coupons' => static::where('used_count', '>', 0)->count(),
            'total_usage' => static::sum('used_count'),
            'total_discount_given' => static::join('orders', 'coupons.code', '=', 'orders.coupon_code')
                ->sum('orders.coupon_discount'),
        ];
    }
}
