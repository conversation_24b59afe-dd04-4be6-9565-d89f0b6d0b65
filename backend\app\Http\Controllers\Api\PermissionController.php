<?php

namespace App\Http\Controllers\Api;

use App\Models\ModulePermission;
use App\Models\UserGroupPermission;
use App\Models\UserPermission;
use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class PermissionController extends BaseController
{
    /**
     * Display a listing of permissions.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $module = $request->get('module');
        $group = $request->get('group');

        $query = ModulePermission::query();

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('permission_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('module_name', 'like', "%{$search}%");
            });
        }

        // Filter by module
        if ($module) {
            $query->where('module_name', $module);
        }

        // Filter by permission group
        if ($group) {
            $query->where('permission_group', $group);
        }

        $permissions = $query->active()
            ->ordered()
            ->paginate($perPage);

        return $this->sendResponse([
            'permissions' => $permissions->items(),
            'pagination' => [
                'current_page' => $permissions->currentPage(),
                'last_page' => $permissions->lastPage(),
                'per_page' => $permissions->perPage(),
                'total' => $permissions->total(),
            ],
            'modules' => ModulePermission::getAvailableModules(),
            'groups' => ModulePermission::getPermissionGroups(),
        ]);
    }

    /**
     * Store a newly created permission.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'module_name' => 'required|string|max:100',
            'page_name' => 'nullable|string|max:100',
            'permission_name' => 'required|string|max:255|unique:module_permissions',
            'permission_group' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $permission = ModulePermission::create($request->all());

            return $this->sendCreated($permission, 'Permission created successfully');
        } catch (\Exception $e) {
            return $this->sendError('Failed to create permission: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified permission.
     */
    public function show(string $id): JsonResponse
    {
        $permission = ModulePermission::find($id);

        if (!$permission) {
            return $this->sendNotFound('Permission not found');
        }

        return $this->sendResponse($permission);
    }

    /**
     * Update the specified permission.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $permission = ModulePermission::find($id);

        if (!$permission) {
            return $this->sendNotFound('Permission not found');
        }

        $validator = Validator::make($request->all(), [
            'module_name' => 'sometimes|string|max:100',
            'page_name' => 'nullable|string|max:100',
            'permission_name' => 'sometimes|string|max:255|unique:module_permissions,permission_name,' . $id,
            'permission_group' => 'sometimes|string|max:100',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $permission->update($request->all());

            return $this->sendResponse($permission, 'Permission updated successfully');
        } catch (\Exception $e) {
            return $this->sendError('Failed to update permission: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Remove the specified permission.
     */
    public function destroy(string $id): JsonResponse
    {
        $permission = ModulePermission::find($id);

        if (!$permission) {
            return $this->sendNotFound('Permission not found');
        }

        try {
            $permission->delete();

            return $this->sendResponse([], 'Permission deleted successfully');
        } catch (\Exception $e) {
            return $this->sendError('Failed to delete permission: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get available modules.
     */
    public function getModules(): JsonResponse
    {
        $modules = ModulePermission::getAvailableModules();

        return $this->sendResponse($modules);
    }

    /**
     * Get pages for a specific module.
     */
    public function getPages(string $module): JsonResponse
    {
        $pages = ModulePermission::getAvailablePages($module);

        return $this->sendResponse($pages);
    }

    /**
     * Assign permission to user.
     */
    public function assignToUser(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'permission_id' => 'required|integer|exists:module_permissions,id',
            'can_create' => 'boolean',
            'can_read' => 'boolean',
            'can_update' => 'boolean',
            'can_delete' => 'boolean',
            'can_export' => 'boolean',
            'can_import' => 'boolean',
            'is_granted' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $userPermission = UserPermission::updateOrCreate(
                [
                    'user_id' => $request->user_id,
                    'permission_id' => $request->permission_id,
                ],
                [
                    'can_create' => $request->get('can_create', false),
                    'can_read' => $request->get('can_read', false),
                    'can_update' => $request->get('can_update', false),
                    'can_delete' => $request->get('can_delete', false),
                    'can_export' => $request->get('can_export', false),
                    'can_import' => $request->get('can_import', false),
                    'is_granted' => $request->get('is_granted', true),
                    'granted_at' => now(),
                    'granted_by' => auth()->id(),
                ]
            );

            return $this->sendResponse($userPermission, 'Permission assigned to user successfully');
        } catch (\Exception $e) {
            return $this->sendError('Failed to assign permission: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Assign permission to user group.
     */
    public function assignToGroup(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_group_id' => 'required|integer|exists:user_groups,id',
            'permission_id' => 'required|integer|exists:module_permissions,id',
            'can_create' => 'boolean',
            'can_read' => 'boolean',
            'can_update' => 'boolean',
            'can_delete' => 'boolean',
            'can_export' => 'boolean',
            'can_import' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $groupPermission = UserGroupPermission::updateOrCreate(
                [
                    'user_group_id' => $request->user_group_id,
                    'permission_id' => $request->permission_id,
                ],
                [
                    'can_create' => $request->get('can_create', false),
                    'can_read' => $request->get('can_read', false),
                    'can_update' => $request->get('can_update', false),
                    'can_delete' => $request->get('can_delete', false),
                    'can_export' => $request->get('can_export', false),
                    'can_import' => $request->get('can_import', false),
                ]
            );

            return $this->sendResponse($groupPermission, 'Permission assigned to group successfully');
        } catch (\Exception $e) {
            return $this->sendError('Failed to assign permission: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get user permissions.
     */
    public function getUserPermissions(string $userId): JsonResponse
    {
        $user = User::find($userId);

        if (!$user) {
            return $this->sendNotFound('User not found');
        }

        $permissions = UserPermission::with(['permission'])
            ->where('user_id', $userId)
            ->get();

        return $this->sendResponse([
            'user' => $user->only(['id', 'name', 'email']),
            'permissions' => $permissions,
        ]);
    }

    /**
     * Get group permissions.
     */
    public function getGroupPermissions(string $groupId): JsonResponse
    {
        $group = UserGroup::find($groupId);

        if (!$group) {
            return $this->sendNotFound('User group not found');
        }

        $permissions = UserGroupPermission::with(['permission'])
            ->where('user_group_id', $groupId)
            ->get();

        return $this->sendResponse([
            'group' => $group->only(['id', 'name', 'description']),
            'permissions' => $permissions,
        ]);
    }
}
