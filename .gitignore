# Dependencies
node_modules/
vendor/

# Environment files
.env
.env.local
.env.production
.env.staging

# Build outputs
/backend/public/hot
/backend/public/storage
/backend/storage/*.key
/frontend/dist/
/mobile/dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Database
*.sqlite
*.db

# Laravel specific
/backend/bootstrap/compiled.php
/backend/app/storage/
/backend/storage/logs/
/backend/storage/framework/cache/
/backend/storage/framework/sessions/
/backend/storage/framework/views/

# Angular specific
/frontend/.angular/
/frontend/tmp/
/frontend/out-tsc/

# React Native specific
/mobile/.expo/
/mobile/android/app/build/
/mobile/ios/build/
/mobile/ios/Pods/

# Docker
docker-compose.override.yml

# Backup files
*.bak
*.backup
