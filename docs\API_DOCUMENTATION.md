# E-Commerce Management System - API Documentation

## Overview

This document provides comprehensive API documentation for the E-Commerce Management System backend. The API is built using <PERSON><PERSON> and follows RESTful principles with JWT authentication.

## Base URL

- **Development**: `http://localhost:8000/api`
- **Production**: `https://your-domain.com/api`

## Authentication

The API uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer {your_jwt_token}
```

### Authentication Endpoints

#### Login
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>",
      "roles": ["super-admin"],
      "permissions": ["*"]
    }
  }
}
```

#### Register
```http
POST /auth/register
```

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "phone": "+1234567890",
  "address": "123 Main St, City, Country"
}
```

#### Logout
```http
POST /auth/logout
```

#### Refresh Token
```http
POST /auth/refresh
```

#### Get Current User
```http
GET /auth/me
```

## User Management

### List Users
```http
GET /users?page=1&per_page=20&search=john&sort_by=name&sort_order=asc
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20)
- `search` (optional): Search term
- `sort_by` (optional): Sort field (name, email, created_at)
- `sort_order` (optional): Sort order (asc, desc)
- `filters[role]` (optional): Filter by role
- `filters[is_active]` (optional): Filter by active status

**Response:**
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "roles": ["customer"],
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 20,
    "total": 100,
    "from": 1,
    "to": 20,
    "has_more_pages": true
  }
}
```

### Create User
```http
POST /users
```

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+1234567891",
  "address": "456 Oak St, City, Country",
  "discount_percentage": 10.00,
  "roles": ["customer"],
  "groups": [1, 2]
}
```

### Update User
```http
PUT /users/{id}
```

### Delete User
```http
DELETE /users/{id}
```

### Assign Role to User
```http
POST /users/{id}/assign-role
```

**Request Body:**
```json
{
  "role": "editor"
}
```

### Assign User to Group
```http
POST /users/{id}/assign-group
```

**Request Body:**
```json
{
  "group_id": 1
}
```

## Product Management

### List Products
```http
GET /products?page=1&per_page=20&category_id=1&status=published
```

**Query Parameters:**
- `category_id` (optional): Filter by category
- `status` (optional): Filter by status (draft, published)
- `in_stock` (optional): Filter by stock status (true, false)
- `price_min` (optional): Minimum price filter
- `price_max` (optional): Maximum price filter

### Create Product
```http
POST /products
```

**Request Body:**
```json
{
  "name": "Sample Product",
  "description": "Product description",
  "short_description": "Short description",
  "sku": "PROD-001",
  "category_id": 1,
  "price": 99.99,
  "sale_price": 79.99,
  "stock_quantity": 100,
  "manage_stock": true,
  "status": "published",
  "featured": false,
  "meta_title": "SEO Title",
  "meta_description": "SEO Description",
  "meta_keywords": "keyword1, keyword2",
  "images": [
    {
      "url": "https://example.com/image1.jpg",
      "alt": "Product Image 1",
      "is_primary": true
    }
  ],
  "attributes": [
    {
      "name": "Color",
      "values": ["Red", "Blue", "Green"]
    },
    {
      "name": "Size",
      "values": ["S", "M", "L", "XL"]
    }
  ],
  "dimensions": {
    "length": 10.5,
    "width": 8.0,
    "height": 2.5,
    "weight": 0.5
  }
}
```

### Update Product
```http
PUT /products/{id}
```

### Delete Product
```http
DELETE /products/{id}
```

### Duplicate Product
```http
POST /products/{id}/duplicate
```

### Bulk Operations
```http
POST /products/bulk-action
```

**Request Body:**
```json
{
  "action": "update_status",
  "ids": [1, 2, 3],
  "data": {
    "status": "published"
  }
}
```

## Category Management

### List Categories
```http
GET /categories?parent_id=null
```

### Create Category
```http
POST /categories
```

**Request Body:**
```json
{
  "name": "Electronics",
  "slug": "electronics",
  "description": "Electronic products",
  "parent_id": null,
  "image": "https://example.com/category.jpg",
  "meta_title": "Electronics Category",
  "meta_description": "Browse our electronics collection",
  "is_active": true,
  "sort_order": 1
}
```

## Order Management

### List Orders
```http
GET /orders?status=pending&date_from=2024-01-01&date_to=2024-12-31
```

### Create Order
```http
POST /orders
```

**Request Body:**
```json
{
  "user_id": 1,
  "items": [
    {
      "product_id": 1,
      "quantity": 2,
      "price": 99.99
    }
  ],
  "shipping_address": {
    "name": "John Doe",
    "address": "123 Main St",
    "city": "City",
    "state": "State",
    "postal_code": "12345",
    "country": "Country"
  },
  "billing_address": {
    "name": "John Doe",
    "address": "123 Main St",
    "city": "City",
    "state": "State",
    "postal_code": "12345",
    "country": "Country"
  },
  "payment_method": "credit_card",
  "coupon_code": "SAVE10"
}
```

### Update Order Status
```http
POST /orders/{id}/status
```

**Request Body:**
```json
{
  "status": "processing",
  "note": "Order is being processed"
}
```

## Media Management

### Upload File
```http
POST /media/upload
```

**Request Body (multipart/form-data):**
- `file`: File to upload
- `folder` (optional): Folder name
- `alt_text` (optional): Alt text for images

### List Media Files
```http
GET /media?type=image&folder=products
```

### Delete Media File
```http
DELETE /media/{id}
```

## Settings Management

### Get All Settings
```http
GET /settings
```

### Update Settings
```http
POST /settings
```

**Request Body:**
```json
{
  "site_title": "My E-Commerce Store",
  "site_tagline": "Best products online",
  "admin_email": "<EMAIL>",
  "currency": "USD",
  "timezone": "UTC",
  "theme_primary_color": "#3f51b5",
  "theme_secondary_color": "#ff4081"
}
```

### Get Settings Group
```http
GET /settings/general
```

## Analytics

### Dashboard Analytics
```http
GET /analytics/dashboard
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_users": 1250,
    "total_orders": 3420,
    "total_revenue": 125000.50,
    "total_products": 450,
    "recent_orders": [...],
    "top_products": [...],
    "revenue_chart": [...],
    "user_growth": [...]
  }
}
```

### User Analytics
```http
GET /analytics/users?period=30d
```

### Product Analytics
```http
GET /analytics/products?period=7d
```

### Order Analytics
```http
GET /analytics/orders?period=1y
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API requests are limited to 60 requests per minute per user. Rate limit headers are included in responses:

- `X-RateLimit-Limit`: Request limit per minute
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Time when limit resets

## Pagination

Paginated endpoints return data in the following format:

```json
{
  "data": [...],
  "pagination": {
    "current_page": 1,
    "last_page": 10,
    "per_page": 20,
    "total": 200,
    "from": 1,
    "to": 20,
    "has_more_pages": true
  }
}
```

## File Uploads

File uploads support the following formats:

**Images**: JPG, JPEG, PNG, GIF, WebP (max 10MB)
**Documents**: PDF, DOC, DOCX (max 10MB)

Upload responses include file information:

```json
{
  "success": true,
  "data": {
    "id": 1,
    "filename": "product-image.jpg",
    "url": "https://example.com/storage/uploads/product-image.jpg",
    "size": 1024000,
    "mime_type": "image/jpeg",
    "alt_text": "Product Image"
  }
}
```
