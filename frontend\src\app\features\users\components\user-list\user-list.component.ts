import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SelectionModel } from '@angular/cdk/collections';
import { Router } from '@angular/router';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

import { UserService, User, Role, UserFilters } from '../../services/user.service';
import { LoadingService } from '../../../../core/services/loading.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { UserFormDialogComponent } from '../user-form-dialog/user-form-dialog.component';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'select',
    'profile_image',
    'name',
    'email',
    'role',
    'status',
    'email_verified',
    'last_login',
    'orders_count',
    'total_spent',
    'created_at',
    'actions'
  ];

  dataSource = new MatTableDataSource<User>();
  selection = new SelectionModel<User>(true, []);
  
  // Filters
  filters: UserFilters = {
    per_page: 20,
    page: 1,
    sort_by: 'created_at',
    sort_order: 'desc'
  };

  roles: Role[] = [];
  searchSubject = new Subject<string>();
  
  // Pagination
  totalItems = 0;
  pageSize = 20;
  pageSizeOptions = [10, 20, 50, 100];

  // Loading states
  loading = false;
  bulkActionLoading = false;

  // Filter options
  roleOptions: { value: string; label: string; color: string }[] = [];
  statusOptions: { value: boolean | string; label: string }[] = [];
  emailVerificationOptions: { value: boolean | string; label: string }[] = [];

  constructor(
    private userService: UserService,
    private loadingService: LoadingService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeFilterOptions();
    this.loadRoles();
    this.loadUsers();
    this.setupSearch();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    // Handle sorting
    this.sort.sortChange.subscribe(() => {
      this.filters.sort_by = this.sort.active;
      this.filters.sort_order = this.sort.direction as 'asc' | 'desc';
      this.filters.page = 1;
      this.loadUsers();
    });

    // Handle pagination
    this.paginator.page.subscribe(() => {
      this.filters.page = this.paginator.pageIndex + 1;
      this.filters.per_page = this.paginator.pageSize;
      this.loadUsers();
    });
  }

  private initializeFilterOptions(): void {
    this.roleOptions = [
      { value: '', label: 'All Roles', color: '' },
      ...this.userService.getRoleOptions()
    ];

    this.statusOptions = this.userService.getStatusOptions();
    this.emailVerificationOptions = this.userService.getEmailVerificationOptions();
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.filters.search = searchTerm;
      this.filters.page = 1;
      this.loadUsers();
    });
  }

  private loadRoles(): void {
    this.userService.getRoles().subscribe({
      next: (response) => {
        if (response.success) {
          this.roles = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading roles:', error);
      }
    });
  }

  loadUsers(): void {
    this.loading = true;
    this.loadingService.setLoading(true);

    this.userService.getUsers(this.filters).subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.data;
          this.totalItems = response.meta.total;
          
          // Update paginator
          this.paginator.length = this.totalItems;
          this.paginator.pageIndex = response.meta.current_page - 1;
          this.paginator.pageSize = response.meta.per_page;
        }
        this.loading = false;
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.snackBar.open('Error loading users', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loading = false;
        this.loadingService.setLoading(false);
      }
    });
  }

  onSearch(searchTerm: string): void {
    this.searchSubject.next(searchTerm);
  }

  onFilterChange(): void {
    this.filters.page = 1;
    this.loadUsers();
  }

  clearFilters(): void {
    this.filters = {
      per_page: 20,
      page: 1,
      sort_by: 'created_at',
      sort_order: 'desc'
    };
    this.loadUsers();
  }

  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  // User actions
  createUser(): void {
    const dialogRef = this.dialog.open(UserFormDialogComponent, {
      width: '600px',
      data: { mode: 'create', roles: this.roles }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUsers();
      }
    });
  }

  editUser(user: User): void {
    const dialogRef = this.dialog.open(UserFormDialogComponent, {
      width: '600px',
      data: { mode: 'edit', user, roles: this.roles }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUsers();
      }
    });
  }

  viewUser(user: User): void {
    this.router.navigate(['/users', user.id]);
  }

  deleteUser(user: User): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete User',
        message: `Are you sure you want to delete "${user.name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadingService.setLoading(true);

        this.userService.deleteUser(user.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('User deleted successfully', 'Close', {
                duration: 3000,
                panelClass: ['success-snackbar']
              });
              this.loadUsers();
            }
            this.loadingService.setLoading(false);
          },
          error: (error) => {
            console.error('Error deleting user:', error);
            this.snackBar.open('Error deleting user', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            this.loadingService.setLoading(false);
          }
        });
      }
    });
  }

  toggleUserStatus(user: User): void {
    const newStatus = !user.is_active;
    const action = newStatus ? 'activate' : 'deactivate';

    this.loadingService.setLoading(true);

    this.userService.updateUserStatus(user.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(`User ${action}d successfully`, 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadUsers();
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error(`Error ${action}ing user:`, error);
        this.snackBar.open(`Error ${action}ing user`, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  sendPasswordReset(user: User): void {
    this.loadingService.setLoading(true);

    this.userService.sendPasswordResetEmail(user.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Password reset email sent successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error sending password reset:', error);
        this.snackBar.open('Error sending password reset email', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  verifyEmail(user: User): void {
    this.loadingService.setLoading(true);

    this.userService.verifyEmail(user.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Email verified successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadUsers();
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error verifying email:', error);
        this.snackBar.open('Error verifying email', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  // Bulk actions
  bulkUpdateStatus(isActive: boolean): void {
    if (this.selection.selected.length === 0) {
      this.snackBar.open('Please select users to update', 'Close', {
        duration: 3000,
        panelClass: ['warning-snackbar']
      });
      return;
    }

    const userIds = this.selection.selected.map(user => user.id);
    const action = isActive ? 'activate' : 'deactivate';
    this.bulkActionLoading = true;

    this.userService.bulkUpdateStatus(userIds, isActive).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(`${userIds.length} users ${action}d successfully`, 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.selection.clear();
          this.loadUsers();
        }
        this.bulkActionLoading = false;
      },
      error: (error) => {
        console.error(`Error ${action}ing users:`, error);
        this.snackBar.open(`Error ${action}ing users`, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.bulkActionLoading = false;
      }
    });
  }

  // Utility methods
  getRoleName(role: string): string {
    const roleOption = this.roleOptions.find(option => option.value === role);
    return roleOption ? roleOption.label : role;
  }

  getRoleColor(role: string): string {
    const roleOption = this.roleOptions.find(option => option.value === role);
    return roleOption ? roleOption.color : '';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  // Export functionality
  exportUsers(): void {
    this.loadingService.setLoading(true);

    this.userService.exportUsers('csv', this.filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `users-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        window.URL.revokeObjectURL(url);
        
        this.snackBar.open('Users exported successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error exporting users:', error);
        this.snackBar.open('Error exporting users', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }
}
