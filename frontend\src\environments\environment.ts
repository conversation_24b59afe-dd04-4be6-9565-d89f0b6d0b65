export const environment = {
  production: false,
  apiUrl: 'http://localhost:8001/api',
  appName: 'E-Commerce Admin Dashboard',
  version: '1.0.0',
  
  // Feature flags
  features: {
    analytics: true,
    bulkOperations: true,
    advancedFilters: true,
    exportData: true,
    realTimeNotifications: true,
    darkMode: true,
    multiLanguage: false,
  },
  
  // API configuration
  api: {
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  },
  
  // File upload configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    allowedDocumentTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    maxFiles: 10,
  },
  
  // Pagination defaults
  pagination: {
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
  },
  
  // Cache configuration
  cache: {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxSize: 100, // Maximum number of cached items
  },
  
  // Theme configuration
  theme: {
    defaultTheme: 'light',
    primaryColor: '#3f51b5',
    accentColor: '#ff4081',
  },
  
  // External services
  external: {
    googleMapsApiKey: '',
    googleAnalyticsId: '',
    sentryDsn: '',
  },
  
  // Development tools
  devTools: {
    enableReduxDevTools: true,
    enableConsoleLogging: true,
    enablePerformanceMonitoring: true,
  }
};
