<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class DesignSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        // Site Identity
        'site_title',
        'site_tagline',
        'admin_email',
        
        // Logos
        'logo_header',
        'logo_footer',
        'logo_mobile',
        'favicon',
        
        // Typography
        'primary_font_family',
        'secondary_font_family',
        'base_font_size',
        'heading_font_weight',
        'body_font_weight',
        
        // Colors
        'primary_color',
        'secondary_color',
        'background_color',
        'text_color',
        'text_secondary_color',
        'button_primary_color',
        'button_secondary_color',
        'button_success_color',
        'button_danger_color',
        'button_warning_color',
        'button_info_color',
        'link_color',
        'link_hover_color',
        'border_color',
        'success_color',
        'error_color',
        'warning_color',
        'info_color',
        
        // Contact Information
        'company_name',
        'company_phone',
        'company_whatsapp',
        'company_email',
        'company_address',
        'company_map_url',
        
        // Payment Settings
        'gpay_upi_id',
        'razorpay_key_id',
        'razorpay_key_secret',
        'cod_enabled',
        'min_order_amount',
        
        // Social Media
        'facebook_url',
        'twitter_url',
        'instagram_url',
        'youtube_url',
        'linkedin_url',
        'pinterest_url',
        'tiktok_url',
        'snapchat_url',
        
        // Analytics
        'google_analytics_id',
        'google_tag_manager_id',
        'facebook_pixel_id',
        'custom_head_code',
        'custom_body_code',
        'custom_footer_code',
        
        // Copyright & Legal
        'copyright_text',
        'terms_of_service_url',
        'privacy_policy_url',
        'refund_policy_url',
        
        // Layout
        'layout_type',
        'container_max_width',
        'sidebar_position',
        'sticky_header',
        'sticky_footer',
        
        // Theme
        'theme_mode',
        'enable_animations',
        'border_radius',
        'box_shadow',
        'custom_css',
        
        // Maintenance
        'maintenance_mode',
        'maintenance_message'
    ];

    protected $casts = [
        'cod_enabled' => 'boolean',
        'min_order_amount' => 'decimal:2',
        'sticky_header' => 'boolean',
        'sticky_footer' => 'boolean',
        'enable_animations' => 'boolean',
        'maintenance_mode' => 'boolean',
        'base_font_size' => 'integer',
        'container_max_width' => 'integer',
    ];

    /**
     * Get the singleton instance of design settings.
     */
    public static function getInstance(): self
    {
        $settings = static::first();
        
        if (!$settings) {
            $settings = static::create([
                'site_title' => 'E-Commerce Management System',
                'primary_color' => '#3f51b5',
                'secondary_color' => '#ff4081',
                'background_color' => '#ffffff',
                'text_color' => '#333333',
                'copyright_text' => '© 2024 E-Commerce Management System. All rights reserved.',
            ]);
        }
        
        return $settings;
    }

    /**
     * Get logo URL.
     */
    public function getLogoHeaderUrlAttribute(): ?string
    {
        return $this->logo_header ? Storage::url($this->logo_header) : null;
    }

    /**
     * Get footer logo URL.
     */
    public function getLogoFooterUrlAttribute(): ?string
    {
        return $this->logo_footer ? Storage::url($this->logo_footer) : null;
    }

    /**
     * Get mobile logo URL.
     */
    public function getLogoMobileUrlAttribute(): ?string
    {
        return $this->logo_mobile ? Storage::url($this->logo_mobile) : null;
    }

    /**
     * Get favicon URL.
     */
    public function getFaviconUrlAttribute(): ?string
    {
        return $this->favicon ? Storage::url($this->favicon) : null;
    }

    /**
     * Get all social media links.
     */
    public function getSocialLinksAttribute(): array
    {
        return array_filter([
            'facebook' => $this->facebook_url,
            'twitter' => $this->twitter_url,
            'instagram' => $this->instagram_url,
            'youtube' => $this->youtube_url,
            'linkedin' => $this->linkedin_url,
            'pinterest' => $this->pinterest_url,
            'tiktok' => $this->tiktok_url,
            'snapchat' => $this->snapchat_url,
        ]);
    }

    /**
     * Get color palette.
     */
    public function getColorPaletteAttribute(): array
    {
        return [
            'primary' => $this->primary_color,
            'secondary' => $this->secondary_color,
            'background' => $this->background_color,
            'text' => $this->text_color,
            'text_secondary' => $this->text_secondary_color,
            'success' => $this->success_color,
            'error' => $this->error_color,
            'warning' => $this->warning_color,
            'info' => $this->info_color,
            'border' => $this->border_color,
        ];
    }

    /**
     * Get button colors.
     */
    public function getButtonColorsAttribute(): array
    {
        return [
            'primary' => $this->button_primary_color,
            'secondary' => $this->button_secondary_color,
            'success' => $this->button_success_color,
            'danger' => $this->button_danger_color,
            'warning' => $this->button_warning_color,
            'info' => $this->button_info_color,
        ];
    }

    /**
     * Get typography settings.
     */
    public function getTypographyAttribute(): array
    {
        return [
            'primary_font' => $this->primary_font_family,
            'secondary_font' => $this->secondary_font_family,
            'base_size' => $this->base_font_size,
            'heading_weight' => $this->heading_font_weight,
            'body_weight' => $this->body_font_weight,
        ];
    }

    /**
     * Get payment configuration.
     */
    public function getPaymentConfigAttribute(): array
    {
        return [
            'gpay_upi_id' => $this->gpay_upi_id,
            'razorpay_key_id' => $this->razorpay_key_id,
            'cod_enabled' => $this->cod_enabled,
            'min_order_amount' => $this->min_order_amount,
        ];
    }

    /**
     * Get analytics configuration.
     */
    public function getAnalyticsConfigAttribute(): array
    {
        return [
            'google_analytics_id' => $this->google_analytics_id,
            'google_tag_manager_id' => $this->google_tag_manager_id,
            'facebook_pixel_id' => $this->facebook_pixel_id,
        ];
    }

    /**
     * Generate CSS variables from settings.
     */
    public function generateCssVariables(): string
    {
        return ":root {
            --primary-color: {$this->primary_color};
            --secondary-color: {$this->secondary_color};
            --background-color: {$this->background_color};
            --text-color: {$this->text_color};
            --text-secondary-color: {$this->text_secondary_color};
            --border-color: {$this->border_color};
            --success-color: {$this->success_color};
            --error-color: {$this->error_color};
            --warning-color: {$this->warning_color};
            --info-color: {$this->info_color};
            --primary-font: '{$this->primary_font_family}';
            --secondary-font: '{$this->secondary_font_family}';
            --base-font-size: {$this->base_font_size}px;
            --border-radius: {$this->border_radius};
            --box-shadow: {$this->box_shadow};
        }";
    }

    /**
     * Check if maintenance mode is enabled.
     */
    public function isMaintenanceMode(): bool
    {
        return $this->maintenance_mode;
    }

    /**
     * Update a specific setting.
     */
    public static function updateSetting(string $key, $value): bool
    {
        $settings = static::getInstance();
        return $settings->update([$key => $value]);
    }

    /**
     * Get a specific setting value.
     */
    public static function getSetting(string $key, $default = null)
    {
        $settings = static::getInstance();
        return $settings->getAttribute($key) ?? $default;
    }
}
