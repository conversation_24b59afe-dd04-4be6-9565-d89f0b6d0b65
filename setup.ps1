# E-Commerce Management Application Setup Script
# This script sets up the development environment for all three components

Write-Host "🚀 Setting up E-Commerce Management Application..." -ForegroundColor Green

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js 18.x or higher." -ForegroundColor Red
    exit 1
}

# Check PHP
try {
    $phpVersion = php --version
    Write-Host "✅ PHP is available" -ForegroundColor Green
} catch {
    Write-Host "❌ PHP is not installed. Please install PHP 8.1 or higher." -ForegroundColor Red
    exit 1
}

# Check Composer
try {
    $composerVersion = composer --version
    Write-Host "✅ Composer is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Composer is not installed. Please install Composer." -ForegroundColor Red
    exit 1
}

# Setup Backend (Laravel)
Write-Host "🔧 Setting up <PERSON><PERSON> Backend..." -ForegroundColor Yellow
if (Test-Path "backend") {
    Set-Location backend
    
    # Install dependencies
    Write-Host "📦 Installing PHP dependencies..." -ForegroundColor Cyan
    composer install
    
    # Setup environment
    if (!(Test-Path ".env")) {
        Copy-Item ".env.example" ".env"
        Write-Host "📝 Created .env file" -ForegroundColor Green
    }
    
    # Generate application key
    php artisan key:generate
    
    # Setup database (if MySQL is running)
    Write-Host "🗄️ Setting up database..." -ForegroundColor Cyan
    try {
        php artisan migrate --seed
        Write-Host "✅ Database setup complete" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Database migration failed. Please ensure MySQL is running and configured." -ForegroundColor Yellow
    }
    
    Set-Location ..
}

# Setup Frontend (Angular)
Write-Host "🔧 Setting up Angular Frontend..." -ForegroundColor Yellow
if (Test-Path "frontend") {
    Set-Location frontend
    
    # Install dependencies
    Write-Host "📦 Installing npm dependencies..." -ForegroundColor Cyan
    npm install
    
    Set-Location ..
}

# Setup Mobile App (React Native)
Write-Host "🔧 Setting up React Native Mobile App..." -ForegroundColor Yellow
if (Test-Path "mobile") {
    Set-Location mobile
    
    # Install dependencies
    Write-Host "📦 Installing npm dependencies..." -ForegroundColor Cyan
    npm install
    
    Set-Location ..
}

Write-Host "🎉 Setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📚 Next steps:" -ForegroundColor Yellow
Write-Host "1. Configure your database connection in backend/.env"
Write-Host "2. Start the backend: cd backend && php artisan serve"
Write-Host "3. Start the frontend: cd frontend && ng serve"
Write-Host "4. Start the mobile app: cd mobile && npx expo start"
Write-Host ""
Write-Host "🌐 URLs:" -ForegroundColor Yellow
Write-Host "- Backend API: http://localhost:8000"
Write-Host "- Frontend Admin: http://localhost:4200"
Write-Host "- Mobile App: Expo DevTools will provide the URL"
