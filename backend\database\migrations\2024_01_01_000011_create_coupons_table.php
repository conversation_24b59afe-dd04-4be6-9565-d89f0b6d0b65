<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['percentage', 'fixed_cart', 'fixed_product'])->default('percentage');
            $table->decimal('value', 10, 2); // Percentage or fixed amount
            $table->decimal('minimum_amount', 10, 2)->nullable(); // Minimum cart amount
            $table->decimal('maximum_discount', 10, 2)->nullable(); // Maximum discount amount
            $table->integer('usage_limit')->nullable(); // Total usage limit
            $table->integer('usage_limit_per_user')->nullable(); // Per user usage limit
            $table->integer('used_count')->default(0); // Current usage count
            $table->boolean('is_active')->default(true);
            $table->timestamp('starts_at')->nullable(); // When coupon becomes active
            $table->timestamp('expires_at')->nullable(); // When coupon expires
            $table->enum('applies_to', ['all', 'specific_products', 'specific_categories'])->default('all');
            $table->boolean('exclude_sale_items')->default(false); // Exclude items on sale
            $table->boolean('individual_use')->default(false); // Cannot be combined with other coupons
            $table->boolean('free_shipping')->default(false); // Provides free shipping
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['code', 'is_active']);
            $table->index(['is_active', 'starts_at', 'expires_at']);
            $table->index(['type', 'is_active']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
