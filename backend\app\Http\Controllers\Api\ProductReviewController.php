<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductReview;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProductReviewController extends Controller
{
    /**
     * Display reviews for a product.
     */
    public function index(Product $product, Request $request): JsonResponse
    {
        $query = $product->reviews()->approved()->with(['user', 'order']);

        // Filter by rating
        if ($request->has('rating')) {
            $query->where('rating', $request->rating);
        }

        // Filter by verified purchase
        if ($request->boolean('verified_only')) {
            $query->where('verified_purchase', true);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        switch ($sortBy) {
            case 'rating':
                $query->orderBy('rating', $sortOrder);
                break;
            case 'helpful':
                $query->orderBy('helpful_count', $sortOrder);
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', $sortOrder);
                break;
        }

        $reviews = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'message' => 'Reviews retrieved successfully',
            'data' => $reviews->items(),
            'meta' => [
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
                'per_page' => $reviews->perPage(),
                'total' => $reviews->total(),
            ]
        ]);
    }

    /**
     * Store a new review.
     */
    public function store(Request $request, Product $product): JsonResponse
    {
        $validated = $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'review' => 'nullable|string|max:2000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'order_id' => 'nullable|exists:orders,id'
        ]);

        // Check if user already reviewed this product
        $existingReview = ProductReview::where('product_id', $product->id)
            ->where('user_id', Auth::id())
            ->first();

        if ($existingReview) {
            return response()->json([
                'success' => false,
                'message' => 'You have already reviewed this product'
            ], 422);
        }

        // Check if order belongs to user and contains this product
        $verifiedPurchase = false;
        if ($validated['order_id']) {
            $order = Order::where('id', $validated['order_id'])
                ->where('user_id', Auth::id())
                ->whereHas('items', function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                })
                ->first();

            $verifiedPurchase = (bool) $order;
        }

        // Handle image uploads
        $imageUrls = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('reviews', 'public');
                $imageUrls[] = $path;
            }
        }

        $review = ProductReview::create([
            'product_id' => $product->id,
            'user_id' => Auth::id(),
            'order_id' => $validated['order_id'] ?? null,
            'rating' => $validated['rating'],
            'title' => $validated['title'] ?? null,
            'review' => $validated['review'] ?? null,
            'images' => $imageUrls,
            'verified_purchase' => $verifiedPurchase,
            'is_approved' => false, // Requires admin approval
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review submitted successfully and is pending approval',
            'data' => $review->load('user')
        ], 201);
    }

    /**
     * Display the specified review.
     */
    public function show(Product $product, ProductReview $review): JsonResponse
    {
        if ($review->product_id !== $product->id) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found for this product'
            ], 404);
        }

        $review->load(['user', 'order']);

        return response()->json([
            'success' => true,
            'message' => 'Review retrieved successfully',
            'data' => $review
        ]);
    }

    /**
     * Update the specified review.
     */
    public function update(Request $request, Product $product, ProductReview $review): JsonResponse
    {
        if ($review->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this review'
            ], 403);
        }

        $validated = $request->validate([
            'rating' => 'sometimes|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'review' => 'nullable|string|max:2000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            // Delete old images
            if ($review->images) {
                foreach ($review->images as $image) {
                    Storage::disk('public')->delete($image);
                }
            }

            $imageUrls = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('reviews', 'public');
                $imageUrls[] = $path;
            }
            $validated['images'] = $imageUrls;
        }

        // Reset approval status if content changed
        if (isset($validated['rating']) || isset($validated['title']) || isset($validated['review'])) {
            $validated['is_approved'] = false;
            $validated['approved_at'] = null;
            $validated['approved_by'] = null;
        }

        $review->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Review updated successfully',
            'data' => $review->fresh()->load('user')
        ]);
    }

    /**
     * Remove the specified review.
     */
    public function destroy(Product $product, ProductReview $review): JsonResponse
    {
        if ($review->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this review'
            ], 403);
        }

        // Delete review images
        if ($review->images) {
            foreach ($review->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $review->delete();

        // Update product average rating
        $product->updateAverageRating();

        return response()->json([
            'success' => true,
            'message' => 'Review deleted successfully'
        ]);
    }

    /**
     * Mark review as helpful.
     */
    public function markHelpful(Product $product, ProductReview $review): JsonResponse
    {
        $review->markAsHelpful();

        return response()->json([
            'success' => true,
            'message' => 'Review marked as helpful',
            'data' => [
                'helpful_count' => $review->helpful_count,
                'helpfulness_ratio' => $review->helpfulness_ratio
            ]
        ]);
    }

    /**
     * Mark review as not helpful.
     */
    public function markNotHelpful(Product $product, ProductReview $review): JsonResponse
    {
        $review->markAsNotHelpful();

        return response()->json([
            'success' => true,
            'message' => 'Review marked as not helpful',
            'data' => [
                'not_helpful_count' => $review->not_helpful_count,
                'helpfulness_ratio' => $review->helpfulness_ratio
            ]
        ]);
    }

    /**
     * Get review statistics for a product.
     */
    public function statistics(Product $product): JsonResponse
    {
        $stats = [
            'total_reviews' => $product->reviews()->approved()->count(),
            'average_rating' => $product->average_rating,
            'rating_distribution' => [],
            'verified_purchase_percentage' => 0
        ];

        // Rating distribution
        for ($i = 1; $i <= 5; $i++) {
            $count = $product->reviews()->approved()->where('rating', $i)->count();
            $stats['rating_distribution'][$i] = [
                'count' => $count,
                'percentage' => $stats['total_reviews'] > 0 ? round(($count / $stats['total_reviews']) * 100, 1) : 0
            ];
        }

        // Verified purchase percentage
        if ($stats['total_reviews'] > 0) {
            $verifiedCount = $product->reviews()->approved()->where('verified_purchase', true)->count();
            $stats['verified_purchase_percentage'] = round(($verifiedCount / $stats['total_reviews']) * 100, 1);
        }

        return response()->json([
            'success' => true,
            'message' => 'Review statistics retrieved successfully',
            'data' => $stats
        ]);
    }
}
