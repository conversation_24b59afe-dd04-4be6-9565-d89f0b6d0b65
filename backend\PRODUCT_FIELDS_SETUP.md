# 🛠️ Product Fields Setup & Verification Guide

This guide will help you ensure all required product fields are properly set up in your database.

## 📋 Required Product Fields

The following fields must be present in the `products` table:

### ✅ Basic Information
- `id` (Primary Key)
- `name` (VARCHAR, NOT NULL)
- `slug` (VARC<PERSON>R, UNIQ<PERSON>, NOT NULL)
- `description` (TEXT, NULLABLE)
- `short_description` (TEXT, NULLABLE)
- `sku` (VARCHAR, UNIQUE, NOT NULL)
- `category_id` (Foreign Key to categories)

### 💰 Pricing Fields
- `mrp` (DECIMAL 10,2, NOT NULL) - Maximum Retail Price
- `selling_price` (DECIMAL 10,2, NOT NULL) - Actual selling price
- `price` (DECIMAL 10,2, NOT NULL) - Legacy compatibility
- `sale_price` (DECIMAL 10,2, NULLABLE) - Special offer price
- `cost_price` (DECIMAL 10,2, NULLABLE) - Cost price
- `discount_percentage` (DECIMAL 5,2, DEFAULT 0) - Product discount %

### 📦 Inventory Fields
- `stock_quantity` (INTEGER, DEFAULT 0)
- `manage_stock` (BOOLEAN, DEFAULT TRUE)
- `stock_status` (ENUM: in_stock, out_of_stock, on_backorder)
- `low_stock_threshold` (INTEGER, DEFAULT 5)

### 📏 Physical Attributes
- `weight` (DECIMAL 8,2, NULLABLE)
- `length` (DECIMAL 8,2, NULLABLE)
- `width` (DECIMAL 8,2, NULLABLE)
- `height` (DECIMAL 8,2, NULLABLE)

### 🎛️ Status & Toggles
- `status` (ENUM: draft, published, archived, DEFAULT draft)
- `is_active` (BOOLEAN, DEFAULT TRUE) - Enable/Disable toggle
- `featured` (BOOLEAN, DEFAULT FALSE)

### 🔍 SEO Fields
- `meta_title` (VARCHAR, NULLABLE)
- `meta_description` (TEXT, NULLABLE)
- `meta_keywords` (TEXT, NULLABLE)

### 📝 Additional Fields
- `purchase_note` (TEXT, NULLABLE)
- `attributes` (JSON, NULLABLE) - Dynamic attributes like size, color
- `gallery` (JSON, NULLABLE) - Multiple product images
- `main_image` (VARCHAR, NULLABLE) - Main product photo

### 📅 Timestamps
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## 🚀 Setup Instructions

### Method 1: Using Laravel Artisan (Recommended)

1. **Run Migrations:**
   ```bash
   php artisan migrate
   ```

2. **Verify Fields:**
   ```bash
   php artisan products:verify-fields
   ```

3. **Auto-fix Missing Fields:**
   ```bash
   php artisan products:verify-fields --fix
   ```

4. **Clear Cache:**
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   ```

### Method 2: Using SQL Scripts

If you can't run Laravel commands, use the SQL scripts:

1. **Run the verification script:**
   ```sql
   -- Execute: backend/database/sql/verify_product_fields.sql
   ```

2. **Add missing fields:**
   ```sql
   -- Execute: backend/database/sql/add_missing_product_fields.sql
   ```

### Method 3: Manual Database Setup

If you need to manually create the table, here's the complete SQL:

```sql
CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description TEXT,
    sku VARCHAR(255) UNIQUE NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    mrp DECIMAL(10,2) NOT NULL,
    selling_price DECIMAL(10,2) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    stock_quantity INTEGER DEFAULT 0,
    manage_stock BOOLEAN DEFAULT TRUE,
    stock_status ENUM('in_stock', 'out_of_stock', 'on_backorder') DEFAULT 'in_stock',
    low_stock_threshold INTEGER DEFAULT 5,
    weight DECIMAL(8,2),
    length DECIMAL(8,2),
    width DECIMAL(8,2),
    height DECIMAL(8,2),
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    is_active BOOLEAN DEFAULT TRUE,
    featured BOOLEAN DEFAULT FALSE,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    purchase_note TEXT,
    attributes JSON,
    gallery JSON,
    main_image VARCHAR(255),
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    
    INDEX idx_category_status (category_id, status),
    INDEX idx_status_featured (status, featured),
    INDEX idx_slug_status (slug, status),
    INDEX idx_sku_status (sku, status),
    INDEX idx_pricing (mrp, selling_price),
    INDEX idx_stock (stock_status, manage_stock),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

## 🔍 Verification Checklist

After setup, verify these items:

- [ ] All 31 required fields are present
- [ ] MRP and selling_price fields exist
- [ ] is_active field exists for enable/disable toggle
- [ ] main_image field exists separate from gallery
- [ ] discount_percentage field exists
- [ ] All indexes are created for performance
- [ ] Foreign key constraint to categories table exists

## 🐛 Troubleshooting

### Issue: Fields not showing in API responses
**Solution:** Clear Laravel cache and restart server
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### Issue: Migration fails
**Solution:** Check if table already exists and run specific migration
```bash
php artisan migrate:status
php artisan migrate --path=/database/migrations/2024_01_01_000036_add_is_active_to_products_table.php
```

### Issue: Database connection error
**Solution:** Check .env file database configuration
```bash
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

## 📞 Support

If you encounter any issues:

1. Check the Laravel logs: `storage/logs/laravel.log`
2. Verify database connection
3. Ensure all migrations are run
4. Clear all caches
5. Restart your web server

## ✅ Success Confirmation

When setup is complete, you should see:
- ✅ All 31 fields present in products table
- ✅ API endpoints returning all field data
- ✅ Product creation/update working with new fields
- ✅ No database errors in logs

**🎉 Your product fields are now 100% complete and ready for use!**
