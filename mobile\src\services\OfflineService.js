import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';
import { Alert } from 'react-native';

class OfflineService {
  constructor() {
    this.isOnline = true;
    this.pendingRequests = [];
    this.offlineData = {};
    this.syncInProgress = false;
    this.listeners = [];
    
    this.initializeNetworkListener();
    this.loadOfflineData();
  }

  /**
   * Initialize network state listener
   */
  initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected && state.isInternetReachable;
      
      if (!wasOnline && this.isOnline) {
        // Just came back online
        this.onConnectionRestored();
      } else if (wasOnline && !this.isOnline) {
        // Just went offline
        this.onConnectionLost();
      }
      
      // Notify listeners
      this.notifyListeners(this.isOnline);
    });
  }

  /**
   * Add network state listener
   */
  addNetworkListener(callback) {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  /**
   * Notify all listeners of network state change
   */
  notifyListeners(isOnline) {
    this.listeners.forEach(callback => callback(isOnline));
  }

  /**
   * Handle connection restored
   */
  async onConnectionRestored() {
    console.log('Connection restored - syncing offline data');
    await this.syncOfflineData();
  }

  /**
   * Handle connection lost
   */
  onConnectionLost() {
    console.log('Connection lost - switching to offline mode');
  }

  /**
   * Check if device is online
   */
  async checkConnection() {
    const state = await NetInfo.fetch();
    this.isOnline = state.isConnected && state.isInternetReachable;
    return this.isOnline;
  }

  /**
   * Get network state
   */
  getNetworkState() {
    return {
      isOnline: this.isOnline,
      hasPendingRequests: this.pendingRequests.length > 0,
      pendingCount: this.pendingRequests.length
    };
  }

  /**
   * Store data for offline access
   */
  async storeOfflineData(key, data, expiryHours = 24) {
    try {
      const offlineItem = {
        data: data,
        timestamp: Date.now(),
        expiry: Date.now() + (expiryHours * 60 * 60 * 1000)
      };

      this.offlineData[key] = offlineItem;
      await AsyncStorage.setItem('offline_data', JSON.stringify(this.offlineData));
      
      console.log(`Stored offline data for key: ${key}`);
    } catch (error) {
      console.error('Error storing offline data:', error);
    }
  }

  /**
   * Get offline data
   */
  async getOfflineData(key) {
    try {
      const offlineItem = this.offlineData[key];
      
      if (!offlineItem) {
        return null;
      }

      // Check if data has expired
      if (Date.now() > offlineItem.expiry) {
        delete this.offlineData[key];
        await AsyncStorage.setItem('offline_data', JSON.stringify(this.offlineData));
        return null;
      }

      return offlineItem.data;
    } catch (error) {
      console.error('Error getting offline data:', error);
      return null;
    }
  }

  /**
   * Load offline data from storage
   */
  async loadOfflineData() {
    try {
      const storedData = await AsyncStorage.getItem('offline_data');
      if (storedData) {
        this.offlineData = JSON.parse(storedData);
        
        // Clean up expired data
        const now = Date.now();
        let hasExpiredData = false;
        
        Object.keys(this.offlineData).forEach(key => {
          if (now > this.offlineData[key].expiry) {
            delete this.offlineData[key];
            hasExpiredData = true;
          }
        });

        if (hasExpiredData) {
          await AsyncStorage.setItem('offline_data', JSON.stringify(this.offlineData));
        }
      }

      // Load pending requests
      const pendingData = await AsyncStorage.getItem('pending_requests');
      if (pendingData) {
        this.pendingRequests = JSON.parse(pendingData);
      }
    } catch (error) {
      console.error('Error loading offline data:', error);
    }
  }

  /**
   * Queue request for later execution
   */
  async queueRequest(request) {
    try {
      const queuedRequest = {
        id: Date.now() + Math.random(),
        ...request,
        timestamp: Date.now()
      };

      this.pendingRequests.push(queuedRequest);
      await AsyncStorage.setItem('pending_requests', JSON.stringify(this.pendingRequests));
      
      console.log(`Queued request: ${request.method} ${request.url}`);
      return queuedRequest.id;
    } catch (error) {
      console.error('Error queuing request:', error);
      throw error;
    }
  }

  /**
   * Remove request from queue
   */
  async removeQueuedRequest(requestId) {
    try {
      this.pendingRequests = this.pendingRequests.filter(req => req.id !== requestId);
      await AsyncStorage.setItem('pending_requests', JSON.stringify(this.pendingRequests));
    } catch (error) {
      console.error('Error removing queued request:', error);
    }
  }

  /**
   * Sync offline data when connection is restored
   */
  async syncOfflineData() {
    if (this.syncInProgress || !this.isOnline || this.pendingRequests.length === 0) {
      return;
    }

    this.syncInProgress = true;
    const requestsToSync = [...this.pendingRequests];
    let successCount = 0;
    let failureCount = 0;

    console.log(`Starting sync of ${requestsToSync.length} pending requests`);

    for (const request of requestsToSync) {
      try {
        // Execute the request
        const response = await this.executeRequest(request);
        
        if (response.success) {
          await this.removeQueuedRequest(request.id);
          successCount++;
          console.log(`Synced request: ${request.method} ${request.url}`);
        } else {
          failureCount++;
          console.error(`Failed to sync request: ${request.method} ${request.url}`);
        }
      } catch (error) {
        failureCount++;
        console.error(`Error syncing request: ${request.method} ${request.url}`, error);
      }
    }

    this.syncInProgress = false;

    // Show sync results
    if (successCount > 0 || failureCount > 0) {
      const message = `Sync completed: ${successCount} successful, ${failureCount} failed`;
      console.log(message);
      
      if (failureCount === 0) {
        Alert.alert('Sync Complete', `Successfully synced ${successCount} pending changes.`);
      } else {
        Alert.alert('Sync Partial', `Synced ${successCount} changes. ${failureCount} failed and will retry later.`);
      }
    }
  }

  /**
   * Execute a queued request
   */
  async executeRequest(request) {
    // This would integrate with your API service
    // For now, we'll simulate the execution
    try {
      const ApiService = require('./ApiService').default;
      
      switch (request.method.toUpperCase()) {
        case 'GET':
          return await ApiService.get(request.url, request.config);
        case 'POST':
          return await ApiService.post(request.url, request.data, request.config);
        case 'PUT':
          return await ApiService.put(request.url, request.data, request.config);
        case 'DELETE':
          return await ApiService.delete(request.url, request.config);
        default:
          throw new Error(`Unsupported method: ${request.method}`);
      }
    } catch (error) {
      console.error('Error executing request:', error);
      return { success: false, error };
    }
  }

  /**
   * Clear all offline data
   */
  async clearOfflineData() {
    try {
      this.offlineData = {};
      this.pendingRequests = [];
      
      await AsyncStorage.removeItem('offline_data');
      await AsyncStorage.removeItem('pending_requests');
      
      console.log('Cleared all offline data');
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  }

  /**
   * Get offline storage statistics
   */
  async getStorageStats() {
    try {
      const offlineDataSize = JSON.stringify(this.offlineData).length;
      const pendingRequestsSize = JSON.stringify(this.pendingRequests).length;
      
      return {
        offlineDataCount: Object.keys(this.offlineData).length,
        offlineDataSize: offlineDataSize,
        pendingRequestsCount: this.pendingRequests.length,
        pendingRequestsSize: pendingRequestsSize,
        totalSize: offlineDataSize + pendingRequestsSize
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return null;
    }
  }

  /**
   * Preload essential data for offline use
   */
  async preloadEssentialData() {
    if (!this.isOnline) {
      console.log('Cannot preload data - device is offline');
      return;
    }

    try {
      console.log('Preloading essential data for offline use...');
      
      // Preload user profile
      const UserService = require('./UserService').default;
      const profile = await UserService.getProfile();
      if (profile.success) {
        await this.storeOfflineData('user_profile', profile.data, 168); // 7 days
      }

      // Preload recent orders
      const OrderService = require('./OrderService').default;
      const orders = await OrderService.getRecentOrders(10);
      if (orders.success) {
        await this.storeOfflineData('recent_orders', orders.data, 24); // 1 day
      }

      // Preload wishlist
      const ProductService = require('./ProductService').default;
      const wishlist = await ProductService.getWishlist();
      if (wishlist.success) {
        await this.storeOfflineData('wishlist', wishlist.data, 24); // 1 day
      }

      // Preload app settings
      const appSettings = await UserService.getAppSettings();
      await this.storeOfflineData('app_settings', appSettings, 168); // 7 days

      console.log('Essential data preloaded successfully');
    } catch (error) {
      console.error('Error preloading essential data:', error);
    }
  }

  /**
   * Handle offline cart operations
   */
  async handleOfflineCart(action, data) {
    const cartKey = 'offline_cart';
    let cart = await this.getOfflineData(cartKey) || { items: [], total: 0 };

    switch (action) {
      case 'add':
        const existingItem = cart.items.find(item => 
          item.product_id === data.product_id && 
          item.variation_id === data.variation_id
        );

        if (existingItem) {
          existingItem.quantity += data.quantity;
        } else {
          cart.items.push(data);
        }
        break;

      case 'remove':
        cart.items = cart.items.filter(item => item.id !== data.item_id);
        break;

      case 'update':
        const itemToUpdate = cart.items.find(item => item.id === data.item_id);
        if (itemToUpdate) {
          itemToUpdate.quantity = data.quantity;
        }
        break;

      case 'clear':
        cart = { items: [], total: 0 };
        break;
    }

    // Recalculate total
    cart.total = cart.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);

    await this.storeOfflineData(cartKey, cart, 24); // 1 day
    return cart;
  }

  /**
   * Get offline cart
   */
  async getOfflineCart() {
    return await this.getOfflineData('offline_cart') || { items: [], total: 0 };
  }
}

export default new OfflineService();
