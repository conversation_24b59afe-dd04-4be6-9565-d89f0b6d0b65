<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_title',
                'value' => 'E-Commerce Management System',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Website title displayed in browser tab',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Comprehensive e-commerce solution by RekTech',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Website tagline or slogan',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'admin_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Administrator email address',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'timezone',
                'value' => 'UTC',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Default timezone for the application',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'date_format',
                'value' => 'Y-m-d',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Date format for display',
                'is_public' => true,
                'sort_order' => 5,
            ],
            [
                'key' => 'time_format',
                'value' => 'H:i:s',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Time format for display',
                'is_public' => true,
                'sort_order' => 6,
            ],
            [
                'key' => 'currency',
                'value' => 'USD',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Default currency code',
                'is_public' => true,
                'sort_order' => 7,
            ],
            [
                'key' => 'currency_symbol',
                'value' => '$',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Currency symbol',
                'is_public' => true,
                'sort_order' => 8,
            ],
            [
                'key' => 'currency_position',
                'value' => 'before',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Currency symbol position (before/after)',
                'is_public' => true,
                'sort_order' => 9,
            ],

            // Design Settings
            [
                'key' => 'theme_primary_color',
                'value' => '#3f51b5',
                'type' => 'string',
                'group' => 'design',
                'description' => 'Primary theme color',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'theme_secondary_color',
                'value' => '#ff4081',
                'type' => 'string',
                'group' => 'design',
                'description' => 'Secondary theme color',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'theme_background_color',
                'value' => '#ffffff',
                'type' => 'string',
                'group' => 'design',
                'description' => 'Background color',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'theme_text_color',
                'value' => '#333333',
                'type' => 'string',
                'group' => 'design',
                'description' => 'Text color',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'theme_font_family',
                'value' => 'Roboto',
                'type' => 'string',
                'group' => 'design',
                'description' => 'Font family',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Contact Information
            [
                'key' => 'company_name',
                'value' => 'RekTech',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'Company name',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'company_phone',
                'value' => '******-567-8900',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'Company phone number',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'company_whatsapp',
                'value' => '******-567-8900',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'WhatsApp business number',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'company_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'Company email address',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'company_address',
                'value' => '123 Business Street, Tech City, TC 12345',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'Company physical address',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Social Media
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/rektech',
                'type' => 'string',
                'group' => 'social',
                'description' => 'Facebook page URL',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'twitter_url',
                'value' => 'https://twitter.com/rektech',
                'type' => 'string',
                'group' => 'social',
                'description' => 'Twitter profile URL',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/rektech',
                'type' => 'string',
                'group' => 'social',
                'description' => 'Instagram profile URL',
                'is_public' => true,
                'sort_order' => 3,
            ],

            // Payment Settings
            [
                'key' => 'cod_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'payment',
                'description' => 'Enable Cash on Delivery',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'min_order_amount',
                'value' => '0',
                'type' => 'float',
                'group' => 'payment',
                'description' => 'Minimum order amount',
                'is_public' => true,
                'sort_order' => 2,
            ],

            // Module Settings
            [
                'key' => 'module_users_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'modules',
                'description' => 'Enable User Management module',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'module_products_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'modules',
                'description' => 'Enable Product Management module',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'module_orders_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'modules',
                'description' => 'Enable Order Management module',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'module_coupons_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'modules',
                'description' => 'Enable Coupon Management module',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'module_analytics_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'modules',
                'description' => 'Enable Analytics module',
                'is_public' => false,
                'sort_order' => 5,
            ],
            [
                'key' => 'module_media_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'modules',
                'description' => 'Enable Media Management module',
                'is_public' => false,
                'sort_order' => 6,
            ],
            [
                'key' => 'module_invoices_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'modules',
                'description' => 'Enable Invoice Management module',
                'is_public' => false,
                'sort_order' => 7,
            ],

            // Copyright
            [
                'key' => 'copyright_text',
                'value' => 'Web Design and developed By RekTech',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Copyright text displayed in footer',
                'is_public' => true,
                'sort_order' => 10,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }

        $this->command->info('Settings seeded successfully!');
        $this->command->info('Created ' . count($settings) . ' default settings');
    }
}
