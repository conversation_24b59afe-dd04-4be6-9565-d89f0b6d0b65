<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .header-left {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        
        .header-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: right;
        }
        
        .company-info h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
            color: #2c3e50;
        }
        
        .company-info p {
            margin: 2px 0;
            color: #666;
        }
        
        .invoice-info h2 {
            margin: 0 0 10px 0;
            font-size: 20px;
            color: #e74c3c;
        }
        
        .invoice-info p {
            margin: 2px 0;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 10px;
        }
        
        .status.paid {
            background-color: #27ae60;
            color: white;
        }
        
        .status.sent {
            background-color: #3498db;
            color: white;
        }
        
        .status.draft {
            background-color: #95a5a6;
            color: white;
        }
        
        .status.overdue {
            background-color: #e74c3c;
            color: white;
        }
        
        .addresses {
            display: table;
            width: 100%;
            margin: 30px 0;
        }
        
        .bill-to, .ship-to {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 20px;
        }
        
        .address-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 5px;
        }
        
        .address p {
            margin: 2px 0;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        
        .items-table th {
            background-color: #34495e;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
        }
        
        .items-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals {
            width: 300px;
            margin-left: auto;
            margin-top: 20px;
        }
        
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals td {
            padding: 8px 12px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .totals .total-row {
            font-weight: bold;
            font-size: 14px;
            background-color: #34495e;
            color: white;
        }
        
        .notes {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
        }
        
        .notes h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .terms {
            margin-top: 20px;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ecf0f1;
            padding-top: 15px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #95a5a6;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <div class="company-info">
                <h1>{{ config('app.name', 'E-Commerce Store') }}</h1>
                <p>123 Business Street</p>
                <p>City, State 12345</p>
                <p>Phone: (*************</p>
                <p>Email: <EMAIL></p>
                <p>Website: www.example.com</p>
            </div>
        </div>
        <div class="header-right">
            <div class="invoice-info">
                <h2>{{ strtoupper($invoice->type) }}</h2>
                <p><strong>{{ $invoice->type === 'invoice' ? 'Invoice' : ($invoice->type === 'credit_note' ? 'Credit Note' : 'Proforma') }} #:</strong> {{ $invoice->invoice_number }}</p>
                <p><strong>Date:</strong> {{ $invoice->created_at->format('M d, Y') }}</p>
                <p><strong>Due Date:</strong> {{ $invoice->due_date->format('M d, Y') }}</p>
                @if($invoice->order)
                    <p><strong>Order #:</strong> {{ $invoice->order->order_number }}</p>
                @endif
                <p><strong>Status:</strong> 
                    <span class="status {{ $invoice->status }}">{{ ucfirst($invoice->status) }}</span>
                </p>
            </div>
        </div>
    </div>

    <!-- Addresses -->
    <div class="addresses">
        <div class="bill-to">
            <div class="address-title">Bill To:</div>
            <div class="address">
                <p><strong>{{ $invoice->billing_address['name'] }}</strong></p>
                <p>{{ $invoice->billing_address['address'] }}</p>
                <p>{{ $invoice->billing_address['city'] }}, {{ $invoice->billing_address['state'] }} {{ $invoice->billing_address['postal_code'] }}</p>
                <p>{{ $invoice->billing_address['country'] }}</p>
                @if(isset($invoice->billing_address['phone']))
                    <p>Phone: {{ $invoice->billing_address['phone'] }}</p>
                @endif
                <p>Email: {{ $invoice->user->email }}</p>
            </div>
        </div>
        @if($invoice->shipping_address)
        <div class="ship-to">
            <div class="address-title">Ship To:</div>
            <div class="address">
                <p><strong>{{ $invoice->shipping_address['name'] }}</strong></p>
                <p>{{ $invoice->shipping_address['address'] }}</p>
                <p>{{ $invoice->shipping_address['city'] }}, {{ $invoice->shipping_address['state'] }} {{ $invoice->shipping_address['postal_code'] }}</p>
                <p>{{ $invoice->shipping_address['country'] }}</p>
                @if(isset($invoice->shipping_address['phone']))
                    <p>Phone: {{ $invoice->shipping_address['phone'] }}</p>
                @endif
            </div>
        </div>
        @endif
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th style="width: 10%;" class="text-center">Qty</th>
                <th style="width: 20%;" class="text-right">Unit Price</th>
                <th style="width: 20%;" class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->items as $item)
            <tr>
                <td>
                    <strong>{{ $item->product_name }}</strong>
                    @if($item->product_sku)
                        <br><small>SKU: {{ $item->product_sku }}</small>
                    @endif
                    @if($item->description)
                        <br><small>{{ $item->description }}</small>
                    @endif
                </td>
                <td class="text-center">{{ $item->quantity }}</td>
                <td class="text-right">{{ $invoice->currency }} {{ number_format($item->unit_price, 2) }}</td>
                <td class="text-right">{{ $invoice->currency }} {{ number_format($item->total_price, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Totals -->
    <div class="totals">
        <table>
            <tr>
                <td>Subtotal:</td>
                <td class="text-right">{{ $invoice->currency }} {{ number_format($invoice->subtotal, 2) }}</td>
            </tr>
            @if($invoice->discount_amount > 0)
            <tr>
                <td>Discount:</td>
                <td class="text-right">-{{ $invoice->currency }} {{ number_format($invoice->discount_amount, 2) }}</td>
            </tr>
            @endif
            @if($invoice->tax_amount > 0)
            <tr>
                <td>Tax:</td>
                <td class="text-right">{{ $invoice->currency }} {{ number_format($invoice->tax_amount, 2) }}</td>
            </tr>
            @endif
            <tr class="total-row">
                <td><strong>Total:</strong></td>
                <td class="text-right"><strong>{{ $invoice->currency }} {{ number_format($invoice->total_amount, 2) }}</strong></td>
            </tr>
        </table>
    </div>

    <!-- Notes -->
    @if($invoice->notes)
    <div class="notes">
        <h4>Notes:</h4>
        <p>{{ $invoice->notes }}</p>
    </div>
    @endif

    <!-- Terms and Conditions -->
    @if($invoice->terms_conditions)
    <div class="terms">
        <h4>Terms and Conditions:</h4>
        <p>{{ $invoice->terms_conditions }}</p>
    </div>
    @else
    <div class="terms">
        <h4>Terms and Conditions:</h4>
        <p>Payment is due within 30 days of invoice date. Late payments may be subject to fees. Please include invoice number with payment.</p>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This invoice was generated on {{ now()->format('M d, Y \a\t g:i A') }}</p>
    </div>
</body>
</html>
