<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Commerce Admin Panel</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 0 20px 30px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-header h2 {
            color: #ecf0f1;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            color: #bdc3c7;
            font-size: 12px;
        }

        .nav-menu {
            list-style: none;
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: #3498db;
            color: white;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            width: calc(100% - 280px);
        }

        .header {
            background: white;
            padding: 15px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .content-area {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 600px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .stat-card h3 {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
        }

        .stat-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-card .change {
            font-size: 12px;
            color: #27ae60;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .section-title {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }

        .modal-title {
            font-size: 20px;
            color: #2c3e50;
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }

        .activity-icon.success {
            background: #27ae60;
        }

        .activity-icon.info {
            background: #3498db;
        }

        .activity-icon.warning {
            background: #f39c12;
        }

        .activity-content h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }

        .activity-content p {
            margin: 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        .activity-time {
            margin-left: auto;
            color: #bdc3c7;
            font-size: 12px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .quick-action {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .quick-action:hover {
            transform: translateY(-2px);
        }

        .quick-action i {
            font-size: 24px;
            margin-bottom: 10px;
            color: #3498db;
        }

        .quick-action h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }

        .quick-action p {
            margin: 0;
            color: #7f8c8d;
            font-size: 12px;
        }

        .error-message {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        /* Settings Styles */
        .settings-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .settings-nav {
            width: 250px;
            background: white;
            border-radius: 8px;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .settings-nav-item {
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            display: flex;
            align-items: center;
            color: #7f8c8d;
        }

        .settings-nav-item:hover {
            background: #f8f9fa;
            color: #2c3e50;
        }

        .settings-nav-item.active {
            background: #e3f2fd;
            color: #3498db;
            border-left-color: #3498db;
        }

        .settings-nav-item i {
            margin-right: 10px;
            width: 20px;
        }

        .settings-content {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .settings-tab {
            display: none;
        }

        .settings-tab.active {
            display: block;
        }

        .settings-form {
            margin-top: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }

        /* User Management Styles */
        .users-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .users-actions {
            display: flex;
            gap: 10px;
        }

        .users-search {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .users-search input {
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            width: 250px;
        }

        .users-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
        }

        .users-table th,
        .users-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .users-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .user-meta {
            font-size: 12px;
            color: #7f8c8d;
        }

        .user-email {
            color: #3498db;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 5px;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-primary {
            background: #cce5ff;
            color: #004085;
        }

        .badge-secondary {
            background: #e2e3e5;
            color: #383d41;
        }

        .badge-gold {
            background: #ffd700;
            color: #b8860b;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-suspended {
            background: #fff3cd;
            color: #856404;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-icon {
            padding: 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #f8f9fa;
            color: #6c757d;
            transition: all 0.3s;
        }

        .btn-icon:hover {
            background: #e9ecef;
            color: #495057;
        }

        .btn-icon.btn-danger:hover {
            background: #f5c6cb;
            color: #721c24;
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 14px;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #7f8c8d;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #e74c3c;
        }

        /* Product Management Styles */
        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .products-actions {
            display: flex;
            gap: 10px;
        }

        .products-filters {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .products-filters select,
        .products-filters input {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }

        .products-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
        }

        .products-table th,
        .products-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .products-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .product-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 6px;
            object-fit: cover;
        }

        .product-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .product-sku {
            font-family: monospace;
            color: #6c757d;
            font-size: 12px;
        }

        .product-price {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .sale-price {
            color: #e74c3c;
            font-weight: 600;
        }

        .original-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 12px;
        }

        .price {
            color: #2c3e50;
            font-weight: 600;
        }

        .stock-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .in-stock {
            background: #d4edda;
            color: #155724;
        }

        .low-stock {
            background: #fff3cd;
            color: #856404;
        }

        .out-of-stock {
            background: #f8d7da;
            color: #721c24;
        }

        .product-rating {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .rating {
            color: #f39c12;
            font-weight: 600;
        }

        .reviews {
            color: #6c757d;
            font-size: 11px;
        }

        /* Order Management Styles */
        .orders-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .orders-actions {
            display: flex;
            gap: 10px;
        }

        .orders-filters {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .orders-filters select,
        .orders-filters input {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }

        .orders-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
        }

        .orders-table th,
        .orders-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .orders-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .order-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .order-number {
            font-weight: 600;
            color: #2c3e50;
            font-family: monospace;
        }

        .order-meta {
            font-size: 11px;
            color: #6c757d;
        }

        .customer-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .customer-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            object-fit: cover;
        }

        .customer-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .customer-email {
            font-size: 12px;
            color: #6c757d;
        }

        .payment-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .payment-paid {
            background: #d4edda;
            color: #155724;
        }

        .payment-pending {
            background: #fff3cd;
            color: #856404;
        }

        .payment-refunded {
            background: #f8d7da;
            color: #721c24;
        }

        .order-total {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .items-count {
            color: #6c757d;
            font-size: 14px;
        }

        .order-date {
            color: #6c757d;
            font-size: 14px;
        }

        /* Order Details Modal */
        .order-details-modal {
            max-width: 800px;
            max-height: 90vh;
        }

        .order-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .order-summary {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .order-items h4 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
        }

        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .items-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .item-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .item-image {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            object-fit: cover;
        }

        .item-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .item-sku {
            font-size: 11px;
            color: #6c757d;
            font-family: monospace;
        }

        .order-addresses {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .address-section h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .address {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            line-height: 1.5;
        }

        /* Analytics Styles */
        .analytics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .period-selector {
            display: flex;
            gap: 5px;
        }

        .period-btn {
            padding: 8px 16px;
            border: 2px solid #e0e0e0;
            background: white;
            color: #6c757d;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .period-btn.active,
        .period-btn:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .analytics-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .card-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .card-header i {
            color: #3498db;
            font-size: 20px;
        }

        .card-content {
            padding: 20px;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .metric-row:last-child {
            border-bottom: none;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .text-danger {
            color: #e74c3c !important;
        }

        .text-warning {
            color: #f39c12 !important;
        }

        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }

        .chart-card,
        .top-products-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .chart-container {
            padding: 20px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .products-list {
            padding: 20px;
        }

        .product-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-rank {
            width: 30px;
            height: 30px;
            background: #3498db;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .product-thumb {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            object-fit: cover;
        }

        .product-details {
            flex: 1;
        }

        .product-details .product-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .product-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #6c757d;
        }

        /* Feature Card Styles */
        .feature-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: 20px;
        }

        .feature-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
        }

        .feature-header h3 {
            margin: 0;
            font-size: 24px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-content {
            padding: 30px;
        }

        .feature-content p {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .feature-list li {
            padding: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 15px;
            color: #2c3e50;
        }

        .feature-list i {
            color: #27ae60;
            font-size: 14px;
        }

        .feature-actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <!-- Admin Dashboard Container -->
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> E-Commerce Admin</h2>
                <p>Management Dashboard</p>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="users">
                        <i class="fas fa-users"></i> User Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="products">
                        <i class="fas fa-box"></i> Product Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="orders">
                        <i class="fas fa-shopping-cart"></i> Order Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="transactions">
                        <i class="fas fa-credit-card"></i> Transactions
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="invoices">
                        <i class="fas fa-file-invoice"></i> Invoices
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="coupons">
                        <i class="fas fa-tags"></i> Coupons & Discounts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="media">
                        <i class="fas fa-images"></i> Media Library
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="analytics">
                        <i class="fas fa-chart-bar"></i> Analytics & Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <h1 id="pageTitle">Dashboard</h1>
                <div class="user-info">
                    <span id="userName">Admin User</span>
                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>

            <div class="content-area" id="contentArea">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading dashboard...
                </div>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="productModalTitle">Add New Product</h2>
                <span class="close" onclick="closeModal('productModal')">&times;</span>
            </div>
            <form id="productForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="productName">Product Name</label>
                        <input type="text" id="productName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="productSku">SKU</label>
                        <input type="text" id="productSku" name="sku" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="productPrice">Price ($)</label>
                        <input type="number" id="productPrice" name="price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="productStock">Stock Quantity</label>
                        <input type="number" id="productStock" name="stock_quantity" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="productCategory">Category</label>
                        <select id="productCategory" name="category_id" required>
                            <option value="">Select Category</option>
                            <option value="1">Electronics</option>
                            <option value="2">Clothing</option>
                            <option value="3">Books</option>
                            <option value="4">Home & Garden</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="productStatus">Status</label>
                        <select id="productStatus" name="is_active" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="productDescription">Description</label>
                    <textarea id="productDescription" name="description" placeholder="Enter product description..."></textarea>
                </div>

                <div class="form-group">
                    <label for="productImage">Product Image URL</label>
                    <input type="url" id="productImage" name="image_url" placeholder="https://example.com/image.jpg">
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="closeModal('productModal')">Cancel</button>
                    <button type="submit" class="btn btn-success">Save Product</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        let authToken = localStorage.getItem('auth_token');
        let currentUser = JSON.parse(localStorage.getItem('current_user') || '{}');

        // Check authentication on load
        if (!authToken) {
            window.location.href = 'admin-login.html';
        } else {
            verifyToken();
        }

        async function verifyToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/check`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.success && data.authenticated) {
                    if (currentUser.name) {
                        document.getElementById('userName').textContent = currentUser.name;
                    }
                    loadDashboardData();
                } else {
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('current_user');
                    window.location.href = 'admin-login.html';
                }
            } catch (error) {
                console.error('Token verification failed:', error);
                localStorage.removeItem('auth_token');
                localStorage.removeItem('current_user');
                window.location.href = 'admin-login.html';
            }
        }

        function logout() {
            fetch(`${API_BASE}/auth/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            }).finally(() => {
                localStorage.removeItem('auth_token');
                localStorage.removeItem('current_user');
                window.location.href = 'admin-login.html';
            });
        }

        // Navigation handler
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                // Update active state
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                link.classList.add('active');

                const section = link.dataset.section;
                loadSection(section);
            });
        });

        async function loadSection(section) {
            const pageTitle = document.getElementById('pageTitle');
            const contentArea = document.getElementById('contentArea');

            // Update page title
            const titles = {
                dashboard: 'Dashboard',
                users: 'User Management',
                products: 'Product Management',
                orders: 'Order Management',
                transactions: 'Transactions',
                invoices: 'Invoices',
                coupons: 'Coupons & Discounts',
                media: 'Media Library',
                analytics: 'Analytics & Reports',
                settings: 'Settings'
            };

            pageTitle.textContent = titles[section] || 'Dashboard';

            // Show loading
            contentArea.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    Loading ${titles[section]}...
                </div>
            `;

            // Load section content
            switch (section) {
                case 'dashboard':
                    await loadDashboardData();
                    break;
                case 'products':
                    await loadProductsSection();
                    break;
                case 'users':
                    await loadUsersSection();
                    break;
                case 'orders':
                    await loadOrdersSection();
                    break;
                case 'analytics':
                    await loadAnalyticsSection();
                    break;
                case 'settings':
                    await loadSettingsSection();
                    break;
                case 'transactions':
                    loadTransactionsSection();
                    break;
                case 'invoices':
                    loadInvoicesSection();
                    break;
                case 'coupons':
                    loadCouponsSection();
                    break;
                case 'media':
                    loadMediaSection();
                    break;
                default:
                    contentArea.innerHTML = `
                        <div class="section-title">${titles[section]}</div>
                        <p>This section is under development. All backend APIs are ready and functional.</p>
                        <div style="margin-top: 20px;">
                            <button class="btn" onclick="testAPI('${section}')">Test ${titles[section]} API</button>
                        </div>
                        <div id="apiResult-${section}" style="margin-top: 20px;"></div>
                    `;
            }
        }

        async function loadDashboardData() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/overview`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    renderDashboard(data.data);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="error-message" style="display: block;">
                        Failed to load dashboard data: ${error.message}
                    </div>
                `;
            }
        }

        function renderDashboard(data) {
            const contentArea = document.getElementById('contentArea');

            contentArea.innerHTML = `
                <div class="section-title">Dashboard Overview</div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Users</h3>
                        <div class="value">${data.stats.users.total}</div>
                        <div class="change">+${data.stats.users.active} active</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Products</h3>
                        <div class="value">${data.stats.products.total}</div>
                        <div class="change">+${data.stats.products.published} published</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Orders</h3>
                        <div class="value">${data.stats.orders.total}</div>
                        <div class="change">$${data.stats.orders.total_revenue} revenue</div>
                    </div>
                    <div class="stat-card">
                        <h3>Categories</h3>
                        <div class="value">${data.stats.categories.total}</div>
                        <div class="change">+${data.stats.categories.active} active</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div>
                        <h3 style="margin-bottom: 20px; color: #2c3e50;">Recent Activities</h3>
                        <div style="background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            ${data.recent_activities.map(activity => `
                                <div class="activity-item">
                                    <div class="activity-icon ${activity.color}">
                                        <i class="fas fa-${activity.icon}"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h4>${activity.title}</h4>
                                        <p>${activity.description}</p>
                                    </div>
                                    <div class="activity-time">${activity.time}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div>
                        <h3 style="margin-bottom: 20px; color: #2c3e50;">Quick Actions</h3>
                        <div class="quick-actions">
                            ${data.quick_actions.map(action => `
                                <div class="quick-action" onclick="loadSection('${action.route.replace('/', '')}')">
                                    <i class="fas fa-${action.icon}"></i>
                                    <h4>${action.title}</h4>
                                    <p>${action.description}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        async function loadProductsSection() {
            try {
                const response = await fetch(`${API_BASE}/products?page=1&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    document.getElementById('contentArea').innerHTML = `
                        <div class="section-title">Product Management</div>

                        <div class="products-header">
                            <div class="products-actions">
                                <button class="btn btn-success" onclick="openProductModal()">
                                    <i class="fas fa-plus"></i> Add New Product
                                </button>
                                <button class="btn btn-info" onclick="exportProducts()">
                                    <i class="fas fa-download"></i> Export Products
                                </button>
                                <button class="btn btn-warning" onclick="manageCategories()">
                                    <i class="fas fa-tags"></i> Categories
                                </button>
                            </div>
                            <div class="products-filters">
                                <select id="categoryFilter" onchange="filterProducts()">
                                    <option value="">All Categories</option>
                                    <option value="Electronics">Electronics</option>
                                    <option value="Clothing">Clothing</option>
                                    <option value="Home & Garden">Home & Garden</option>
                                    <option value="Accessories">Accessories</option>
                                </select>
                                <select id="statusFilter" onchange="filterProducts()">
                                    <option value="">All Status</option>
                                    <option value="published">Published</option>
                                    <option value="draft">Draft</option>
                                </select>
                                <input type="text" id="productSearch" placeholder="Search products..." onkeyup="searchProducts(this.value)">
                            </div>
                        </div>

                        <div class="products-table-container">
                            <table class="products-table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>SKU</th>
                                        <th>Price</th>
                                        <th>Stock</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Rating</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    ${data.data.map(product => `
                                        <tr>
                                            <td>
                                                <div class="product-info">
                                                    <img src="${product.image}" alt="${product.name}" class="product-image">
                                                    <div>
                                                        <div class="product-name">${product.name}</div>
                                                        ${product.featured ? '<span class="badge badge-gold">Featured</span>' : ''}
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="product-sku">${product.sku}</td>
                                            <td>
                                                <div class="product-price">
                                                    ${product.sale_price ?
                                                        `<span class="sale-price">$${product.sale_price}</span>
                                                         <span class="original-price">$${product.price}</span>` :
                                                        `<span class="price">$${product.price}</span>`
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <span class="stock-badge ${product.stock === 0 ? 'out-of-stock' : product.stock < 10 ? 'low-stock' : 'in-stock'}">
                                                    ${product.stock} ${product.stock === 0 ? '(Out of Stock)' : product.stock < 10 ? '(Low Stock)' : ''}
                                                </span>
                                            </td>
                                            <td>${product.category}</td>
                                            <td>
                                                <span class="status-badge status-${product.status}">${product.status}</span>
                                            </td>
                                            <td>
                                                <div class="product-rating">
                                                    <span class="rating">${product.rating}/5</span>
                                                    <span class="reviews">(${product.reviews})</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-icon" onclick="editProduct(${product.id})" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn-icon" onclick="viewProduct(${product.id})" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn-icon btn-danger" onclick="deleteProduct(${product.id})" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <button class="btn" onclick="loadProductsPage(${data.pagination.current_page - 1})" ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                                Previous
                            </button>
                            <span class="pagination-info">
                                Page ${data.pagination.current_page} of ${data.pagination.total_pages}
                                (${data.pagination.total} total products)
                            </span>
                            <button class="btn" onclick="loadProductsPage(${data.pagination.current_page + 1})" ${data.pagination.current_page >= data.pagination.total_pages ? 'disabled' : ''}>
                                Next
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(data.message || 'Failed to load products');
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="section-title">Product Management</div>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load products: ${error.message}
                    </div>
                `;
            }
        }

        function renderProductsSection(data) {
            const contentArea = document.getElementById('contentArea');

            contentArea.innerHTML = `
                <div class="section-title">Product Management</div>

                <div style="margin-bottom: 30px;">
                    <button class="btn btn-success" onclick="openModal('productModal')">
                        <i class="fas fa-plus"></i> Add New Product
                    </button>
                    <button class="btn" onclick="loadMockProducts()">Load Sample Products</button>
                    <button class="btn btn-warning" onclick="manageCategories()">Manage Categories</button>
                </div>

                <div class="stats-grid" style="margin-bottom: 30px;">
                    <div class="stat-card">
                        <h3>Total Products</h3>
                        <div class="value">${data.total_products || 0}</div>
                        <div class="change">All products in catalog</div>
                    </div>
                    <div class="stat-card">
                        <h3>Active Products</h3>
                        <div class="value">${data.active_products || 0}</div>
                        <div class="change">Currently published</div>
                    </div>
                    <div class="stat-card">
                        <h3>Out of Stock</h3>
                        <div class="value">${data.out_of_stock || 0}</div>
                        <div class="change">Need restocking</div>
                    </div>
                    <div class="stat-card">
                        <h3>Low Stock</h3>
                        <div class="value">${data.low_stock || 0}</div>
                        <div class="change">Below 10 units</div>
                    </div>
                </div>

                <div id="productsTable">
                    <h3 style="margin-bottom: 15px;">Product List</h3>
                    <div style="background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 20px;">
                        <p>Loading products...</p>
                    </div>
                </div>
            `;

            loadMockProducts();
        }

        function loadMockProducts() {
            const mockProducts = [
                {
                    id: 1,
                    name: 'iPhone 15 Pro',
                    sku: 'IPH15PRO',
                    price: 999.99,
                    stock_quantity: 25,
                    category: 'Electronics',
                    status: 'Active',
                    image: 'https://via.placeholder.com/50x50'
                },
                {
                    id: 2,
                    name: 'MacBook Air M2',
                    sku: 'MBA-M2',
                    price: 1199.99,
                    stock_quantity: 15,
                    category: 'Electronics',
                    status: 'Active',
                    image: 'https://via.placeholder.com/50x50'
                },
                {
                    id: 3,
                    name: 'Nike Air Max',
                    sku: 'NAM-001',
                    price: 129.99,
                    stock_quantity: 5,
                    category: 'Clothing',
                    status: 'Active',
                    image: 'https://via.placeholder.com/50x50'
                },
                {
                    id: 4,
                    name: 'Samsung Galaxy S24',
                    sku: 'SGS24',
                    price: 899.99,
                    stock_quantity: 0,
                    category: 'Electronics',
                    status: 'Inactive',
                    image: 'https://via.placeholder.com/50x50'
                }
            ];

            const tableContainer = document.getElementById('productsTable');
            if (tableContainer) {
                tableContainer.innerHTML = `
                    <h3 style="margin-bottom: 15px;">Product List</h3>
                    <div style="background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Product Name</th>
                                    <th>SKU</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${mockProducts.map(product => `
                                    <tr>
                                        <td><img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; border-radius: 4px;"></td>
                                        <td><strong>${product.name}</strong></td>
                                        <td>${product.sku}</td>
                                        <td>$${product.price}</td>
                                        <td>
                                            <span class="badge ${product.stock_quantity === 0 ? 'badge-danger' : product.stock_quantity < 10 ? 'badge-warning' : 'badge-success'}">
                                                ${product.stock_quantity} units
                                            </span>
                                        </td>
                                        <td>${product.category}</td>
                                        <td>
                                            <span class="badge ${product.status === 'Active' ? 'badge-success' : 'badge-danger'}">
                                                ${product.status}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn" onclick="editProduct(${product.id})" style="padding: 5px 10px; margin: 2px;">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-danger" onclick="deleteProduct(${product.id})" style="padding: 5px 10px; margin: 2px;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Product form handler
        document.getElementById('productForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const productData = Object.fromEntries(formData);

            // Simulate API call
            try {
                showSuccess('Product saved successfully!');
                closeModal('productModal');
                loadMockProducts(); // Refresh the product list
                e.target.reset(); // Clear the form
            } catch (error) {
                showError('Failed to save product: ' + error.message);
            }
        });

        function editProduct(id) {
            openModal('productModal');
            document.getElementById('productModalTitle').textContent = 'Edit Product';
            // In a real app, you would load the product data here
            showSuccess('Edit functionality would load product data here');
        }

        function deleteProduct(id) {
            if (confirm('Are you sure you want to delete this product?')) {
                showSuccess('Product deleted successfully!');
                loadMockProducts(); // Refresh the product list
            }
        }

        function manageCategories() {
            showSuccess('Category management would open here');
        }

        // Other section loaders
        async function loadUsersSection() {
            try {
                const response = await fetch(`${API_BASE}/users?page=1&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    document.getElementById('contentArea').innerHTML = `
                        <div class="section-title">User Management</div>

                        <div class="users-header">
                            <div class="users-actions">
                                <button class="btn btn-success" onclick="openUserModal()">
                                    <i class="fas fa-plus"></i> Add New User
                                </button>
                                <button class="btn btn-info" onclick="exportUsers()">
                                    <i class="fas fa-download"></i> Export Users
                                </button>
                            </div>
                            <div class="users-search">
                                <input type="text" id="userSearch" placeholder="Search users..." onkeyup="searchUsers(this.value)">
                                <button class="btn" onclick="searchUsers(document.getElementById('userSearch').value)">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>

                        <div class="users-table-container">
                            <table class="users-table">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Orders</th>
                                        <th>Total Spent</th>
                                        <th>Last Login</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    ${data.data.map(user => `
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <img src="${user.avatar}" alt="${user.name}" class="user-avatar">
                                                    <div>
                                                        <div class="user-name">${user.name}</div>
                                                        <div class="user-meta">${user.country}, ${user.city}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="user-email">${user.email}</div>
                                                ${user.verified ? '<span class="badge badge-success">Verified</span>' : '<span class="badge badge-warning">Unverified</span>'}
                                            </td>
                                            <td>
                                                <span class="badge badge-${user.role === 'admin' ? 'primary' : 'secondary'}">${user.role}</span>
                                                ${user.premium ? '<span class="badge badge-gold">Premium</span>' : ''}
                                            </td>
                                            <td>
                                                <span class="status-badge status-${user.status}">${user.status}</span>
                                            </td>
                                            <td>${user.total_orders}</td>
                                            <td>$${user.total_spent.toFixed(2)}</td>
                                            <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-icon" onclick="editUser(${user.id})" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn-icon" onclick="viewUser(${user.id})" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn-icon btn-danger" onclick="deleteUser(${user.id})" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <button class="btn" onclick="loadUsersPage(${data.pagination.current_page - 1})" ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                                Previous
                            </button>
                            <span class="pagination-info">
                                Page ${data.pagination.current_page} of ${data.pagination.total_pages}
                                (${data.pagination.total} total users)
                            </span>
                            <button class="btn" onclick="loadUsersPage(${data.pagination.current_page + 1})" ${data.pagination.current_page >= data.pagination.total_pages ? 'disabled' : ''}>
                                Next
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(data.message || 'Failed to load users');
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="section-title">User Management</div>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load users: ${error.message}
                    </div>
                `;
            }
        }

        async function loadOrdersSection() {
            try {
                const response = await fetch(`${API_BASE}/orders?page=1&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    document.getElementById('contentArea').innerHTML = `
                        <div class="section-title">Order Management</div>

                        <div class="orders-header">
                            <div class="orders-actions">
                                <button class="btn btn-success" onclick="createNewOrder()">
                                    <i class="fas fa-plus"></i> Create Order
                                </button>
                                <button class="btn btn-info" onclick="exportOrders()">
                                    <i class="fas fa-download"></i> Export Orders
                                </button>
                                <button class="btn btn-warning" onclick="bulkActions()">
                                    <i class="fas fa-tasks"></i> Bulk Actions
                                </button>
                            </div>
                            <div class="orders-filters">
                                <select id="statusFilter" onchange="filterOrders()">
                                    <option value="">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                                <input type="text" id="orderSearch" placeholder="Search orders..." onkeyup="searchOrders(this.value)">
                            </div>
                        </div>

                        <div class="orders-table-container">
                            <table class="orders-table">
                                <thead>
                                    <tr>
                                        <th>Order</th>
                                        <th>Customer</th>
                                        <th>Status</th>
                                        <th>Payment</th>
                                        <th>Total</th>
                                        <th>Items</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="ordersTableBody">
                                    ${data.data.map(order => `
                                        <tr>
                                            <td>
                                                <div class="order-info">
                                                    <div class="order-number">${order.order_number}</div>
                                                    <div class="order-meta">ID: ${order.id}</div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="customer-info">
                                                    <img src="${order.customer.avatar}" alt="${order.customer.name}" class="customer-avatar">
                                                    <div>
                                                        <div class="customer-name">${order.customer.name}</div>
                                                        <div class="customer-email">${order.customer.email}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status-badge status-${order.status}">${order.status}</span>
                                            </td>
                                            <td>
                                                <span class="payment-badge payment-${order.payment_status}">${order.payment_status}</span>
                                            </td>
                                            <td class="order-total">$${order.total.toFixed(2)}</td>
                                            <td class="items-count">${order.items_count} items</td>
                                            <td class="order-date">${new Date(order.created_at).toLocaleDateString()}</td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-icon" onclick="viewOrder(${order.id})" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn-icon" onclick="editOrderStatus(${order.id})" title="Update Status">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn-icon" onclick="printInvoice(${order.id})" title="Print Invoice">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <button class="btn" onclick="loadOrdersPage(${data.pagination.current_page - 1})" ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                                Previous
                            </button>
                            <span class="pagination-info">
                                Page ${data.pagination.current_page} of ${data.pagination.total_pages}
                                (${data.pagination.total} total orders)
                            </span>
                            <button class="btn" onclick="loadOrdersPage(${data.pagination.current_page + 1})" ${data.pagination.current_page >= data.pagination.total_pages ? 'disabled' : ''}>
                                Next
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(data.message || 'Failed to load orders');
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="section-title">Order Management</div>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load orders: ${error.message}
                    </div>
                `;
            }
        }

        async function loadAnalyticsSection() {
            try {
                const [overviewResponse, salesResponse, productsResponse] = await Promise.all([
                    fetch(`${API_BASE}/analytics/overview`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    fetch(`${API_BASE}/analytics/sales-chart?period=7days`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    fetch(`${API_BASE}/analytics/top-products`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    })
                ]);

                const [overviewData, salesData, productsData] = await Promise.all([
                    overviewResponse.json(),
                    salesResponse.json(),
                    productsResponse.json()
                ]);

                if (overviewData.success && salesData.success && productsData.success) {
                    const overview = overviewData.data;
                    const sales = salesData.data;
                    const products = productsData.data;

                    document.getElementById('contentArea').innerHTML = `
                        <div class="section-title">Analytics & Reports</div>

                        <div class="analytics-header">
                            <div class="period-selector">
                                <button class="btn period-btn active" onclick="changePeriod('7days')">7 Days</button>
                                <button class="btn period-btn" onclick="changePeriod('30days')">30 Days</button>
                                <button class="btn period-btn" onclick="changePeriod('90days')">90 Days</button>
                            </div>
                            <div class="export-actions">
                                <button class="btn btn-info" onclick="exportReport()">
                                    <i class="fas fa-download"></i> Export Report
                                </button>
                            </div>
                        </div>

                        <div class="analytics-grid">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Revenue Overview</h3>
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="card-content">
                                    <div class="metric-row">
                                        <span>Today</span>
                                        <span class="metric-value">$${overview.revenue.today.toFixed(2)}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>This Week</span>
                                        <span class="metric-value">$${overview.revenue.this_week.toFixed(2)}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>This Month</span>
                                        <span class="metric-value">$${overview.revenue.this_month.toFixed(2)}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>This Year</span>
                                        <span class="metric-value">$${overview.revenue.this_year.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Orders Overview</h3>
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="card-content">
                                    <div class="metric-row">
                                        <span>Today</span>
                                        <span class="metric-value">${overview.orders.today}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>This Week</span>
                                        <span class="metric-value">${overview.orders.this_week}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Pending</span>
                                        <span class="metric-value">${overview.orders.pending}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Processing</span>
                                        <span class="metric-value">${overview.orders.processing}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Customer Metrics</h3>
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="card-content">
                                    <div class="metric-row">
                                        <span>Total Customers</span>
                                        <span class="metric-value">${overview.customers.total}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>New Today</span>
                                        <span class="metric-value">${overview.customers.new_today}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Active</span>
                                        <span class="metric-value">${overview.customers.active_customers}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Returning</span>
                                        <span class="metric-value">${overview.customers.returning_customers}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Product Stats</h3>
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="card-content">
                                    <div class="metric-row">
                                        <span>Total Products</span>
                                        <span class="metric-value">${overview.products.total}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Published</span>
                                        <span class="metric-value">${overview.products.published}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Out of Stock</span>
                                        <span class="metric-value text-danger">${overview.products.out_of_stock}</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Low Stock</span>
                                        <span class="metric-value text-warning">${overview.products.low_stock}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="charts-section">
                            <div class="chart-card">
                                <div class="card-header">
                                    <h3>Sales Trend (Last 7 Days)</h3>
                                </div>
                                <div class="chart-container">
                                    <canvas id="salesChart" width="400" height="200"></canvas>
                                </div>
                            </div>

                            <div class="top-products-card">
                                <div class="card-header">
                                    <h3>Top Selling Products</h3>
                                </div>
                                <div class="products-list">
                                    ${products.map((product, index) => `
                                        <div class="product-item">
                                            <div class="product-rank">#${index + 1}</div>
                                            <img src="${product.image}" alt="${product.name}" class="product-thumb">
                                            <div class="product-details">
                                                <div class="product-name">${product.name}</div>
                                                <div class="product-stats">
                                                    <span>${product.sales} sales</span>
                                                    <span>$${product.revenue.toFixed(2)}</span>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    `;

                    // Initialize chart
                    initializeSalesChart(sales);
                } else {
                    throw new Error('Failed to load analytics data');
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="section-title">Analytics & Reports</div>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load analytics: ${error.message}
                    </div>
                `;
            }
        }

        async function loadSettingsSection() {
            try {
                const response = await fetch(`${API_BASE}/settings`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    const settings = data.data;
                    document.getElementById('contentArea').innerHTML = `
                        <div class="section-title">Settings</div>

                        <div class="settings-container">
                            <div class="settings-nav">
                                <div class="settings-nav-item active" data-tab="general">
                                    <i class="fas fa-cog"></i> General
                                </div>
                                <div class="settings-nav-item" data-tab="email">
                                    <i class="fas fa-envelope"></i> Email
                                </div>
                                <div class="settings-nav-item" data-tab="payment">
                                    <i class="fas fa-credit-card"></i> Payment
                                </div>
                                <div class="settings-nav-item" data-tab="notifications">
                                    <i class="fas fa-bell"></i> Notifications
                                </div>
                                <div class="settings-nav-item" data-tab="security">
                                    <i class="fas fa-shield-alt"></i> Security
                                </div>
                                <div class="settings-nav-item" data-tab="seo">
                                    <i class="fas fa-search"></i> SEO
                                </div>
                            </div>

                            <div class="settings-content">
                                <div id="settings-general" class="settings-tab active">
                                    <h3>General Settings</h3>
                                    <form class="settings-form" data-category="general">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Site Name</label>
                                                <input type="text" name="site_name" value="${settings.general.site_name}" required>
                                            </div>
                                            <div class="form-group">
                                                <label>Admin Email</label>
                                                <input type="email" name="admin_email" value="${settings.general.admin_email}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Site Description</label>
                                            <textarea name="site_description" rows="3">${settings.general.site_description}</textarea>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Site URL</label>
                                                <input type="url" name="site_url" value="${settings.general.site_url}">
                                            </div>
                                            <div class="form-group">
                                                <label>Timezone</label>
                                                <select name="timezone">
                                                    <option value="UTC" ${settings.general.timezone === 'UTC' ? 'selected' : ''}>UTC</option>
                                                    <option value="America/New_York" ${settings.general.timezone === 'America/New_York' ? 'selected' : ''}>Eastern Time</option>
                                                    <option value="America/Chicago" ${settings.general.timezone === 'America/Chicago' ? 'selected' : ''}>Central Time</option>
                                                    <option value="America/Los_Angeles" ${settings.general.timezone === 'America/Los_Angeles' ? 'selected' : ''}>Pacific Time</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Currency</label>
                                                <select name="currency">
                                                    <option value="USD" ${settings.general.currency === 'USD' ? 'selected' : ''}>USD - US Dollar</option>
                                                    <option value="EUR" ${settings.general.currency === 'EUR' ? 'selected' : ''}>EUR - Euro</option>
                                                    <option value="GBP" ${settings.general.currency === 'GBP' ? 'selected' : ''}>GBP - British Pound</option>
                                                    <option value="CAD" ${settings.general.currency === 'CAD' ? 'selected' : ''}>CAD - Canadian Dollar</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>Language</label>
                                                <select name="language">
                                                    <option value="en" ${settings.general.language === 'en' ? 'selected' : ''}>English</option>
                                                    <option value="es" ${settings.general.language === 'es' ? 'selected' : ''}>Spanish</option>
                                                    <option value="fr" ${settings.general.language === 'fr' ? 'selected' : ''}>French</option>
                                                    <option value="de" ${settings.general.language === 'de' ? 'selected' : ''}>German</option>
                                                </select>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-success">Save General Settings</button>
                                    </form>
                                </div>

                                <div id="settings-email" class="settings-tab">
                                    <h3>Email Settings</h3>
                                    <form class="settings-form" data-category="email">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>SMTP Host</label>
                                                <input type="text" name="smtp_host" value="${settings.email.smtp_host}" required>
                                            </div>
                                            <div class="form-group">
                                                <label>SMTP Port</label>
                                                <input type="number" name="smtp_port" value="${settings.email.smtp_port}" required>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>SMTP Username</label>
                                                <input type="text" name="smtp_username" value="${settings.email.smtp_username}">
                                            </div>
                                            <div class="form-group">
                                                <label>SMTP Password</label>
                                                <input type="password" name="smtp_password" value="${settings.email.smtp_password}">
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>From Name</label>
                                                <input type="text" name="from_name" value="${settings.email.from_name}">
                                            </div>
                                            <div class="form-group">
                                                <label>From Email</label>
                                                <input type="email" name="from_email" value="${settings.email.from_email}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Encryption</label>
                                            <select name="smtp_encryption">
                                                <option value="tls" ${settings.email.smtp_encryption === 'tls' ? 'selected' : ''}>TLS</option>
                                                <option value="ssl" ${settings.email.smtp_encryption === 'ssl' ? 'selected' : ''}>SSL</option>
                                                <option value="none" ${settings.email.smtp_encryption === 'none' ? 'selected' : ''}>None</option>
                                            </select>
                                        </div>
                                        <div class="form-actions">
                                            <button type="submit" class="btn btn-success">Save Email Settings</button>
                                            <button type="button" class="btn btn-info" onclick="testEmailSettings()">Test Email</button>
                                        </div>
                                    </form>
                                </div>

                                <div id="settings-payment" class="settings-tab">
                                    <h3>Payment Settings</h3>
                                    <form class="settings-form" data-category="payment">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="stripe_enabled" ${settings.payment.stripe_enabled ? 'checked' : ''}>
                                                    Enable Stripe
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="paypal_enabled" ${settings.payment.paypal_enabled ? 'checked' : ''}>
                                                    Enable PayPal
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Stripe Public Key</label>
                                                <input type="text" name="stripe_public_key" value="${settings.payment.stripe_public_key}">
                                            </div>
                                            <div class="form-group">
                                                <label>Stripe Secret Key</label>
                                                <input type="password" name="stripe_secret_key" value="${settings.payment.stripe_secret_key}">
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Tax Rate (%)</label>
                                                <input type="number" name="tax_rate" value="${settings.payment.tax_rate}" step="0.1" min="0" max="100">
                                            </div>
                                            <div class="form-group">
                                                <label>Free Shipping Threshold ($)</label>
                                                <input type="number" name="free_shipping_threshold" value="${settings.payment.free_shipping_threshold}" step="0.01" min="0">
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-success">Save Payment Settings</button>
                                    </form>
                                </div>

                                <div id="settings-notifications" class="settings-tab">
                                    <h3>Notification Settings</h3>
                                    <form class="settings-form" data-category="notifications">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="email_notifications" ${settings.notifications.email_notifications ? 'checked' : ''}>
                                                    Email Notifications
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="sms_notifications" ${settings.notifications.sms_notifications ? 'checked' : ''}>
                                                    SMS Notifications
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="order_notifications" ${settings.notifications.order_notifications ? 'checked' : ''}>
                                                    Order Notifications
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="inventory_alerts" ${settings.notifications.inventory_alerts ? 'checked' : ''}>
                                                    Inventory Alerts
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Low Stock Threshold</label>
                                            <input type="number" name="low_stock_threshold" value="${settings.notifications.low_stock_threshold}" min="1">
                                        </div>
                                        <button type="submit" class="btn btn-success">Save Notification Settings</button>
                                    </form>
                                </div>

                                <div id="settings-security" class="settings-tab">
                                    <h3>Security Settings</h3>
                                    <form class="settings-form" data-category="security">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="two_factor_auth" ${settings.security.two_factor_auth ? 'checked' : ''}>
                                                    Two-Factor Authentication
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="require_email_verification" ${settings.security.require_email_verification ? 'checked' : ''}>
                                                    Require Email Verification
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Session Timeout (hours)</label>
                                                <input type="number" name="session_timeout" value="${settings.security.session_timeout}" min="1" max="168">
                                            </div>
                                            <div class="form-group">
                                                <label>Password Minimum Length</label>
                                                <input type="number" name="password_min_length" value="${settings.security.password_min_length}" min="6" max="50">
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Login Attempts Limit</label>
                                                <input type="number" name="login_attempts_limit" value="${settings.security.login_attempts_limit}" min="3" max="10">
                                            </div>
                                            <div class="form-group">
                                                <label>Account Lockout Duration (minutes)</label>
                                                <input type="number" name="account_lockout_duration" value="${settings.security.account_lockout_duration}" min="5" max="1440">
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-success">Save Security Settings</button>
                                    </form>
                                </div>

                                <div id="settings-seo" class="settings-tab">
                                    <h3>SEO Settings</h3>
                                    <form class="settings-form" data-category="seo">
                                        <div class="form-group">
                                            <label>Meta Title</label>
                                            <input type="text" name="meta_title" value="${settings.seo.meta_title}" maxlength="60">
                                        </div>
                                        <div class="form-group">
                                            <label>Meta Description</label>
                                            <textarea name="meta_description" rows="3" maxlength="160">${settings.seo.meta_description}</textarea>
                                        </div>
                                        <div class="form-group">
                                            <label>Meta Keywords</label>
                                            <input type="text" name="meta_keywords" value="${settings.seo.meta_keywords}">
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Google Analytics ID</label>
                                                <input type="text" name="google_analytics_id" value="${settings.seo.google_analytics_id}" placeholder="GA-XXXXXXXXX">
                                            </div>
                                            <div class="form-group">
                                                <label>Facebook Pixel ID</label>
                                                <input type="text" name="facebook_pixel_id" value="${settings.seo.facebook_pixel_id}">
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="sitemap_enabled" ${settings.seo.sitemap_enabled ? 'checked' : ''}>
                                                    Enable Sitemap
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="robots_txt_enabled" ${settings.seo.robots_txt_enabled ? 'checked' : ''}>
                                                    Enable Robots.txt
                                                </label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-success">Save SEO Settings</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add settings navigation functionality
                    setupSettingsNavigation();
                    setupSettingsForms();
                } else {
                    throw new Error(data.message || 'Failed to load settings');
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="section-title">Settings</div>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load settings: ${error.message}
                    </div>
                `;
            }
        }

        function setupSettingsNavigation() {
            document.querySelectorAll('.settings-nav-item').forEach(item => {
                item.addEventListener('click', () => {
                    // Remove active class from all nav items and tabs
                    document.querySelectorAll('.settings-nav-item').forEach(nav => nav.classList.remove('active'));
                    document.querySelectorAll('.settings-tab').forEach(tab => tab.classList.remove('active'));

                    // Add active class to clicked nav item
                    item.classList.add('active');

                    // Show corresponding tab
                    const tabId = 'settings-' + item.dataset.tab;
                    const tab = document.getElementById(tabId);
                    if (tab) {
                        tab.classList.add('active');
                    }
                });
            });
        }

        function setupSettingsForms() {
            document.querySelectorAll('.settings-form').forEach(form => {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const category = form.dataset.category;
                    const formData = new FormData(form);
                    const settings = {};

                    // Convert FormData to object
                    for (let [key, value] of formData.entries()) {
                        settings[key] = value;
                    }

                    try {
                        const response = await fetch(`${API_BASE}/settings`, {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${authToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ category, settings })
                        });

                        const data = await response.json();

                        if (data.success) {
                            showMessage('success', data.message);
                        } else {
                            showMessage('error', data.message || 'Failed to save settings');
                        }
                    } catch (error) {
                        showMessage('error', 'Failed to save settings: ' + error.message);
                    }
                });
            });
        }

        async function testEmailSettings() {
            const emailInput = prompt('Enter email address to test:');
            if (!emailInput) return;

            try {
                const response = await fetch(`${API_BASE}/settings/test-email`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: emailInput })
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('success', data.message);
                } else {
                    showMessage('error', data.message || 'Email test failed');
                }
            } catch (error) {
                showMessage('error', 'Email test failed: ' + error.message);
            }
        }

        function showMessage(type, message) {
            // Remove existing messages
            document.querySelectorAll('.message').forEach(msg => msg.remove());

            // Create new message
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.style.display = 'block';
            messageDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}`;

            // Insert at top of settings content
            const settingsContent = document.querySelector('.settings-content');
            if (settingsContent) {
                settingsContent.insertBefore(messageDiv, settingsContent.firstChild);

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    messageDiv.remove();
                }, 5000);
            }
        }

        // User Management Functions
        async function loadUsersPage(page) {
            if (page < 1) return;

            try {
                const response = await fetch(`${API_BASE}/users?page=${page}&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update table body
                    document.getElementById('usersTableBody').innerHTML = data.data.map(user => `
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="${user.avatar}" alt="${user.name}" class="user-avatar">
                                    <div>
                                        <div class="user-name">${user.name}</div>
                                        <div class="user-meta">${user.country}, ${user.city}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="user-email">${user.email}</div>
                                ${user.verified ? '<span class="badge badge-success">Verified</span>' : '<span class="badge badge-warning">Unverified</span>'}
                            </td>
                            <td>
                                <span class="badge badge-${user.role === 'admin' ? 'primary' : 'secondary'}">${user.role}</span>
                                ${user.premium ? '<span class="badge badge-gold">Premium</span>' : ''}
                            </td>
                            <td>
                                <span class="status-badge status-${user.status}">${user.status}</span>
                            </td>
                            <td>${user.total_orders}</td>
                            <td>$${user.total_spent.toFixed(2)}</td>
                            <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="editUser(${user.id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="viewUser(${user.id})" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon btn-danger" onclick="deleteUser(${user.id})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');

                    // Update pagination
                    document.querySelector('.pagination').innerHTML = `
                        <button class="btn" onclick="loadUsersPage(${data.pagination.current_page - 1})" ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                            Previous
                        </button>
                        <span class="pagination-info">
                            Page ${data.pagination.current_page} of ${data.pagination.total_pages}
                            (${data.pagination.total} total users)
                        </span>
                        <button class="btn" onclick="loadUsersPage(${data.pagination.current_page + 1})" ${data.pagination.current_page >= data.pagination.total_pages ? 'disabled' : ''}>
                            Next
                        </button>
                    `;
                }
            } catch (error) {
                showError('Failed to load users: ' + error.message);
            }
        }

        async function searchUsers(query) {
            try {
                const response = await fetch(`${API_BASE}/users?search=${encodeURIComponent(query)}&page=1&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update table with search results
                    document.getElementById('usersTableBody').innerHTML = data.data.map(user => `
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="${user.avatar}" alt="${user.name}" class="user-avatar">
                                    <div>
                                        <div class="user-name">${user.name}</div>
                                        <div class="user-meta">${user.country}, ${user.city}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="user-email">${user.email}</div>
                                ${user.verified ? '<span class="badge badge-success">Verified</span>' : '<span class="badge badge-warning">Unverified</span>'}
                            </td>
                            <td>
                                <span class="badge badge-${user.role === 'admin' ? 'primary' : 'secondary'}">${user.role}</span>
                                ${user.premium ? '<span class="badge badge-gold">Premium</span>' : ''}
                            </td>
                            <td>
                                <span class="status-badge status-${user.status}">${user.status}</span>
                            </td>
                            <td>${user.total_orders}</td>
                            <td>$${user.total_spent.toFixed(2)}</td>
                            <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="editUser(${user.id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="viewUser(${user.id})" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon btn-danger" onclick="deleteUser(${user.id})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');
                }
            } catch (error) {
                showError('Search failed: ' + error.message);
            }
        }

        function openUserModal(userId = null) {
            const isEdit = userId !== null;
            const title = isEdit ? 'Edit User' : 'Add New User';

            // Create modal HTML
            const modalHTML = `
                <div class="modal-overlay" onclick="closeModal()">
                    <div class="modal-content" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h3>${title}</h3>
                            <button class="close-btn" onclick="closeModal()">&times;</button>
                        </div>
                        <form id="userForm" onsubmit="saveUser(event, ${userId})">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>Name *</label>
                                    <input type="text" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label>Email *</label>
                                    <input type="email" name="email" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>Role</label>
                                    <select name="role">
                                        <option value="customer">Customer</option>
                                        <option value="admin">Admin</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Status</label>
                                    <select name="status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="suspended">Suspended</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-success">${isEdit ? 'Update' : 'Create'} User</button>
                                <button type="button" class="btn" onclick="closeModal()">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        async function saveUser(event, userId = null) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const userData = {};

            for (let [key, value] of formData.entries()) {
                userData[key] = value;
            }

            try {
                const url = userId ? `${API_BASE}/users/${userId}` : `${API_BASE}/users`;
                const method = userId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess(data.message);
                    closeModal();
                    loadUsersSection(); // Reload users list
                } else {
                    showError(data.message || 'Failed to save user');
                }
            } catch (error) {
                showError('Failed to save user: ' + error.message);
            }
        }

        function editUser(userId) {
            // In a real app, you would fetch user data first
            openUserModal(userId);
        }

        function viewUser(userId) {
            alert(`View user details for user ID: ${userId}\n\nThis would open a detailed user profile view.`);
        }

        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess(data.message);
                    loadUsersSection(); // Reload users list
                } else {
                    showError(data.message || 'Failed to delete user');
                }
            } catch (error) {
                showError('Failed to delete user: ' + error.message);
            }
        }

        function exportUsers() {
            // Simulate user export
            const csvContent = "Name,Email,Role,Status,Orders,Total Spent\\n";
            // In a real app, you would fetch all users and generate CSV

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'users_export.csv';
            a.click();
            window.URL.revokeObjectURL(url);

            showSuccess('Users exported successfully!');
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        // Product Management Functions
        async function loadProductsPage(page) {
            if (page < 1) return;

            try {
                const response = await fetch(`${API_BASE}/products?page=${page}&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update table body
                    document.getElementById('productsTableBody').innerHTML = data.data.map(product => `
                        <tr>
                            <td>
                                <div class="product-info">
                                    <img src="${product.image}" alt="${product.name}" class="product-image">
                                    <div>
                                        <div class="product-name">${product.name}</div>
                                        ${product.featured ? '<span class="badge badge-gold">Featured</span>' : ''}
                                    </div>
                                </div>
                            </td>
                            <td class="product-sku">${product.sku}</td>
                            <td>
                                <div class="product-price">
                                    ${product.sale_price ?
                                        `<span class="sale-price">$${product.sale_price}</span>
                                         <span class="original-price">$${product.price}</span>` :
                                        `<span class="price">$${product.price}</span>`
                                    }
                                </div>
                            </td>
                            <td>
                                <span class="stock-badge ${product.stock === 0 ? 'out-of-stock' : product.stock < 10 ? 'low-stock' : 'in-stock'}">
                                    ${product.stock} ${product.stock === 0 ? '(Out of Stock)' : product.stock < 10 ? '(Low Stock)' : ''}
                                </span>
                            </td>
                            <td>${product.category}</td>
                            <td>
                                <span class="status-badge status-${product.status}">${product.status}</span>
                            </td>
                            <td>
                                <div class="product-rating">
                                    <span class="rating">${product.rating}/5</span>
                                    <span class="reviews">(${product.reviews})</span>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="editProduct(${product.id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="viewProduct(${product.id})" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon btn-danger" onclick="deleteProduct(${product.id})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');

                    // Update pagination
                    document.querySelector('.pagination').innerHTML = `
                        <button class="btn" onclick="loadProductsPage(${data.pagination.current_page - 1})" ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                            Previous
                        </button>
                        <span class="pagination-info">
                            Page ${data.pagination.current_page} of ${data.pagination.total_pages}
                            (${data.pagination.total} total products)
                        </span>
                        <button class="btn" onclick="loadProductsPage(${data.pagination.current_page + 1})" ${data.pagination.current_page >= data.pagination.total_pages ? 'disabled' : ''}>
                            Next
                        </button>
                    `;
                }
            } catch (error) {
                showError('Failed to load products: ' + error.message);
            }
        }

        async function searchProducts(query) {
            try {
                const response = await fetch(`${API_BASE}/products?search=${encodeURIComponent(query)}&page=1&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update table with search results
                    document.getElementById('productsTableBody').innerHTML = data.data.map(product => `
                        <tr>
                            <td>
                                <div class="product-info">
                                    <img src="${product.image}" alt="${product.name}" class="product-image">
                                    <div>
                                        <div class="product-name">${product.name}</div>
                                        ${product.featured ? '<span class="badge badge-gold">Featured</span>' : ''}
                                    </div>
                                </div>
                            </td>
                            <td class="product-sku">${product.sku}</td>
                            <td>
                                <div class="product-price">
                                    ${product.sale_price ?
                                        `<span class="sale-price">$${product.sale_price}</span>
                                         <span class="original-price">$${product.price}</span>` :
                                        `<span class="price">$${product.price}</span>`
                                    }
                                </div>
                            </td>
                            <td>
                                <span class="stock-badge ${product.stock === 0 ? 'out-of-stock' : product.stock < 10 ? 'low-stock' : 'in-stock'}">
                                    ${product.stock} ${product.stock === 0 ? '(Out of Stock)' : product.stock < 10 ? '(Low Stock)' : ''}
                                </span>
                            </td>
                            <td>${product.category}</td>
                            <td>
                                <span class="status-badge status-${product.status}">${product.status}</span>
                            </td>
                            <td>
                                <div class="product-rating">
                                    <span class="rating">${product.rating}/5</span>
                                    <span class="reviews">(${product.reviews})</span>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="editProduct(${product.id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="viewProduct(${product.id})" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon btn-danger" onclick="deleteProduct(${product.id})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');
                }
            } catch (error) {
                showError('Search failed: ' + error.message);
            }
        }

        function filterProducts() {
            const category = document.getElementById('categoryFilter').value;
            const status = document.getElementById('statusFilter').value;

            let url = `${API_BASE}/products?page=1&limit=20`;
            if (category) url += `&category=${encodeURIComponent(category)}`;
            if (status) url += `&status=${status}`;

            fetch(url, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update table with filtered results
                    document.getElementById('productsTableBody').innerHTML = data.data.map(product => `
                        <tr>
                            <td>
                                <div class="product-info">
                                    <img src="${product.image}" alt="${product.name}" class="product-image">
                                    <div>
                                        <div class="product-name">${product.name}</div>
                                        ${product.featured ? '<span class="badge badge-gold">Featured</span>' : ''}
                                    </div>
                                </div>
                            </td>
                            <td class="product-sku">${product.sku}</td>
                            <td>
                                <div class="product-price">
                                    ${product.sale_price ?
                                        `<span class="sale-price">$${product.sale_price}</span>
                                         <span class="original-price">$${product.price}</span>` :
                                        `<span class="price">$${product.price}</span>`
                                    }
                                </div>
                            </td>
                            <td>
                                <span class="stock-badge ${product.stock === 0 ? 'out-of-stock' : product.stock < 10 ? 'low-stock' : 'in-stock'}">
                                    ${product.stock} ${product.stock === 0 ? '(Out of Stock)' : product.stock < 10 ? '(Low Stock)' : ''}
                                </span>
                            </td>
                            <td>${product.category}</td>
                            <td>
                                <span class="status-badge status-${product.status}">${product.status}</span>
                            </td>
                            <td>
                                <div class="product-rating">
                                    <span class="rating">${product.rating}/5</span>
                                    <span class="reviews">(${product.reviews})</span>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="editProduct(${product.id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="viewProduct(${product.id})" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon btn-danger" onclick="deleteProduct(${product.id})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');
                }
            })
            .catch(error => {
                showError('Filter failed: ' + error.message);
            });
        }

        function openProductModal(productId = null) {
            const isEdit = productId !== null;
            const title = isEdit ? 'Edit Product' : 'Add New Product';

            const modalHTML = `
                <div class="modal-overlay" onclick="closeModal()">
                    <div class="modal-content" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h3>${title}</h3>
                            <button class="close-btn" onclick="closeModal()">&times;</button>
                        </div>
                        <form id="productForm" onsubmit="saveProduct(event, ${productId})">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>Product Name *</label>
                                    <input type="text" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label>SKU *</label>
                                    <input type="text" name="sku" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>Price *</label>
                                    <input type="number" name="price" step="0.01" min="0" required>
                                </div>
                                <div class="form-group">
                                    <label>Cost</label>
                                    <input type="number" name="cost" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>Stock Quantity *</label>
                                    <input type="number" name="stock" min="0" required>
                                </div>
                                <div class="form-group">
                                    <label>Category *</label>
                                    <select name="category" required>
                                        <option value="">Select Category</option>
                                        <option value="Electronics">Electronics</option>
                                        <option value="Clothing">Clothing</option>
                                        <option value="Home & Garden">Home & Garden</option>
                                        <option value="Accessories">Accessories</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status">
                                    <option value="draft">Draft</option>
                                    <option value="published">Published</option>
                                </select>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-success">${isEdit ? 'Update' : 'Create'} Product</button>
                                <button type="button" class="btn" onclick="closeModal()">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        async function saveProduct(event, productId = null) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const productData = {};

            for (let [key, value] of formData.entries()) {
                productData[key] = value;
            }

            try {
                const url = productId ? `${API_BASE}/products/${productId}` : `${API_BASE}/products`;
                const method = productId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(productData)
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess(data.message);
                    closeModal();
                    loadProductsSection(); // Reload products list
                } else {
                    showError(data.message || 'Failed to save product');
                }
            } catch (error) {
                showError('Failed to save product: ' + error.message);
            }
        }

        function editProduct(productId) {
            openProductModal(productId);
        }

        function viewProduct(productId) {
            alert(`View product details for product ID: ${productId}\n\nThis would open a detailed product view with analytics, reviews, etc.`);
        }

        async function deleteProduct(productId) {
            if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/products/${productId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess(data.message);
                    loadProductsSection(); // Reload products list
                } else {
                    showError(data.message || 'Failed to delete product');
                }
            } catch (error) {
                showError('Failed to delete product: ' + error.message);
            }
        }

        function exportProducts() {
            const csvContent = "Name,SKU,Price,Stock,Category,Status\\n";

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'products_export.csv';
            a.click();
            window.URL.revokeObjectURL(url);

            showSuccess('Products exported successfully!');
        }

        function manageCategories() {
            alert('Category Management\n\nThis would open a category management interface where you can:\n- Add new categories\n- Edit existing categories\n- Set category hierarchy\n- Manage category attributes');
        }

        // Order Management Functions
        async function loadOrdersPage(page) {
            if (page < 1) return;

            try {
                const response = await fetch(`${API_BASE}/orders?page=${page}&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update table body
                    document.getElementById('ordersTableBody').innerHTML = data.data.map(order => `
                        <tr>
                            <td>
                                <div class="order-info">
                                    <div class="order-number">${order.order_number}</div>
                                    <div class="order-meta">ID: ${order.id}</div>
                                </div>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <img src="${order.customer.avatar}" alt="${order.customer.name}" class="customer-avatar">
                                    <div>
                                        <div class="customer-name">${order.customer.name}</div>
                                        <div class="customer-email">${order.customer.email}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-${order.status}">${order.status}</span>
                            </td>
                            <td>
                                <span class="payment-badge payment-${order.payment_status}">${order.payment_status}</span>
                            </td>
                            <td class="order-total">$${order.total.toFixed(2)}</td>
                            <td class="items-count">${order.items_count} items</td>
                            <td class="order-date">${new Date(order.created_at).toLocaleDateString()}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="viewOrder(${order.id})" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" onclick="editOrderStatus(${order.id})" title="Update Status">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="printInvoice(${order.id})" title="Print Invoice">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');

                    // Update pagination
                    document.querySelector('.pagination').innerHTML = `
                        <button class="btn" onclick="loadOrdersPage(${data.pagination.current_page - 1})" ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                            Previous
                        </button>
                        <span class="pagination-info">
                            Page ${data.pagination.current_page} of ${data.pagination.total_pages}
                            (${data.pagination.total} total orders)
                        </span>
                        <button class="btn" onclick="loadOrdersPage(${data.pagination.current_page + 1})" ${data.pagination.current_page >= data.pagination.total_pages ? 'disabled' : ''}>
                            Next
                        </button>
                    `;
                }
            } catch (error) {
                showError('Failed to load orders: ' + error.message);
            }
        }

        async function searchOrders(query) {
            try {
                const response = await fetch(`${API_BASE}/orders?search=${encodeURIComponent(query)}&page=1&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update table with search results
                    document.getElementById('ordersTableBody').innerHTML = data.data.map(order => `
                        <tr>
                            <td>
                                <div class="order-info">
                                    <div class="order-number">${order.order_number}</div>
                                    <div class="order-meta">ID: ${order.id}</div>
                                </div>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <img src="${order.customer.avatar}" alt="${order.customer.name}" class="customer-avatar">
                                    <div>
                                        <div class="customer-name">${order.customer.name}</div>
                                        <div class="customer-email">${order.customer.email}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-${order.status}">${order.status}</span>
                            </td>
                            <td>
                                <span class="payment-badge payment-${order.payment_status}">${order.payment_status}</span>
                            </td>
                            <td class="order-total">$${order.total.toFixed(2)}</td>
                            <td class="items-count">${order.items_count} items</td>
                            <td class="order-date">${new Date(order.created_at).toLocaleDateString()}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="viewOrder(${order.id})" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" onclick="editOrderStatus(${order.id})" title="Update Status">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="printInvoice(${order.id})" title="Print Invoice">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');
                }
            } catch (error) {
                showError('Search failed: ' + error.message);
            }
        }

        function filterOrders() {
            const status = document.getElementById('statusFilter').value;

            let url = `${API_BASE}/orders?page=1&limit=20`;
            if (status) url += `&status=${status}`;

            fetch(url, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update table with filtered results
                    document.getElementById('ordersTableBody').innerHTML = data.data.map(order => `
                        <tr>
                            <td>
                                <div class="order-info">
                                    <div class="order-number">${order.order_number}</div>
                                    <div class="order-meta">ID: ${order.id}</div>
                                </div>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <img src="${order.customer.avatar}" alt="${order.customer.name}" class="customer-avatar">
                                    <div>
                                        <div class="customer-name">${order.customer.name}</div>
                                        <div class="customer-email">${order.customer.email}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-${order.status}">${order.status}</span>
                            </td>
                            <td>
                                <span class="payment-badge payment-${order.payment_status}">${order.payment_status}</span>
                            </td>
                            <td class="order-total">$${order.total.toFixed(2)}</td>
                            <td class="items-count">${order.items_count} items</td>
                            <td class="order-date">${new Date(order.created_at).toLocaleDateString()}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="viewOrder(${order.id})" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" onclick="editOrderStatus(${order.id})" title="Update Status">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="printInvoice(${order.id})" title="Print Invoice">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');
                }
            })
            .catch(error => {
                showError('Filter failed: ' + error.message);
            });
        }

        async function viewOrder(orderId) {
            try {
                const response = await fetch(`${API_BASE}/orders/${orderId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    const order = data.data;
                    const modalHTML = `
                        <div class="modal-overlay" onclick="closeModal()">
                            <div class="modal-content order-details-modal" onclick="event.stopPropagation()">
                                <div class="modal-header">
                                    <h3>Order Details - ${order.order_number}</h3>
                                    <button class="close-btn" onclick="closeModal()">&times;</button>
                                </div>
                                <div class="order-details">
                                    <div class="order-summary">
                                        <div class="summary-row">
                                            <span>Status:</span>
                                            <span class="status-badge status-${order.status}">${order.status}</span>
                                        </div>
                                        <div class="summary-row">
                                            <span>Payment:</span>
                                            <span class="payment-badge payment-${order.payment_status}">${order.payment_status}</span>
                                        </div>
                                        <div class="summary-row">
                                            <span>Total:</span>
                                            <span class="order-total">$${order.total.toFixed(2)}</span>
                                        </div>
                                        <div class="summary-row">
                                            <span>Date:</span>
                                            <span>${new Date(order.created_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>

                                    <div class="order-items">
                                        <h4>Order Items</h4>
                                        <table class="items-table">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Price</th>
                                                    <th>Qty</th>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${order.items.map(item => `
                                                    <tr>
                                                        <td>
                                                            <div class="item-info">
                                                                <img src="${item.image}" alt="${item.name}" class="item-image">
                                                                <div>
                                                                    <div class="item-name">${item.name}</div>
                                                                    <div class="item-sku">SKU: ${item.sku}</div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>$${item.price.toFixed(2)}</td>
                                                        <td>${item.quantity}</td>
                                                        <td>$${item.total.toFixed(2)}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="order-addresses">
                                        <div class="address-section">
                                            <h4>Shipping Address</h4>
                                            <div class="address">
                                                ${order.shipping_address.name}<br>
                                                ${order.shipping_address.address_line_1}<br>
                                                ${order.shipping_address.address_line_2 ? order.shipping_address.address_line_2 + '<br>' : ''}
                                                ${order.shipping_address.city}, ${order.shipping_address.state} ${order.shipping_address.postal_code}<br>
                                                ${order.shipping_address.country}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    document.body.insertAdjacentHTML('beforeend', modalHTML);
                }
            } catch (error) {
                showError('Failed to load order details: ' + error.message);
            }
        }

        function editOrderStatus(orderId) {
            const modalHTML = `
                <div class="modal-overlay" onclick="closeModal()">
                    <div class="modal-content" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h3>Update Order Status</h3>
                            <button class="close-btn" onclick="closeModal()">&times;</button>
                        </div>
                        <form onsubmit="updateOrderStatus(event, ${orderId})">
                            <div class="form-group">
                                <label>Order Status</label>
                                <select name="status" required>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Tracking Number (optional)</label>
                                <input type="text" name="tracking_number" placeholder="Enter tracking number">
                            </div>
                            <div class="form-group">
                                <label>Notes (optional)</label>
                                <textarea name="notes" rows="3" placeholder="Add any notes about this status update"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-success">Update Status</button>
                                <button type="button" class="btn" onclick="closeModal()">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        async function updateOrderStatus(event, orderId) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const statusData = {};

            for (let [key, value] of formData.entries()) {
                statusData[key] = value;
            }

            try {
                const response = await fetch(`${API_BASE}/orders/${orderId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(statusData)
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess(data.message);
                    closeModal();
                    loadOrdersSection(); // Reload orders list
                } else {
                    showError(data.message || 'Failed to update order status');
                }
            } catch (error) {
                showError('Failed to update order status: ' + error.message);
            }
        }

        function printInvoice(orderId) {
            // In a real app, this would generate and print an invoice
            window.open(`/invoices/${orderId}`, '_blank');
            showSuccess(`Invoice for order ${orderId} opened in new tab`);
        }

        function createNewOrder() {
            alert('Create New Order\n\nThis would open an order creation interface where you can:\n- Select customer\n- Add products\n- Set shipping details\n- Process payment');
        }

        function exportOrders() {
            const csvContent = "Order Number,Customer,Status,Total,Date\\n";

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'orders_export.csv';
            a.click();
            window.URL.revokeObjectURL(url);

            showSuccess('Orders exported successfully!');
        }

        function bulkActions() {
            alert('Bulk Actions\n\nThis would allow you to:\n- Select multiple orders\n- Update status in bulk\n- Export selected orders\n- Send bulk notifications');
        }

        // Analytics Functions
        let salesChart = null;

        function initializeSalesChart(salesData) {
            const ctx = document.getElementById('salesChart');
            if (!ctx) return;

            // Destroy existing chart if it exists
            if (salesChart) {
                salesChart.destroy();
            }

            salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: salesData.map(item => new Date(item.date).toLocaleDateString()),
                    datasets: [{
                        label: 'Sales ($)',
                        data: salesData.map(item => item.sales),
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }, {
                        label: 'Orders',
                        data: salesData.map(item => item.orders),
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Sales ($)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Orders'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    }
                }
            });
        }

        async function changePeriod(period) {
            // Update active button
            document.querySelectorAll('.period-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            try {
                const response = await fetch(`${API_BASE}/analytics/sales-chart?period=${period}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success) {
                    initializeSalesChart(data.data);

                    // Update chart title
                    const chartCard = document.querySelector('.chart-card .card-header h3');
                    if (chartCard) {
                        const periodText = period === '7days' ? 'Last 7 Days' :
                                         period === '30days' ? 'Last 30 Days' : 'Last 90 Days';
                        chartCard.textContent = `Sales Trend (${periodText})`;
                    }
                }
            } catch (error) {
                showError('Failed to load chart data: ' + error.message);
            }
        }

        function exportReport() {
            // Simulate report export
            const reportData = {
                generated_at: new Date().toISOString(),
                period: '7days',
                summary: 'Analytics Report Export'
            };

            const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics_report_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            window.URL.revokeObjectURL(url);

            showSuccess('Analytics report exported successfully!');
        }

        // Additional Section Functions
        function loadTransactionsSection() {
            document.getElementById('contentArea').innerHTML = `
                <div class="section-title">Transactions</div>
                <div class="feature-card">
                    <div class="feature-header">
                        <h3><i class="fas fa-credit-card"></i> Transaction Management</h3>
                    </div>
                    <div class="feature-content">
                        <p>Comprehensive transaction management system with:</p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Payment processing history</li>
                            <li><i class="fas fa-check"></i> Refund management</li>
                            <li><i class="fas fa-check"></i> Transaction analytics</li>
                            <li><i class="fas fa-check"></i> Payment gateway integration</li>
                            <li><i class="fas fa-check"></i> Fraud detection</li>
                        </ul>
                        <div class="feature-actions">
                            <button class="btn btn-success">View All Transactions</button>
                            <button class="btn btn-info">Generate Report</button>
                            <button class="btn" onclick="testAPI('transactions')">Test API</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadInvoicesSection() {
            document.getElementById('contentArea').innerHTML = `
                <div class="section-title">Invoices</div>
                <div class="feature-card">
                    <div class="feature-header">
                        <h3><i class="fas fa-file-invoice"></i> Invoice Management</h3>
                    </div>
                    <div class="feature-content">
                        <p>Professional invoice generation and management:</p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Automated invoice generation</li>
                            <li><i class="fas fa-check"></i> Custom invoice templates</li>
                            <li><i class="fas fa-check"></i> PDF export and email delivery</li>
                            <li><i class="fas fa-check"></i> Payment tracking</li>
                            <li><i class="fas fa-check"></i> Tax calculations</li>
                        </ul>
                        <div class="feature-actions">
                            <button class="btn btn-success">Create Invoice</button>
                            <button class="btn btn-info">View All Invoices</button>
                            <button class="btn" onclick="testAPI('invoices')">Test API</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadCouponsSection() {
            document.getElementById('contentArea').innerHTML = `
                <div class="section-title">Coupons & Discounts</div>
                <div class="feature-card">
                    <div class="feature-header">
                        <h3><i class="fas fa-tags"></i> Coupon Management</h3>
                    </div>
                    <div class="feature-content">
                        <p>Flexible discount and promotion system:</p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Percentage and fixed amount discounts</li>
                            <li><i class="fas fa-check"></i> Product-specific coupons</li>
                            <li><i class="fas fa-check"></i> Usage limits and expiration dates</li>
                            <li><i class="fas fa-check"></i> Bulk discount codes</li>
                            <li><i class="fas fa-check"></i> Performance analytics</li>
                        </ul>
                        <div class="feature-actions">
                            <button class="btn btn-success">Create Coupon</button>
                            <button class="btn btn-info">View All Coupons</button>
                            <button class="btn" onclick="testAPI('coupons')">Test API</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadMediaSection() {
            document.getElementById('contentArea').innerHTML = `
                <div class="section-title">Media Library</div>
                <div class="feature-card">
                    <div class="feature-header">
                        <h3><i class="fas fa-images"></i> Media Management</h3>
                    </div>
                    <div class="feature-content">
                        <p>Centralized media asset management:</p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Image and video uploads</li>
                            <li><i class="fas fa-check"></i> Automatic image optimization</li>
                            <li><i class="fas fa-check"></i> CDN integration</li>
                            <li><i class="fas fa-check"></i> Bulk operations</li>
                            <li><i class="fas fa-check"></i> Storage analytics</li>
                        </ul>
                        <div class="feature-actions">
                            <button class="btn btn-success">Upload Media</button>
                            <button class="btn btn-info">Browse Library</button>
                            <button class="btn" onclick="testAPI('media')">Test API</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // API testing function
        async function testAPI(section) {
            const resultDiv = document.getElementById(`apiResult-${section}`);
            if (resultDiv) {
                resultDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Testing API...</div>';

                try {
                    const response = await fetch(`${API_BASE}/${section}/statistics`, {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });

                    const data = await response.json();

                    resultDiv.innerHTML = `
                        <div class="success-message" style="display: block;">
                            API Test Successful! Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="error-message" style="display: block;">
                            API Test Failed: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // Utility functions
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.display = 'block';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

            const contentArea = document.getElementById('contentArea');
            contentArea.insertBefore(errorDiv, contentArea.firstChild);

            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.style.display = 'block';
            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;

            const contentArea = document.getElementById('contentArea');
            contentArea.insertBefore(successDiv, contentArea.firstChild);

            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

    </script>
</body>
</html>
