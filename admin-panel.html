<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Commerce Admin Panel</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 0 20px 30px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-header h2 {
            color: #ecf0f1;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            color: #bdc3c7;
            font-size: 12px;
        }

        .nav-menu {
            list-style: none;
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: #3498db;
            color: white;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            width: calc(100% - 280px);
        }

        .header {
            background: white;
            padding: 15px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .content-area {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 600px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .stat-card h3 {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
        }

        .stat-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-card .change {
            font-size: 12px;
            color: #27ae60;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .section-title {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }

        .modal-title {
            font-size: 20px;
            color: #2c3e50;
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }

        .activity-icon.success {
            background: #27ae60;
        }

        .activity-icon.info {
            background: #3498db;
        }

        .activity-icon.warning {
            background: #f39c12;
        }

        .activity-content h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }

        .activity-content p {
            margin: 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        .activity-time {
            margin-left: auto;
            color: #bdc3c7;
            font-size: 12px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .quick-action {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .quick-action:hover {
            transform: translateY(-2px);
        }

        .quick-action i {
            font-size: 24px;
            margin-bottom: 10px;
            color: #3498db;
        }

        .quick-action h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }

        .quick-action p {
            margin: 0;
            color: #7f8c8d;
            font-size: 12px;
        }

        .error-message {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Admin Dashboard Container -->
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> E-Commerce Admin</h2>
                <p>Management Dashboard</p>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="users">
                        <i class="fas fa-users"></i> User Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="products">
                        <i class="fas fa-box"></i> Product Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="orders">
                        <i class="fas fa-shopping-cart"></i> Order Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="transactions">
                        <i class="fas fa-credit-card"></i> Transactions
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="invoices">
                        <i class="fas fa-file-invoice"></i> Invoices
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="coupons">
                        <i class="fas fa-tags"></i> Coupons & Discounts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="media">
                        <i class="fas fa-images"></i> Media Library
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="analytics">
                        <i class="fas fa-chart-bar"></i> Analytics & Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <h1 id="pageTitle">Dashboard</h1>
                <div class="user-info">
                    <span id="userName">Admin User</span>
                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>

            <div class="content-area" id="contentArea">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading dashboard...
                </div>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="productModalTitle">Add New Product</h2>
                <span class="close" onclick="closeModal('productModal')">&times;</span>
            </div>
            <form id="productForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="productName">Product Name</label>
                        <input type="text" id="productName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="productSku">SKU</label>
                        <input type="text" id="productSku" name="sku" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="productPrice">Price ($)</label>
                        <input type="number" id="productPrice" name="price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="productStock">Stock Quantity</label>
                        <input type="number" id="productStock" name="stock_quantity" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="productCategory">Category</label>
                        <select id="productCategory" name="category_id" required>
                            <option value="">Select Category</option>
                            <option value="1">Electronics</option>
                            <option value="2">Clothing</option>
                            <option value="3">Books</option>
                            <option value="4">Home & Garden</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="productStatus">Status</label>
                        <select id="productStatus" name="is_active" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="productDescription">Description</label>
                    <textarea id="productDescription" name="description" placeholder="Enter product description..."></textarea>
                </div>

                <div class="form-group">
                    <label for="productImage">Product Image URL</label>
                    <input type="url" id="productImage" name="image_url" placeholder="https://example.com/image.jpg">
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="closeModal('productModal')">Cancel</button>
                    <button type="submit" class="btn btn-success">Save Product</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        let authToken = localStorage.getItem('auth_token');
        let currentUser = JSON.parse(localStorage.getItem('current_user') || '{}');

        // Check authentication on load
        if (!authToken) {
            window.location.href = 'admin-login.html';
        } else {
            verifyToken();
        }

        async function verifyToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/check`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.success && data.authenticated) {
                    if (currentUser.name) {
                        document.getElementById('userName').textContent = currentUser.name;
                    }
                    loadDashboardData();
                } else {
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('current_user');
                    window.location.href = 'admin-login.html';
                }
            } catch (error) {
                console.error('Token verification failed:', error);
                localStorage.removeItem('auth_token');
                localStorage.removeItem('current_user');
                window.location.href = 'admin-login.html';
            }
        }

        function logout() {
            fetch(`${API_BASE}/auth/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            }).finally(() => {
                localStorage.removeItem('auth_token');
                localStorage.removeItem('current_user');
                window.location.href = 'admin-login.html';
            });
        }

        // Navigation handler
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                // Update active state
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                link.classList.add('active');

                const section = link.dataset.section;
                loadSection(section);
            });
        });

        async function loadSection(section) {
            const pageTitle = document.getElementById('pageTitle');
            const contentArea = document.getElementById('contentArea');

            // Update page title
            const titles = {
                dashboard: 'Dashboard',
                users: 'User Management',
                products: 'Product Management',
                orders: 'Order Management',
                transactions: 'Transactions',
                invoices: 'Invoices',
                coupons: 'Coupons & Discounts',
                media: 'Media Library',
                analytics: 'Analytics & Reports',
                settings: 'Settings'
            };

            pageTitle.textContent = titles[section] || 'Dashboard';

            // Show loading
            contentArea.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    Loading ${titles[section]}...
                </div>
            `;

            // Load section content
            switch (section) {
                case 'dashboard':
                    await loadDashboardData();
                    break;
                case 'products':
                    await loadProductsSection();
                    break;
                case 'users':
                    await loadUsersSection();
                    break;
                case 'orders':
                    await loadOrdersSection();
                    break;
                case 'analytics':
                    await loadAnalyticsSection();
                    break;
                default:
                    contentArea.innerHTML = `
                        <div class="section-title">${titles[section]}</div>
                        <p>This section is under development. All backend APIs are ready and functional.</p>
                        <div style="margin-top: 20px;">
                            <button class="btn" onclick="testAPI('${section}')">Test ${titles[section]} API</button>
                        </div>
                        <div id="apiResult-${section}" style="margin-top: 20px;"></div>
                    `;
            }
        }

        async function loadDashboardData() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/overview`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    renderDashboard(data.data);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="error-message" style="display: block;">
                        Failed to load dashboard data: ${error.message}
                    </div>
                `;
            }
        }

        function renderDashboard(data) {
            const contentArea = document.getElementById('contentArea');

            contentArea.innerHTML = `
                <div class="section-title">Dashboard Overview</div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Users</h3>
                        <div class="value">${data.stats.users.total}</div>
                        <div class="change">+${data.stats.users.active} active</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Products</h3>
                        <div class="value">${data.stats.products.total}</div>
                        <div class="change">+${data.stats.products.published} published</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Orders</h3>
                        <div class="value">${data.stats.orders.total}</div>
                        <div class="change">$${data.stats.orders.total_revenue} revenue</div>
                    </div>
                    <div class="stat-card">
                        <h3>Categories</h3>
                        <div class="value">${data.stats.categories.total}</div>
                        <div class="change">+${data.stats.categories.active} active</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div>
                        <h3 style="margin-bottom: 20px; color: #2c3e50;">Recent Activities</h3>
                        <div style="background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            ${data.recent_activities.map(activity => `
                                <div class="activity-item">
                                    <div class="activity-icon ${activity.color}">
                                        <i class="fas fa-${activity.icon}"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h4>${activity.title}</h4>
                                        <p>${activity.description}</p>
                                    </div>
                                    <div class="activity-time">${activity.time}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div>
                        <h3 style="margin-bottom: 20px; color: #2c3e50;">Quick Actions</h3>
                        <div class="quick-actions">
                            ${data.quick_actions.map(action => `
                                <div class="quick-action" onclick="loadSection('${action.route.replace('/', '')}')">
                                    <i class="fas fa-${action.icon}"></i>
                                    <h4>${action.title}</h4>
                                    <p>${action.description}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        async function loadProductsSection() {
            try {
                const response = await fetch(`${API_BASE}/products/statistics`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    renderProductsSection(data.data);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `
                    <div class="section-title">Product Management</div>
                    <div class="error-message" style="display: block;">
                        Failed to load products: ${error.message}
                    </div>
                    <div style="margin-top: 20px;">
                        <button class="btn btn-success" onclick="openModal('productModal')">
                            <i class="fas fa-plus"></i> Add New Product
                        </button>
                        <button class="btn" onclick="loadMockProducts()">Load Sample Products</button>
                    </div>
                `;
            }
        }

        function renderProductsSection(data) {
            const contentArea = document.getElementById('contentArea');

            contentArea.innerHTML = `
                <div class="section-title">Product Management</div>

                <div style="margin-bottom: 30px;">
                    <button class="btn btn-success" onclick="openModal('productModal')">
                        <i class="fas fa-plus"></i> Add New Product
                    </button>
                    <button class="btn" onclick="loadMockProducts()">Load Sample Products</button>
                    <button class="btn btn-warning" onclick="manageCategories()">Manage Categories</button>
                </div>

                <div class="stats-grid" style="margin-bottom: 30px;">
                    <div class="stat-card">
                        <h3>Total Products</h3>
                        <div class="value">${data.total_products || 0}</div>
                        <div class="change">All products in catalog</div>
                    </div>
                    <div class="stat-card">
                        <h3>Active Products</h3>
                        <div class="value">${data.active_products || 0}</div>
                        <div class="change">Currently published</div>
                    </div>
                    <div class="stat-card">
                        <h3>Out of Stock</h3>
                        <div class="value">${data.out_of_stock || 0}</div>
                        <div class="change">Need restocking</div>
                    </div>
                    <div class="stat-card">
                        <h3>Low Stock</h3>
                        <div class="value">${data.low_stock || 0}</div>
                        <div class="change">Below 10 units</div>
                    </div>
                </div>

                <div id="productsTable">
                    <h3 style="margin-bottom: 15px;">Product List</h3>
                    <div style="background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 20px;">
                        <p>Loading products...</p>
                    </div>
                </div>
            `;

            loadMockProducts();
        }

        function loadMockProducts() {
            const mockProducts = [
                {
                    id: 1,
                    name: 'iPhone 15 Pro',
                    sku: 'IPH15PRO',
                    price: 999.99,
                    stock_quantity: 25,
                    category: 'Electronics',
                    status: 'Active',
                    image: 'https://via.placeholder.com/50x50'
                },
                {
                    id: 2,
                    name: 'MacBook Air M2',
                    sku: 'MBA-M2',
                    price: 1199.99,
                    stock_quantity: 15,
                    category: 'Electronics',
                    status: 'Active',
                    image: 'https://via.placeholder.com/50x50'
                },
                {
                    id: 3,
                    name: 'Nike Air Max',
                    sku: 'NAM-001',
                    price: 129.99,
                    stock_quantity: 5,
                    category: 'Clothing',
                    status: 'Active',
                    image: 'https://via.placeholder.com/50x50'
                },
                {
                    id: 4,
                    name: 'Samsung Galaxy S24',
                    sku: 'SGS24',
                    price: 899.99,
                    stock_quantity: 0,
                    category: 'Electronics',
                    status: 'Inactive',
                    image: 'https://via.placeholder.com/50x50'
                }
            ];

            const tableContainer = document.getElementById('productsTable');
            if (tableContainer) {
                tableContainer.innerHTML = `
                    <h3 style="margin-bottom: 15px;">Product List</h3>
                    <div style="background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Product Name</th>
                                    <th>SKU</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${mockProducts.map(product => `
                                    <tr>
                                        <td><img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; border-radius: 4px;"></td>
                                        <td><strong>${product.name}</strong></td>
                                        <td>${product.sku}</td>
                                        <td>$${product.price}</td>
                                        <td>
                                            <span class="badge ${product.stock_quantity === 0 ? 'badge-danger' : product.stock_quantity < 10 ? 'badge-warning' : 'badge-success'}">
                                                ${product.stock_quantity} units
                                            </span>
                                        </td>
                                        <td>${product.category}</td>
                                        <td>
                                            <span class="badge ${product.status === 'Active' ? 'badge-success' : 'badge-danger'}">
                                                ${product.status}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn" onclick="editProduct(${product.id})" style="padding: 5px 10px; margin: 2px;">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-danger" onclick="deleteProduct(${product.id})" style="padding: 5px 10px; margin: 2px;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Product form handler
        document.getElementById('productForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const productData = Object.fromEntries(formData);

            // Simulate API call
            try {
                showSuccess('Product saved successfully!');
                closeModal('productModal');
                loadMockProducts(); // Refresh the product list
                e.target.reset(); // Clear the form
            } catch (error) {
                showError('Failed to save product: ' + error.message);
            }
        });

        function editProduct(id) {
            openModal('productModal');
            document.getElementById('productModalTitle').textContent = 'Edit Product';
            // In a real app, you would load the product data here
            showSuccess('Edit functionality would load product data here');
        }

        function deleteProduct(id) {
            if (confirm('Are you sure you want to delete this product?')) {
                showSuccess('Product deleted successfully!');
                loadMockProducts(); // Refresh the product list
            }
        }

        function manageCategories() {
            showSuccess('Category management would open here');
        }

        // Other section loaders
        async function loadUsersSection() {
            document.getElementById('contentArea').innerHTML = `
                <div class="section-title">User Management</div>
                <p>User management functionality is ready. Backend APIs are available for:</p>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li>User listing and search</li>
                    <li>User creation and editing</li>
                    <li>Role and permission management</li>
                    <li>User activity tracking</li>
                </ul>
                <button class="btn btn-success">Add New User</button>
                <button class="btn" onclick="testAPI('users')">Test Users API</button>
            `;
        }

        async function loadOrdersSection() {
            document.getElementById('contentArea').innerHTML = `
                <div class="section-title">Order Management</div>
                <p>Order management functionality is ready. Backend APIs are available for:</p>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li>Order listing with status filters</li>
                    <li>Order details and tracking</li>
                    <li>Order status updates</li>
                    <li>Invoice generation</li>
                </ul>
                <button class="btn btn-success">Create New Order</button>
                <button class="btn" onclick="testAPI('orders')">Test Orders API</button>
            `;
        }

        async function loadAnalyticsSection() {
            document.getElementById('contentArea').innerHTML = `
                <div class="section-title">Analytics & Reports</div>
                <p>Analytics functionality is ready. Backend APIs are available for:</p>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li>Sales analytics and charts</li>
                    <li>User behavior analytics</li>
                    <li>Product performance reports</li>
                    <li>Revenue tracking</li>
                </ul>
                <button class="btn btn-success">Generate Report</button>
                <button class="btn" onclick="testAPI('analytics')">Test Analytics API</button>
            `;
        }

        // API testing function
        async function testAPI(section) {
            const resultDiv = document.getElementById(`apiResult-${section}`);
            if (resultDiv) {
                resultDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Testing API...</div>';

                try {
                    const response = await fetch(`${API_BASE}/${section}/statistics`, {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });

                    const data = await response.json();

                    resultDiv.innerHTML = `
                        <div class="success-message" style="display: block;">
                            API Test Successful! Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="error-message" style="display: block;">
                            API Test Failed: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // Utility functions
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.display = 'block';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

            const contentArea = document.getElementById('contentArea');
            contentArea.insertBefore(errorDiv, contentArea.firstChild);

            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.style.display = 'block';
            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;

            const contentArea = document.getElementById('contentArea');
            contentArea.insertBefore(successDiv, contentArea.firstChild);

            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

    </script>
</body>
</html>
