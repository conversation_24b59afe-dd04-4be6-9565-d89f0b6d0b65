import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Order {
  id: number;
  order_number: string;
  user: {
    id: number;
    name: string;
    email: string;
    phone?: string;
  };
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  status_color: string;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded';
  payment_status_color: string;
  payment_method: 'cod' | 'razorpay' | 'gpay' | 'bank_transfer';
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  total_items: number;
  items_count: number;
  coupon_code?: string;
  coupon_discount?: number;
  formatted_shipping_address: string;
  formatted_billing_address: string;
  shipping_address: Address;
  billing_address: Address;
  notes?: string;
  can_be_cancelled: boolean;
  can_be_refunded: boolean;
  items?: OrderItem[];
  transactions?: Transaction[];
  invoice?: Invoice;
  processed_at?: string;
  shipped_at?: string;
  delivered_at?: string;
  cancelled_at?: string;
  refunded_at?: string;
  creator?: { id: number; name: string };
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: number;
  product: {
    id: number;
    name: string;
    slug: string;
  };
  product_name: string;
  product_sku: string;
  product_image_url?: string;
  price: number;
  quantity: number;
  total: number;
  product_attributes?: any;
  formatted_attributes?: string;
}

export interface Address {
  name: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
}

export interface Transaction {
  id: number;
  transaction_id: string;
  type: string;
  amount: number;
  status: string;
  reference?: string;
  created_at: string;
}

export interface Invoice {
  id: number;
  invoice_number: string;
  status: string;
  created_at: string;
}

export interface OrderFilters {
  search?: string;
  status?: string;
  payment_status?: string;
  payment_method?: string;
  user_id?: number;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  per_page?: number;
  page?: number;
}

export interface CreateOrderRequest {
  user_id: number;
  items: {
    product_id: number;
    product_variation_id?: number;
    quantity: number;
    price?: number;
  }[];
  shipping_address: Address;
  billing_address?: Address;
  payment_method: string;
  shipping_amount?: number;
  coupon_code?: string;
  notes?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  links: {
    first: string;
    last: string;
    prev?: string;
    next?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private readonly API_URL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get paginated list of orders
   */
  getOrders(filters: OrderFilters = {}): Observable<PaginatedResponse<Order>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof OrderFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<PaginatedResponse<Order>>(`${this.API_URL}/orders`, { params });
  }

  /**
   * Get single order by ID
   */
  getOrder(id: number): Observable<ApiResponse<Order>> {
    return this.http.get<ApiResponse<Order>>(`${this.API_URL}/orders/${id}`);
  }

  /**
   * Create new order
   */
  createOrder(orderData: CreateOrderRequest): Observable<ApiResponse<Order>> {
    return this.http.post<ApiResponse<Order>>(`${this.API_URL}/orders`, orderData);
  }

  /**
   * Update existing order
   */
  updateOrder(id: number, orderData: Partial<Order>): Observable<ApiResponse<Order>> {
    return this.http.put<ApiResponse<Order>>(`${this.API_URL}/orders/${id}`, orderData);
  }

  /**
   * Update order status
   */
  updateOrderStatus(id: number, status: string, note?: string): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/orders/${id}/status`, {
      status,
      note
    });
  }

  /**
   * Cancel order
   */
  cancelOrder(id: number, reason?: string): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/orders/${id}/cancel`, {
      reason
    });
  }

  /**
   * Get order statistics
   */
  getStatistics(): Observable<ApiResponse<any>> {
    return this.http.get<ApiResponse<any>>(`${this.API_URL}/orders/statistics`);
  }

  /**
   * Get orders by user
   */
  getUserOrders(userId: number, filters: OrderFilters = {}): Observable<PaginatedResponse<Order>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof OrderFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<PaginatedResponse<Order>>(`${this.API_URL}/orders/user/${userId}`, { params });
  }

  /**
   * Search orders
   */
  searchOrders(query: string, limit: number = 10): Observable<ApiResponse<Order[]>> {
    const params = new HttpParams()
      .set('search', query)
      .set('per_page', limit.toString());

    return this.http.get<ApiResponse<Order[]>>(`${this.API_URL}/orders/search`, { params });
  }

  /**
   * Export orders
   */
  exportOrders(format: 'csv' | 'excel' = 'csv', filters: OrderFilters = {}): Observable<Blob> {
    let params = new HttpParams().set('format', format);
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof OrderFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get(`${this.API_URL}/orders/export`, {
      params,
      responseType: 'blob'
    });
  }

  /**
   * Generate invoice for order
   */
  generateInvoice(orderId: number): Observable<ApiResponse<Invoice>> {
    return this.http.post<ApiResponse<Invoice>>(`${this.API_URL}/orders/${orderId}/invoice`, {});
  }

  /**
   * Download invoice PDF
   */
  downloadInvoice(orderId: number): Observable<Blob> {
    return this.http.get(`${this.API_URL}/orders/${orderId}/invoice/download`, {
      responseType: 'blob'
    });
  }

  /**
   * Send order confirmation email
   */
  sendConfirmationEmail(orderId: number): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/orders/${orderId}/send-confirmation`, {});
  }

  /**
   * Add tracking information
   */
  addTracking(orderId: number, trackingData: { tracking_number: string; carrier: string; tracking_url?: string }): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/orders/${orderId}/tracking`, trackingData);
  }

  /**
   * Get order timeline/activity
   */
  getOrderTimeline(orderId: number): Observable<ApiResponse<any[]>> {
    return this.http.get<ApiResponse<any[]>>(`${this.API_URL}/orders/${orderId}/timeline`);
  }

  /**
   * Process refund
   */
  processRefund(orderId: number, refundData: { amount: number; reason?: string; items?: number[] }): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/orders/${orderId}/refund`, refundData);
  }

  /**
   * Get recent orders
   */
  getRecentOrders(limit: number = 10): Observable<ApiResponse<Order[]>> {
    const params = new HttpParams()
      .set('per_page', limit.toString())
      .set('sort_by', 'created_at')
      .set('sort_order', 'desc');

    return this.http.get<ApiResponse<Order[]>>(`${this.API_URL}/orders/recent`, { params });
  }

  /**
   * Get pending orders
   */
  getPendingOrders(): Observable<ApiResponse<Order[]>> {
    const params = new HttpParams()
      .set('status', 'pending')
      .set('sort_by', 'created_at')
      .set('sort_order', 'asc');

    return this.http.get<ApiResponse<Order[]>>(`${this.API_URL}/orders`, { params });
  }

  /**
   * Bulk update order status
   */
  bulkUpdateStatus(orderIds: number[], status: string, note?: string): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/orders/bulk-update-status`, {
      order_ids: orderIds,
      status,
      note
    });
  }

  /**
   * Get order status options
   */
  getStatusOptions(): { value: string; label: string; color: string }[] {
    return [
      { value: 'pending', label: 'Pending', color: 'warn' },
      { value: 'processing', label: 'Processing', color: 'accent' },
      { value: 'shipped', label: 'Shipped', color: 'primary' },
      { value: 'delivered', label: 'Delivered', color: 'primary' },
      { value: 'cancelled', label: 'Cancelled', color: 'warn' },
      { value: 'refunded', label: 'Refunded', color: 'warn' }
    ];
  }

  /**
   * Get payment status options
   */
  getPaymentStatusOptions(): { value: string; label: string; color: string }[] {
    return [
      { value: 'pending', label: 'Pending', color: 'warn' },
      { value: 'paid', label: 'Paid', color: 'primary' },
      { value: 'failed', label: 'Failed', color: 'warn' },
      { value: 'refunded', label: 'Refunded', color: 'warn' },
      { value: 'partially_refunded', label: 'Partially Refunded', color: 'accent' }
    ];
  }

  /**
   * Get payment method options
   */
  getPaymentMethodOptions(): { value: string; label: string }[] {
    return [
      { value: 'cod', label: 'Cash on Delivery' },
      { value: 'razorpay', label: 'Razorpay' },
      { value: 'gpay', label: 'Google Pay' },
      { value: 'bank_transfer', label: 'Bank Transfer' }
    ];
  }
}
