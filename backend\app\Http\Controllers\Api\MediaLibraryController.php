<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MediaLibrary;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class MediaLibraryController extends Controller
{
    /**
     * Display a listing of media files.
     */
    public function index(Request $request): JsonResponse
    {
        $query = MediaLibrary::query();

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by collection
        if ($request->has('collection')) {
            $query->where('collection_name', $request->collection);
        }

        // Filter by folder
        if ($request->has('folder')) {
            $query->where('folder_path', $request->folder);
        }

        // Search by name
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('alt_text', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $media = $query->with('uploader:id,name')->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'message' => 'Media files retrieved successfully',
            'data' => $media->items(),
            'meta' => [
                'current_page' => $media->currentPage(),
                'last_page' => $media->lastPage(),
                'per_page' => $media->perPage(),
                'total' => $media->total(),
            ],
            'statistics' => $this->getMediaStatistics()
        ]);
    }

    /**
     * Upload media files.
     */
    public function upload(Request $request): JsonResponse
    {
        $request->validate([
            'files' => 'required|array|max:10',
            'files.*' => 'file|max:10240', // 10MB max
            'collection' => 'nullable|string|max:100',
            'folder_path' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);

        $uploadedFiles = [];
        $collection = $request->get('collection', 'default');
        $folderPath = $request->get('folder_path', '');

        foreach ($request->file('files') as $file) {
            try {
                $uploadedFile = $this->processFileUpload($file, $collection, $folderPath, $request);
                $uploadedFiles[] = $uploadedFile;
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Upload failed: ' . $e->getMessage()
                ], 422);
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($uploadedFiles) . ' file(s) uploaded successfully',
            'data' => $uploadedFiles
        ], 201);
    }

    /**
     * Display the specified media file.
     */
    public function show(MediaLibrary $media): JsonResponse
    {
        $media->load('uploader:id,name');
        $media->increment('download_count');
        $media->update(['last_accessed_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'Media file retrieved successfully',
            'data' => [
                'media' => $media,
                'url' => $media->getUrl(),
                'download_url' => route('media.download', $media->id),
            ]
        ]);
    }

    /**
     * Update the specified media file.
     */
    public function update(Request $request, MediaLibrary $media): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'title' => 'nullable|string|max:255',
            'caption' => 'nullable|string|max:500',
            'folder_path' => 'nullable|string|max:255',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'is_public' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $media->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Media file updated successfully',
            'data' => $media->fresh()
        ]);
    }

    /**
     * Remove the specified media file.
     */
    public function destroy(MediaLibrary $media): JsonResponse
    {
        // Delete file from storage
        Storage::disk($media->disk)->delete($media->file_name);
        
        // Delete responsive images if they exist
        if ($media->responsive_images) {
            foreach ($media->responsive_images as $responsiveImage) {
                Storage::disk($media->disk)->delete($responsiveImage);
            }
        }

        $media->delete();

        return response()->json([
            'success' => true,
            'message' => 'Media file deleted successfully'
        ]);
    }

    /**
     * Bulk delete media files.
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        $request->validate([
            'media_ids' => 'required|array',
            'media_ids.*' => 'integer|exists:media_library,id'
        ]);

        $mediaFiles = MediaLibrary::whereIn('id', $request->media_ids)->get();
        $deletedCount = 0;

        foreach ($mediaFiles as $media) {
            try {
                Storage::disk($media->disk)->delete($media->file_name);
                if ($media->responsive_images) {
                    foreach ($media->responsive_images as $responsiveImage) {
                        Storage::disk($media->disk)->delete($responsiveImage);
                    }
                }
                $media->delete();
                $deletedCount++;
            } catch (\Exception $e) {
                // Continue with other files if one fails
                continue;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$deletedCount} media file(s) deleted successfully"
        ]);
    }

    /**
     * Get media folders.
     */
    public function getFolders(): JsonResponse
    {
        $folders = MediaLibrary::whereNotNull('folder_path')
            ->where('folder_path', '!=', '')
            ->distinct()
            ->pluck('folder_path')
            ->sort()
            ->values();

        return response()->json([
            'success' => true,
            'message' => 'Folders retrieved successfully',
            'data' => $folders
        ]);
    }

    /**
     * Get media collections.
     */
    public function getCollections(): JsonResponse
    {
        $collections = MediaLibrary::distinct()
            ->pluck('collection_name')
            ->sort()
            ->values();

        return response()->json([
            'success' => true,
            'message' => 'Collections retrieved successfully',
            'data' => $collections
        ]);
    }

    /**
     * Process file upload.
     */
    private function processFileUpload($file, string $collection, string $folderPath, Request $request): MediaLibrary
    {
        $originalName = $file->getClientOriginalName();
        $mimeType = $file->getMimeType();
        $size = $file->getSize();
        
        // Generate unique filename
        $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
        $storagePath = $collection . '/' . $folderPath . '/' . $fileName;
        
        // Store file
        $path = $file->storeAs($collection . '/' . $folderPath, $fileName, 'public');
        
        // Determine file type
        $type = $this->determineFileType($mimeType);
        
        // Get image dimensions if it's an image
        $width = null;
        $height = null;
        if ($type === 'image') {
            try {
                $image = Image::make($file);
                $width = $image->width();
                $height = $image->height();
            } catch (\Exception $e) {
                // Continue without dimensions if image processing fails
            }
        }

        // Create media record
        return MediaLibrary::create([
            'name' => $originalName,
            'file_name' => $path,
            'mime_type' => $mimeType,
            'disk' => 'public',
            'collection_name' => $collection,
            'size' => $size,
            'type' => $type,
            'width' => $width,
            'height' => $height,
            'folder_path' => $folderPath,
            'alt_text' => $request->get('alt_text'),
            'description' => $request->get('description'),
            'uploaded_by' => Auth::id(),
        ]);
    }

    /**
     * Determine file type from MIME type.
     */
    private function determineFileType(string $mimeType): string
    {
        if (str_starts_with($mimeType, 'image/')) {
            return 'image';
        } elseif (str_starts_with($mimeType, 'video/')) {
            return 'video';
        } elseif (str_starts_with($mimeType, 'audio/')) {
            return 'audio';
        } elseif (in_array($mimeType, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'text/csv'
        ])) {
            return 'document';
        }
        
        return 'other';
    }

    /**
     * Get media statistics.
     */
    private function getMediaStatistics(): array
    {
        return [
            'total_files' => MediaLibrary::count(),
            'total_size' => MediaLibrary::sum('size'),
            'by_type' => MediaLibrary::selectRaw('type, COUNT(*) as count, SUM(size) as total_size')
                ->groupBy('type')
                ->get()
                ->keyBy('type'),
            'recent_uploads' => MediaLibrary::where('created_at', '>=', now()->subDays(7))->count(),
        ];
    }
}
