<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_group_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('user_group_id')->constrained('user_groups')->onDelete('cascade');
            $table->timestamp('added_at')->default(now());
            $table->foreignId('added_by')->nullable()->constrained('users');
            $table->timestamps();
            
            // Unique constraint to prevent duplicate memberships
            $table->unique(['user_id', 'user_group_id']);
            
            // Indexes
            $table->index('user_id');
            $table->index('user_group_id');
            $table->index('added_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_group_members');
    }
};
