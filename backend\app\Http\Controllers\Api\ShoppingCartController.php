<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ShoppingCart;
use App\Models\ShoppingCartItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ShoppingCartController extends Controller
{
    /**
     * Display the user's shopping cart.
     */
    public function show(): JsonResponse
    {
        $cart = $this->getUserCart();
        
        $cart->load(['items.product.images', 'items.variation', 'coupon']);

        return response()->json([
            'success' => true,
            'message' => 'Cart retrieved successfully',
            'data' => [
                'cart' => $cart,
                'items' => $cart->items,
                'summary' => [
                    'total_items' => $cart->total_items,
                    'total_unique_items' => $cart->total_unique_items,
                    'subtotal' => $cart->subtotal,
                    'tax_amount' => $cart->tax_amount,
                    'shipping_amount' => $cart->shipping_amount,
                    'discount_amount' => $cart->discount_amount,
                    'total_amount' => $cart->total_amount,
                ]
            ]
        ]);
    }

    /**
     * Add item to cart.
     */
    public function addItem(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1|max:100',
            'product_variation_id' => 'nullable|exists:product_variations,id',
            'product_options' => 'nullable|array'
        ]);

        $cart = $this->getUserCart();
        
        try {
            $item = $cart->addItem(
                $validated['product_id'],
                $validated['quantity'],
                $validated['product_variation_id'] ?? null,
                $validated['product_options'] ?? []
            );

            return response()->json([
                'success' => true,
                'message' => 'Item added to cart successfully',
                'data' => [
                    'item' => $item->load(['product', 'variation']),
                    'cart_summary' => [
                        'total_items' => $cart->total_items,
                        'total_amount' => $cart->total_amount,
                    ]
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add item to cart: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Update cart item.
     */
    public function updateItem(Request $request, ShoppingCartItem $item): JsonResponse
    {
        $this->authorize('update', $item->cart);

        $validated = $request->validate([
            'quantity' => 'required|integer|min:1|max:100'
        ]);

        try {
            $item->cart->updateItemQuantity($item->id, $validated['quantity']);

            return response()->json([
                'success' => true,
                'message' => 'Cart item updated successfully',
                'data' => [
                    'item' => $item->fresh()->load(['product', 'variation']),
                    'cart_summary' => [
                        'total_items' => $item->cart->total_items,
                        'total_amount' => $item->cart->total_amount,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update cart item: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(ShoppingCartItem $item): JsonResponse
    {
        $this->authorize('update', $item->cart);

        try {
            $cart = $item->cart;
            $cart->removeItem($item->id);

            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart successfully',
                'data' => [
                    'cart_summary' => [
                        'total_items' => $cart->total_items,
                        'total_amount' => $cart->total_amount,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove cart item: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Clear all items from cart.
     */
    public function clear(): JsonResponse
    {
        $cart = $this->getUserCart();
        
        $itemCount = $cart->items()->count();
        $cart->clearItems();

        return response()->json([
            'success' => true,
            'message' => "{$itemCount} items removed from cart successfully"
        ]);
    }

    /**
     * Apply coupon to cart.
     */
    public function applyCoupon(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'coupon_code' => 'required|string'
        ]);

        $cart = $this->getUserCart();
        
        $result = $cart->applyCoupon($validated['coupon_code']);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['success'] ? [
                'coupon' => $cart->fresh()->coupon,
                'cart_summary' => [
                    'subtotal' => $cart->subtotal,
                    'discount_amount' => $cart->discount_amount,
                    'total_amount' => $cart->total_amount,
                ]
            ] : null
        ], $result['success'] ? 200 : 422);
    }

    /**
     * Remove coupon from cart.
     */
    public function removeCoupon(): JsonResponse
    {
        $cart = $this->getUserCart();
        
        $cart->removeCoupon();

        return response()->json([
            'success' => true,
            'message' => 'Coupon removed successfully',
            'data' => [
                'cart_summary' => [
                    'subtotal' => $cart->subtotal,
                    'discount_amount' => $cart->discount_amount,
                    'total_amount' => $cart->total_amount,
                ]
            ]
        ]);
    }

    /**
     * Move item to wishlist.
     */
    public function moveToWishlist(Request $request, ShoppingCartItem $item): JsonResponse
    {
        $this->authorize('update', $item->cart);

        $validated = $request->validate([
            'wishlist_id' => 'nullable|exists:wishlists,id'
        ]);

        $result = $item->moveToWishlist($validated['wishlist_id'] ?? null);

        return response()->json($result, $result['success'] ? 200 : 422);
    }

    /**
     * Validate cart for checkout.
     */
    public function validateForCheckout(): JsonResponse
    {
        $cart = $this->getUserCart();
        
        if ($cart->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Cart is empty'
            ], 422);
        }

        $issues = [];
        
        foreach ($cart->items as $item) {
            if (!$item->isAvailable()) {
                $issues[] = [
                    'item_id' => $item->id,
                    'product_name' => $item->display_name,
                    'issue' => $item->availability_status
                ];
            }
            
            if ($item->has_price_changed) {
                $issues[] = [
                    'item_id' => $item->id,
                    'product_name' => $item->display_name,
                    'issue' => 'Price has changed',
                    'old_price' => $item->unit_price,
                    'new_price' => $item->current_price
                ];
            }
        }

        return response()->json([
            'success' => empty($issues),
            'message' => empty($issues) ? 'Cart is valid for checkout' : 'Cart has issues that need to be resolved',
            'data' => [
                'issues' => $issues,
                'cart_summary' => [
                    'total_items' => $cart->total_items,
                    'total_amount' => $cart->total_amount,
                ]
            ]
        ], empty($issues) ? 200 : 422);
    }

    /**
     * Update item prices to current prices.
     */
    public function updatePrices(): JsonResponse
    {
        $cart = $this->getUserCart();
        
        $updatedCount = 0;
        
        foreach ($cart->items as $item) {
            if ($item->has_price_changed) {
                $item->updatePrice();
                $updatedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$updatedCount} item prices updated successfully",
            'data' => [
                'updated_count' => $updatedCount,
                'cart_summary' => [
                    'total_items' => $cart->total_items,
                    'total_amount' => $cart->total_amount,
                ]
            ]
        ]);
    }

    /**
     * Get cart statistics.
     */
    public function getStatistics(): JsonResponse
    {
        $cart = $this->getUserCart();
        
        $stats = [
            'total_items' => $cart->total_items,
            'total_unique_items' => $cart->total_unique_items,
            'total_weight' => $cart->items->sum('total_weight'),
            'requires_shipping' => $cart->items->where('requires_shipping', true)->count() > 0,
            'categories' => $cart->items->load('product.category')
                ->groupBy('product.category.name')
                ->map->count()
                ->toArray(),
            'price_range' => [
                'min' => $cart->items->min('unit_price'),
                'max' => $cart->items->max('unit_price'),
                'average' => $cart->items->avg('unit_price'),
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Cart statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Get or create user's cart.
     */
    private function getUserCart(): ShoppingCart
    {
        $user = Auth::user();
        
        $cart = $user->cart;
        
        if (!$cart) {
            $cart = ShoppingCart::create([
                'user_id' => $user->id,
                'expires_at' => now()->addDays(30)
            ]);
        } elseif ($cart->isExpired()) {
            $cart->extendExpiration();
        }
        
        return $cart;
    }
}
