{"expo": {"name": "E-Commerce Mobile", "slug": "ecommerce-mobile-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.rektech.ecommerce.mobile", "buildNumber": "1.0.0", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to scan barcodes and take product photos.", "NSPhotoLibraryUsageDescription": "This app accesses the photo library to select product images.", "NSLocationWhenInUseUsageDescription": "This app uses location to provide location-based services."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.rektech.ecommerce.mobile", "versionCode": 1, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-secure-store", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-barcode-scanner", {"cameraPermission": "Allow $(PRODUCT_NAME) to access camera for barcode scanning."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "sounds": ["./assets/notification-sound.wav"]}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}]], "extra": {"eas": {"projectId": "your-eas-project-id"}}, "owner": "rektech"}}