.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  
  mat-card {
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
}

.login-header-image {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  
  mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

mat-card-title {
  color: #333;
  font-weight: 600;
  margin-bottom: 4px;
}

mat-card-subtitle {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-top: 24px;
  
  .full-width {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .forgot-password-link {
      font-size: 14px;
      text-transform: none;
      padding: 0;
      min-width: auto;
    }
  }
  
  .login-button {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    
    mat-icon {
      margin-right: 8px;
    }
  }
}

.register-link {
  text-align: center;
  margin: 0;
  color: #666;
  font-size: 14px;
  
  button {
    text-transform: none;
    font-weight: 500;
    padding: 0;
    min-width: auto;
    margin-left: 4px;
  }
}

.demo-credentials {
  mat-card {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    
    mat-card-title {
      font-size: 16px;
      color: #495057;
    }
    
    .credential-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      strong {
        color: #495057;
        min-width: 80px;
      }
      
      span {
        color: #6c757d;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        background-color: #e9ecef;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    max-width: 100%;
    
    mat-card {
      padding: 20px;
    }
  }
  
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

// Material theme customizations
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }
  
  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
  
  .mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: rgba(0, 0, 0, 0.12);
    }
    
    &.mat-focused .mat-form-field-outline-thick {
      color: #667eea;
    }
  }
  
  .mat-primary {
    background-color: #667eea !important;
  }
  
  .mat-accent {
    color: #764ba2 !important;
  }
}
