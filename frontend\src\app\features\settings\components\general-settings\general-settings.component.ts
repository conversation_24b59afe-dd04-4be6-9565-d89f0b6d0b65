import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';

import { Setting, SettingsService } from '../../services/settings.service';
import { LoadingService } from '../../../../core/services/loading.service';

@Component({
  selector: 'app-general-settings',
  templateUrl: './general-settings.component.html',
  styleUrls: ['./general-settings.component.scss']
})
export class GeneralSettingsComponent implements OnInit, OnChanges {
  @Input() settings: Setting[] = [];
  @Input() saving = false;
  @Output() settingsChange = new EventEmitter<{ [key: string]: any }>();

  generalForm: FormGroup;
  timezones: { [key: string]: string } = {};
  currencies: { [key: string]: any } = {};
  countries: { [key: string]: string } = {};
  
  dateFormats = [
    { value: 'Y-m-d', label: 'YYYY-MM-DD (2024-01-15)' },
    { value: 'm/d/Y', label: 'MM/DD/YYYY (01/15/2024)' },
    { value: 'd/m/Y', label: 'DD/MM/YYYY (15/01/2024)' },
    { value: 'M j, Y', label: 'Month DD, YYYY (Jan 15, 2024)' },
    { value: 'F j, Y', label: 'Month DD, YYYY (January 15, 2024)' },
    { value: 'j F Y', label: 'DD Month YYYY (15 January 2024)' }
  ];

  timeFormats = [
    { value: 'H:i', label: '24 Hour (14:30)' },
    { value: 'g:i A', label: '12 Hour (2:30 PM)' },
    { value: 'g:i a', label: '12 Hour (2:30 pm)' }
  ];

  currencyPositions = [
    { value: 'before', label: 'Before amount ($100)' },
    { value: 'after', label: 'After amount (100$)' }
  ];

  constructor(
    private fb: FormBuilder,
    private settingsService: SettingsService,
    private loadingService: LoadingService,
    private snackBar: MatSnackBar
  ) {
    this.generalForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadOptions();
  }

  ngOnChanges(): void {
    if (this.settings.length > 0) {
      this.populateForm();
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      app_name: ['', [Validators.required, Validators.maxLength(255)]],
      app_description: ['', [Validators.maxLength(500)]],
      app_url: ['', [Validators.required, Validators.pattern(/^https?:\/\/.+/)]],
      contact_email: ['', [Validators.required, Validators.email]],
      contact_phone: ['', [Validators.pattern(/^[\+]?[1-9][\d]{0,15}$/)]],
      contact_address: [''],
      timezone: ['UTC', [Validators.required]],
      date_format: ['Y-m-d', [Validators.required]],
      time_format: ['H:i', [Validators.required]],
      currency: ['USD', [Validators.required]],
      currency_symbol: ['$', [Validators.required]],
      currency_position: ['before', [Validators.required]],
      decimal_places: [2, [Validators.required, Validators.min(0), Validators.max(4)]],
      thousand_separator: [',', [Validators.required, Validators.maxLength(1)]],
      decimal_separator: ['.', [Validators.required, Validators.maxLength(1)]]
    });
  }

  private populateForm(): void {
    const formData: { [key: string]: any } = {};
    
    this.settings.forEach(setting => {
      if (this.generalForm.get(setting.key)) {
        formData[setting.key] = setting.value;
      }
    });

    this.generalForm.patchValue(formData);
  }

  private loadOptions(): void {
    // Load timezones
    this.settingsService.getTimezones().subscribe({
      next: (response) => {
        if (response.success) {
          this.timezones = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading timezones:', error);
      }
    });

    // Load currencies
    this.settingsService.getCurrencies().subscribe({
      next: (response) => {
        if (response.success) {
          this.currencies = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading currencies:', error);
      }
    });

    // Load countries
    this.settingsService.getCountries().subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading countries:', error);
      }
    });
  }

  onSave(): void {
    if (this.generalForm.valid) {
      const formValue = this.generalForm.value;
      this.settingsChange.emit(formValue);
    } else {
      this.markFormGroupTouched();
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000,
        panelClass: ['warning-snackbar']
      });
    }
  }

  onReset(): void {
    this.populateForm();
    this.snackBar.open('Form reset to saved values', 'Close', {
      duration: 2000,
      panelClass: ['info-snackbar']
    });
  }

  onFileSelected(event: any, settingKey: string): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadFile(file, settingKey);
    }
  }

  private uploadFile(file: File, settingKey: string): void {
    this.loadingService.setLoading(true);

    this.settingsService.uploadFile(settingKey, file).subscribe({
      next: (response) => {
        if (response.success) {
          this.generalForm.patchValue({
            [settingKey]: response.data.url
          });
          this.snackBar.open('File uploaded successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error uploading file:', error);
        this.snackBar.open('Error uploading file', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.generalForm.controls).forEach(key => {
      const control = this.generalForm.get(key);
      control?.markAsTouched();
    });
  }

  // Getter methods for form controls
  get appName() { return this.generalForm.get('app_name'); }
  get appDescription() { return this.generalForm.get('app_description'); }
  get appUrl() { return this.generalForm.get('app_url'); }
  get contactEmail() { return this.generalForm.get('contact_email'); }
  get contactPhone() { return this.generalForm.get('contact_phone'); }
  get contactAddress() { return this.generalForm.get('contact_address'); }
  get timezone() { return this.generalForm.get('timezone'); }
  get dateFormat() { return this.generalForm.get('date_format'); }
  get timeFormat() { return this.generalForm.get('time_format'); }
  get currency() { return this.generalForm.get('currency'); }
  get currencySymbol() { return this.generalForm.get('currency_symbol'); }
  get currencyPosition() { return this.generalForm.get('currency_position'); }
  get decimalPlaces() { return this.generalForm.get('decimal_places'); }
  get thousandSeparator() { return this.generalForm.get('thousand_separator'); }
  get decimalSeparator() { return this.generalForm.get('decimal_separator'); }

  // Error message methods
  getAppNameErrorMessage(): string {
    if (this.appName?.hasError('required')) {
      return 'Application name is required';
    }
    if (this.appName?.hasError('maxlength')) {
      return 'Application name cannot exceed 255 characters';
    }
    return '';
  }

  getAppUrlErrorMessage(): string {
    if (this.appUrl?.hasError('required')) {
      return 'Application URL is required';
    }
    if (this.appUrl?.hasError('pattern')) {
      return 'Please enter a valid URL (http:// or https://)';
    }
    return '';
  }

  getContactEmailErrorMessage(): string {
    if (this.contactEmail?.hasError('required')) {
      return 'Contact email is required';
    }
    if (this.contactEmail?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    return '';
  }

  getContactPhoneErrorMessage(): string {
    if (this.contactPhone?.hasError('pattern')) {
      return 'Please enter a valid phone number';
    }
    return '';
  }

  get hasUnsavedChanges(): boolean {
    return this.generalForm.dirty;
  }

  get canSave(): boolean {
    return this.generalForm.valid && this.generalForm.dirty && !this.saving;
  }

  get canReset(): boolean {
    return this.generalForm.dirty && !this.saving;
  }

  getCurrencyOptions(): { value: string; label: string }[] {
    return Object.keys(this.currencies).map(code => ({
      value: code,
      label: `${code} - ${this.currencies[code].name}`
    }));
  }

  getTimezoneOptions(): { value: string; label: string }[] {
    return Object.keys(this.timezones).map(tz => ({
      value: tz,
      label: this.timezones[tz]
    }));
  }

  onCurrencyChange(): void {
    const selectedCurrency = this.currency?.value;
    if (selectedCurrency && this.currencies[selectedCurrency]) {
      this.generalForm.patchValue({
        currency_symbol: this.currencies[selectedCurrency].symbol
      });
    }
  }
}
