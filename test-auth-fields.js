// Test authentication with new fields
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAuthWithFields() {
    console.log('🔐 Testing Authentication Integration with New Fields...\n');
    
    try {
        // Test 1: Verify protected endpoints block unauthenticated access
        console.log('1. Testing unauthenticated access to new endpoints...');
        const protectedEndpoints = [
            '/api/dashboard/stats',
            '/api/sales/statistics', 
            '/api/marketing/statistics',
            '/api/inventory/statistics',
            '/api/support/statistics',
            '/api/users/detailed'
        ];
        
        for (const endpoint of protectedEndpoints) {
            const response = await fetch(`http://localhost:8001${endpoint}`);
            const data = await response.json();
            
            if (response.status === 401 && data.code === 'UNAUTHORIZED') {
                console.log(`   ✅ ${endpoint}: Properly protected`);
            } else {
                console.log(`   ❌ ${endpoint}: Not properly protected!`);
            }
        }
        
        // Test 2: Login and verify all fields are accessible
        console.log('\n2. Logging in and testing field access...');
        const loginResponse = await fetch('http://localhost:8001/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: '<EMAIL>', password: 'admin123' })
        });
        const loginData = await loginResponse.json();
        
        if (!loginData.success) {
            console.error('❌ Login failed');
            return;
        }
        
        const token = loginData.data.token;
        console.log('✅ Login successful');
        
        // Test 3: Verify all new fields are accessible with authentication
        console.log('\n3. Testing authenticated access to all new fields...');
        
        const statsResponse = await fetch('http://localhost:8001/api/dashboard/stats', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        const statsData = await statsResponse.json();
        
        if (statsData.success) {
            console.log('✅ Dashboard stats accessible');
            
            // Check for presence of new fields
            const requiredFields = {
                'users.premium': statsData.data.users.premium,
                'users.bounce_rate': statsData.data.users.bounce_rate,
                'users.countries': statsData.data.users.countries,
                'products.featured': statsData.data.products.featured,
                'products.avg_rating': statsData.data.products.avg_rating,
                'orders.conversion_rate': statsData.data.orders.conversion_rate,
                'orders.repeat_customers': statsData.data.orders.repeat_customers,
                'sales.growth_rate': statsData.data.sales.growth_rate,
                'sales.best_selling_product': statsData.data.sales.best_selling_product,
                'inventory.total_value': statsData.data.inventory.total_value,
                'inventory.suppliers': statsData.data.inventory.suppliers,
                'marketing.email_subscribers': statsData.data.marketing.email_subscribers,
                'marketing.roas': statsData.data.marketing.roas,
                'support.satisfaction_rating': statsData.data.support.satisfaction_rating
            };
            
            console.log('\n   📊 Verifying new fields are present:');
            let allFieldsPresent = true;
            
            for (const [fieldPath, value] of Object.entries(requiredFields)) {
                if (value !== undefined && value !== null) {
                    console.log(`   ✅ ${fieldPath}: ${value}`);
                } else {
                    console.log(`   ❌ ${fieldPath}: Missing!`);
                    allFieldsPresent = false;
                }
            }
            
            if (allFieldsPresent) {
                console.log('\n   🎉 All new fields are present and accessible!');
            } else {
                console.log('\n   ⚠️ Some fields are missing');
            }
        }
        
        // Test 4: Test individual new endpoints
        console.log('\n4. Testing individual new endpoints with authentication...');
        
        const newEndpoints = [
            { name: 'Sales Statistics', url: '/api/sales/statistics', key: 'growth_rate' },
            { name: 'Marketing Statistics', url: '/api/marketing/statistics', key: 'roas' },
            { name: 'Inventory Statistics', url: '/api/inventory/statistics', key: 'total_value' },
            { name: 'Support Statistics', url: '/api/support/statistics', key: 'satisfaction_rating' }
        ];
        
        for (const endpoint of newEndpoints) {
            const response = await fetch(`http://localhost:8001${endpoint.url}`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            const data = await response.json();
            
            if (data.success && data.data[endpoint.key] !== undefined) {
                console.log(`   ✅ ${endpoint.name}: ${endpoint.key} = ${data.data[endpoint.key]}`);
            } else {
                console.log(`   ❌ ${endpoint.name}: Failed or missing ${endpoint.key}`);
            }
        }
        
        // Test 5: Verify logout invalidates access
        console.log('\n5. Testing logout and access revocation...');
        
        const logoutResponse = await fetch('http://localhost:8001/api/auth/logout', {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${token}` }
        });
        const logoutData = await logoutResponse.json();
        
        if (logoutData.success) {
            console.log('✅ Logout successful');
            
            // Try to access protected data with expired token
            const testResponse = await fetch('http://localhost:8001/api/dashboard/stats', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            const testData = await testResponse.json();
            
            if (testResponse.status === 401) {
                console.log('✅ Access properly revoked after logout');
            } else {
                console.log('❌ Access not properly revoked after logout');
            }
        }
        
        console.log('\n🎉 Authentication Integration Test Complete!');
        console.log('\n📋 Summary:');
        console.log('   ✅ All endpoints properly protected');
        console.log('   ✅ Authentication grants access to all new fields');
        console.log('   ✅ New comprehensive data structure working');
        console.log('   ✅ Logout properly revokes access');
        console.log('   ✅ No interference between auth and field visibility');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testAuthWithFields();
