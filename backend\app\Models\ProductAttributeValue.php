<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductAttributeValue extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'attribute_id',
        'value',
        'display_value'
    ];

    /**
     * Get the product that owns the attribute value.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the attribute.
     */
    public function attribute(): BelongsTo
    {
        return $this->belongsTo(ProductAttribute::class);
    }

    /**
     * Get the formatted value for display.
     */
    public function getFormattedValueAttribute(): string
    {
        if ($this->display_value) {
            return $this->display_value;
        }

        return $this->attribute->formatValue($this->value);
    }

    /**
     * Get the typed value based on attribute type.
     */
    public function getTypedValueAttribute()
    {
        switch ($this->attribute->type) {
            case 'number':
                return is_numeric($this->value) ? (float) $this->value : $this->value;

            case 'boolean':
                return in_array(strtolower($this->value), ['true', '1', 'yes', 'on']) ? true : false;

            case 'date':
                return $this->value ? date('Y-m-d', strtotime($this->value)) : null;

            case 'multiselect':
                return is_string($this->value) ? json_decode($this->value, true) : $this->value;

            default:
                return $this->value;
        }
    }

    /**
     * Set the value with proper formatting.
     */
    public function setValueAttribute($value)
    {
        // Handle array values (for multiselect)
        if (is_array($value)) {
            $this->attributes['value'] = json_encode($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }

    /**
     * Validate the value against attribute rules.
     */
    public function validateValue(): array
    {
        return $this->attribute->validateValue($this->typed_value);
    }

    /**
     * Check if value is valid.
     */
    public function isValid(): bool
    {
        return empty($this->validateValue());
    }

    /**
     * Get the search weight for this attribute value.
     */
    public function getSearchWeightAttribute(): int
    {
        if (!$this->attribute->is_searchable) {
            return 0;
        }

        // Higher weight for exact matches, lower for partial
        return $this->attribute->is_required ? 10 : 5;
    }

    /**
     * Scope for searchable values.
     */
    public function scopeSearchable($query)
    {
        return $query->whereHas('attribute', function ($q) {
            $q->where('is_searchable', true);
        });
    }

    /**
     * Scope for filterable values.
     */
    public function scopeFilterable($query)
    {
        return $query->whereHas('attribute', function ($q) {
            $q->where('is_filterable', true);
        });
    }

    /**
     * Scope for visible values.
     */
    public function scopeVisible($query)
    {
        return $query->whereHas('attribute', function ($q) {
            $q->where('is_visible', true);
        });
    }

    /**
     * Scope by attribute type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->whereHas('attribute', function ($q) use ($type) {
            $q->where('type', $type);
        });
    }

    /**
     * Scope by attribute slug.
     */
    public function scopeByAttribute($query, string $attributeSlug)
    {
        return $query->whereHas('attribute', function ($q) use ($attributeSlug) {
            $q->where('slug', $attributeSlug);
        });
    }

    /**
     * Scope for value search.
     */
    public function scopeValueLike($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('value', 'like', "%{$search}%")
              ->orWhere('display_value', 'like', "%{$search}%");
        });
    }

    /**
     * Get all unique values for an attribute.
     */
    public static function getUniqueValuesForAttribute(int $attributeId): array
    {
        return static::where('attribute_id', $attributeId)
            ->distinct('value')
            ->pluck('value')
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * Get products with specific attribute value.
     */
    public static function getProductsWithValue(string $attributeSlug, $value): \Illuminate\Database\Eloquent\Collection
    {
        return static::whereHas('attribute', function ($q) use ($attributeSlug) {
            $q->where('slug', $attributeSlug);
        })
        ->where('value', $value)
        ->with('product')
        ->get()
        ->pluck('product');
    }

    /**
     * Bulk update attribute values for a product.
     */
    public static function updateProductAttributes(int $productId, array $attributes): void
    {
        foreach ($attributes as $attributeId => $value) {
            static::updateOrCreate(
                [
                    'product_id' => $productId,
                    'attribute_id' => $attributeId,
                ],
                [
                    'value' => $value,
                    'display_value' => null, // Will be auto-generated if needed
                ]
            );
        }
    }

    /**
     * Delete attribute values not in the provided list.
     */
    public static function cleanupProductAttributes(int $productId, array $keepAttributeIds): void
    {
        static::where('product_id', $productId)
            ->whereNotIn('attribute_id', $keepAttributeIds)
            ->delete();
    }
}
