<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_variations', function (Blueprint $table) {
            $table->decimal('mrp', 10, 2)->nullable()->after('product_id');
            $table->decimal('selling_price', 10, 2)->nullable()->after('mrp');
            $table->decimal('discount_percentage', 5, 2)->default(0)->after('sale_price');
            $table->boolean('is_active')->default(true)->after('stock_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_variations', function (Blueprint $table) {
            $table->dropColumn(['mrp', 'selling_price', 'discount_percentage', 'is_active']);
        });
    }
};
