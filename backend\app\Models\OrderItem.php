<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'product_id',
        'product_variation_id',
        'product_name',
        'product_sku',
        'product_image',
        'price',
        'quantity',
        'total',
        'product_attributes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'total' => 'decimal:2',
        'quantity' => 'integer',
        'product_attributes' => 'array',
    ];

    /**
     * Get the order that owns the item.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the product variation.
     */
    public function productVariation(): BelongsTo
    {
        return $this->belongsTo(ProductVariation::class);
    }

    /**
     * Get the product image URL.
     */
    public function getProductImageUrlAttribute(): ?string
    {
        if ($this->product_image) {
            if (str_starts_with($this->product_image, 'http')) {
                return $this->product_image;
            }
            return asset('storage/' . $this->product_image);
        }

        // Fallback to product's main image
        if ($this->product) {
            return $this->product->main_image_url;
        }

        return null;
    }

    /**
     * Get formatted product attributes.
     */
    public function getFormattedAttributesAttribute(): string
    {
        if (!$this->product_attributes) {
            return '';
        }

        $attributes = [];
        foreach ($this->product_attributes as $key => $value) {
            $attributes[] = ucfirst($key) . ': ' . $value;
        }

        return implode(', ', $attributes);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($orderItem) {
            // Calculate total
            $orderItem->total = $orderItem->price * $orderItem->quantity;
        });

        static::updating(function ($orderItem) {
            // Recalculate total if price or quantity changed
            if ($orderItem->isDirty(['price', 'quantity'])) {
                $orderItem->total = $orderItem->price * $orderItem->quantity;
            }
        });
    }
}
