<div class="user-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>Users</h1>
      <p>Manage user accounts and permissions</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="createUser()">
        <mat-icon>person_add</mat-icon>
        Add User
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-container">
        <!-- Search -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search users</mat-label>
          <input
            matInput
            placeholder="Search by name, email, or phone"
            (input)="onSearch($event.target.value)"
            [value]="filters.search || ''"
          >
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Role Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Role</mat-label>
          <mat-select [(value)]="filters.role" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of roleOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Status Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(value)]="filters.is_active" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of statusOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Email Verification Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Email Status</mat-label>
          <mat-select [(value)]="filters.email_verified" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let option of emailVerificationOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Date Range -->
        <mat-form-field appearance="outline">
          <mat-label>From Date</mat-label>
          <input
            matInput
            type="date"
            [(ngModel)]="filters.date_from"
            (change)="onFilterChange()"
          >
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>To Date</mat-label>
          <input
            matInput
            type="date"
            [(ngModel)]="filters.date_to"
            (change)="onFilterChange()"
          >
        </mat-form-field>

        <!-- Clear Filters -->
        <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
          <mat-icon>clear</mat-icon>
          Clear Filters
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Bulk Actions -->
  <div class="bulk-actions" *ngIf="selection.selected.length > 0">
    <mat-card>
      <mat-card-content>
        <div class="bulk-actions-content">
          <span class="selection-count">
            {{ selection.selected.length }} user(s) selected
          </span>
          
          <div class="bulk-action-buttons">
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus(true)"
            >
              <mat-icon>check_circle</mat-icon>
              Activate
            </button>
            
            <button
              mat-stroked-button
              [disabled]="bulkActionLoading"
              (click)="bulkUpdateStatus(false)"
            >
              <mat-icon>block</mat-icon>
              Deactivate
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Data Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <!-- Table Actions -->
      <div class="table-actions">
        <div class="table-info">
          <span>{{ totalItems }} users found</span>
        </div>
        
        <div class="table-action-buttons">
          <button mat-icon-button (click)="loadUsers()" [disabled]="loading">
            <mat-icon>refresh</mat-icon>
          </button>
          
          <button mat-icon-button (click)="exportUsers()">
            <mat-icon>download</mat-icon>
          </button>
        </div>
      </div>

      <!-- Loading Indicator -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading users...</p>
      </div>

      <!-- Data Table -->
      <div class="table-container" *ngIf="!loading">
        <table mat-table [dataSource]="dataSource" matSort class="users-table">
          
          <!-- Select Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? masterToggle() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
              ></mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let user">
              <mat-checkbox
                (click)="$event.stopPropagation()"
                (change)="$event ? selection.toggle(user) : null"
                [checked]="selection.isSelected(user)"
              ></mat-checkbox>
            </td>
          </ng-container>

          <!-- Profile Image Column -->
          <ng-container matColumnDef="profile_image">
            <th mat-header-cell *matHeaderCellDef>Avatar</th>
            <td mat-cell *matCellDef="let user">
              <div class="user-avatar">
                <img
                  [src]="user.profile_image_url || '/assets/images/default-avatar.png'"
                  [alt]="user.name"
                  (error)="$event.target.src='/assets/images/default-avatar.png'"
                >
              </div>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let user">
              <div class="user-name">
                <span class="name">{{ user.name }}</span>
                <span class="phone" *ngIf="user.phone">{{ user.phone }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let user">
              <div class="email-info">
                <span class="email">{{ user.email }}</span>
                <mat-icon 
                  *ngIf="user.email_verified_at" 
                  class="verified-icon" 
                  matTooltip="Email Verified"
                >
                  verified
                </mat-icon>
                <mat-icon 
                  *ngIf="!user.email_verified_at" 
                  class="unverified-icon" 
                  matTooltip="Email Not Verified"
                >
                  warning
                </mat-icon>
              </div>
            </td>
          </ng-container>

          <!-- Role Column -->
          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Role</th>
            <td mat-cell *matCellDef="let user">
              <mat-chip [color]="getRoleColor(user.role)">
                {{ getRoleName(user.role) }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let user">
              <mat-slide-toggle
                [checked]="user.is_active"
                (change)="toggleUserStatus(user)"
                [disabled]="user.role === 'super_admin'"
              ></mat-slide-toggle>
            </td>
          </ng-container>

          <!-- Email Verified Column -->
          <ng-container matColumnDef="email_verified">
            <th mat-header-cell *matHeaderCellDef>Email Verified</th>
            <td mat-cell *matCellDef="let user">
              <mat-icon 
                *ngIf="user.email_verified_at" 
                color="primary"
              >
                check_circle
              </mat-icon>
              <mat-icon 
                *ngIf="!user.email_verified_at" 
                color="warn"
              >
                cancel
              </mat-icon>
            </td>
          </ng-container>

          <!-- Last Login Column -->
          <ng-container matColumnDef="last_login">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Login</th>
            <td mat-cell *matCellDef="let user">
              <span *ngIf="user.last_login_at">{{ user.last_login_at | date:'short' }}</span>
              <span *ngIf="!user.last_login_at" class="never-logged-in">Never</span>
            </td>
          </ng-container>

          <!-- Orders Count Column -->
          <ng-container matColumnDef="orders_count">
            <th mat-header-cell *matHeaderCellDef>Orders</th>
            <td mat-cell *matCellDef="let user">
              <span class="orders-count">{{ user.orders_count || 0 }}</span>
            </td>
          </ng-container>

          <!-- Total Spent Column -->
          <ng-container matColumnDef="total_spent">
            <th mat-header-cell *matHeaderCellDef>Total Spent</th>
            <td mat-cell *matCellDef="let user">
              <span class="total-spent">{{ formatCurrency(user.total_spent || 0) }}</span>
            </td>
          </ng-container>

          <!-- Created At Column -->
          <ng-container matColumnDef="created_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Created</th>
            <td mat-cell *matCellDef="let user">
              {{ user.created_at | date:'short' }}
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let user">
              <div class="action-buttons">
                <button
                  mat-icon-button
                  [matTooltip]="'View User'"
                  (click)="viewUser(user)"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  [matTooltip]="'Edit User'"
                  (click)="editUser(user)"
                >
                  <mat-icon>edit</mat-icon>
                </button>

                <button
                  mat-icon-button
                  [matTooltip]="'Send Password Reset'"
                  (click)="sendPasswordReset(user)"
                >
                  <mat-icon>lock_reset</mat-icon>
                </button>

                <button
                  mat-icon-button
                  [matTooltip]="'Verify Email'"
                  (click)="verifyEmail(user)"
                  *ngIf="!user.email_verified_at"
                >
                  <mat-icon>mark_email_read</mat-icon>
                </button>
                
                <button
                  mat-icon-button
                  [matTooltip]="'Delete User'"
                  color="warn"
                  (click)="deleteUser(user)"
                  [disabled]="user.role === 'super_admin'"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data -->
        <div *ngIf="dataSource.data.length === 0" class="no-data">
          <mat-icon>people</mat-icon>
          <h3>No users found</h3>
          <p>Try adjusting your filters or create a new user.</p>
          <button mat-raised-button color="primary" (click)="createUser()">
            <mat-icon>person_add</mat-icon>
            Create User
          </button>
        </div>
      </div>

      <!-- Paginator -->
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        showFirstLastButtons
        *ngIf="!loading && dataSource.data.length > 0"
      ></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
