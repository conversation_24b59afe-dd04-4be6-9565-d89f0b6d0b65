<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Transaction;
use App\Models\Order;
use App\Models\User;
use Carbon\Carbon;

class TransactionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some orders to create transactions for
        $orders = Order::with('user')->take(20)->get();

        if ($orders->isEmpty()) {
            $this->command->warn('No orders found. Please run OrdersSeeder first.');
            return;
        }

        // Get the super admin user to set as creator
        $superAdmin = User::where('email', '<EMAIL>')->first();

        $transactionCount = 0;

        foreach ($orders as $order) {
            // Create a payment transaction for each order
            $paymentTransaction = Transaction::create([
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'type' => Transaction::TYPE_PAYMENT,
                'status' => $this->getRandomPaymentStatus(),
                'payment_method' => $order->payment_method,
                'gateway' => $this->getGatewayForPaymentMethod($order->payment_method),
                'gateway_transaction_id' => $this->generateGatewayTransactionId(),
                'amount' => $order->total_amount,
                'currency' => $order->currency,
                'fee' => $this->calculateFee($order->total_amount, $order->payment_method),
                'description' => "Payment for order {$order->order_number}",
                'gateway_response' => $this->generateGatewayResponse($order->payment_method),
                'processed_at' => $this->getRandomProcessedAt(),
                'created_by' => $superAdmin->id,
                'created_at' => $order->created_at->addMinutes(rand(1, 30)),
            ]);

            $transactionCount++;

            // Create refund transactions for some completed payments (20% chance)
            if ($paymentTransaction->status === Transaction::STATUS_COMPLETED && rand(1, 100) <= 20) {
                $refundAmount = rand(1, 2) === 1 ? 
                    $paymentTransaction->amount : // Full refund
                    round($paymentTransaction->amount * (rand(30, 80) / 100), 2); // Partial refund

                $refundType = $refundAmount < $paymentTransaction->amount ? 
                    Transaction::TYPE_PARTIAL_REFUND : 
                    Transaction::TYPE_REFUND;

                $refundTransaction = Transaction::create([
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'type' => $refundType,
                    'status' => Transaction::STATUS_COMPLETED,
                    'payment_method' => $order->payment_method,
                    'gateway' => $paymentTransaction->gateway,
                    'gateway_transaction_id' => $this->generateGatewayTransactionId(),
                    'amount' => $refundAmount,
                    'currency' => $order->currency,
                    'fee' => 0, // Usually no fee for refunds
                    'reference' => $paymentTransaction->transaction_id,
                    'description' => "Refund for order {$order->order_number}",
                    'gateway_response' => $this->generateRefundGatewayResponse(),
                    'processed_at' => $paymentTransaction->processed_at->addDays(rand(1, 30)),
                    'created_by' => $superAdmin->id,
                    'created_at' => $paymentTransaction->created_at->addDays(rand(1, 30)),
                ]);

                $transactionCount++;
            }

            // Create failed payment attempts for some orders (10% chance)
            if (rand(1, 100) <= 10) {
                $failedTransaction = Transaction::create([
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'type' => Transaction::TYPE_PAYMENT,
                    'status' => Transaction::STATUS_FAILED,
                    'payment_method' => $order->payment_method,
                    'gateway' => $this->getGatewayForPaymentMethod($order->payment_method),
                    'gateway_transaction_id' => $this->generateGatewayTransactionId(),
                    'amount' => $order->total_amount,
                    'currency' => $order->currency,
                    'fee' => 0, // No fee for failed transactions
                    'description' => "Failed payment attempt for order {$order->order_number}",
                    'failure_reason' => $this->getRandomFailureReason(),
                    'gateway_response' => $this->generateFailedGatewayResponse(),
                    'created_by' => $superAdmin->id,
                    'created_at' => $order->created_at->subMinutes(rand(5, 60)),
                ]);

                $transactionCount++;
            }
        }

        // Create some standalone transactions (adjustments, chargebacks)
        for ($i = 0; $i < 5; $i++) {
            $randomOrder = $orders->random();
            
            Transaction::create([
                'order_id' => $randomOrder->id,
                'user_id' => $randomOrder->user_id,
                'type' => rand(1, 2) === 1 ? Transaction::TYPE_ADJUSTMENT : Transaction::TYPE_CHARGEBACK,
                'status' => Transaction::STATUS_COMPLETED,
                'payment_method' => $randomOrder->payment_method,
                'gateway' => Transaction::GATEWAY_MANUAL,
                'amount' => round(rand(500, 5000) / 100, 2), // Random amount between $5-$50
                'currency' => $randomOrder->currency,
                'fee' => 0,
                'description' => rand(1, 2) === 1 ? 
                    'Manual adjustment for customer service issue' : 
                    'Chargeback from payment processor',
                'processed_at' => now()->subDays(rand(1, 90)),
                'created_by' => $superAdmin->id,
                'created_at' => now()->subDays(rand(1, 90)),
            ]);

            $transactionCount++;
        }

        $this->command->info("Transactions seeded successfully!");
        $this->command->info("Created {$transactionCount} sample transactions");
        $this->command->info("Includes payments, refunds, failed attempts, adjustments, and chargebacks");
    }

    /**
     * Get random payment status.
     */
    private function getRandomPaymentStatus(): string
    {
        $statuses = [
            Transaction::STATUS_COMPLETED => 70, // 70% chance
            Transaction::STATUS_PENDING => 15,   // 15% chance
            Transaction::STATUS_PROCESSING => 10, // 10% chance
            Transaction::STATUS_FAILED => 5,     // 5% chance
        ];

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($statuses as $status => $percentage) {
            $cumulative += $percentage;
            if ($rand <= $cumulative) {
                return $status;
            }
        }

        return Transaction::STATUS_COMPLETED;
    }

    /**
     * Get gateway for payment method.
     */
    private function getGatewayForPaymentMethod(string $paymentMethod): string
    {
        return match($paymentMethod) {
            'razorpay', 'gpay', 'upi' => Transaction::GATEWAY_RAZORPAY,
            'bank_transfer' => Transaction::GATEWAY_MANUAL,
            'cod' => Transaction::GATEWAY_MANUAL,
            default => Transaction::GATEWAY_RAZORPAY,
        };
    }

    /**
     * Generate gateway transaction ID.
     */
    private function generateGatewayTransactionId(): string
    {
        return 'gw_' . strtolower(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'));
    }

    /**
     * Calculate fee based on amount and payment method.
     */
    private function calculateFee(float $amount, string $paymentMethod): float
    {
        $feeRates = [
            'razorpay' => 0.025, // 2.5%
            'gpay' => 0.02,      // 2%
            'upi' => 0.015,      // 1.5%
            'bank_transfer' => 0.01, // 1%
            'cod' => 0,          // No fee
        ];

        $rate = $feeRates[$paymentMethod] ?? 0.02;
        return round($amount * $rate, 2);
    }

    /**
     * Generate gateway response.
     */
    private function generateGatewayResponse(string $paymentMethod): array
    {
        return [
            'gateway' => $this->getGatewayForPaymentMethod($paymentMethod),
            'response_code' => '200',
            'response_message' => 'Success',
            'transaction_time' => now()->toISOString(),
            'gateway_reference' => 'ref_' . strtolower(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')),
        ];
    }

    /**
     * Generate refund gateway response.
     */
    private function generateRefundGatewayResponse(): array
    {
        return [
            'gateway' => 'razorpay',
            'response_code' => '200',
            'response_message' => 'Refund processed successfully',
            'refund_time' => now()->toISOString(),
            'refund_reference' => 'rfnd_' . strtolower(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')),
        ];
    }

    /**
     * Generate failed gateway response.
     */
    private function generateFailedGatewayResponse(): array
    {
        return [
            'gateway' => 'razorpay',
            'response_code' => '400',
            'response_message' => 'Payment failed',
            'error_code' => 'PAYMENT_FAILED',
            'error_description' => 'Insufficient funds',
        ];
    }

    /**
     * Get random failure reason.
     */
    private function getRandomFailureReason(): string
    {
        $reasons = [
            'Insufficient funds',
            'Card declined',
            'Invalid card details',
            'Transaction timeout',
            'Bank server error',
            'Payment gateway error',
            'User cancelled payment',
        ];

        return $reasons[array_rand($reasons)];
    }

    /**
     * Get random processed at time.
     */
    private function getRandomProcessedAt(): ?Carbon
    {
        // 90% chance of being processed
        if (rand(1, 100) <= 90) {
            return now()->subMinutes(rand(1, 1440)); // Within last 24 hours
        }

        return null; // Not processed yet
    }
}
