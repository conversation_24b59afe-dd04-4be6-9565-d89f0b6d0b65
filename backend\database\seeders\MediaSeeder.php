<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Media;
use App\Models\User;
use Carbon\Carbon;

class MediaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('📁 Seeding media files...');

        // Get admin users who can upload media
        $adminUsers = User::role(['super-admin', 'admin', 'editor'])->get();

        if ($adminUsers->isEmpty()) {
            $this->command->warn('No admin users found. Skipping media seeding.');
            return;
        }

        // Sample media files data
        $mediaFiles = [
            // Product images
            [
                'filename' => 'product-1.jpg',
                'original_filename' => 'smartphone-galaxy.jpg',
                'path' => 'media/products/product-1.jpg',
                'url' => '/storage/media/products/product-1.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 245760, // ~240KB
                'alt_text' => 'Samsung Galaxy Smartphone',
                'title' => 'Galaxy Smartphone Product Image',
                'description' => 'High-quality product image of Samsung Galaxy smartphone',
                'folder' => 'products',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 800,
                    'height' => 600,
                    'orientation' => 'landscape',
                ],
            ],
            [
                'filename' => 'product-2.jpg',
                'original_filename' => 'laptop-macbook.jpg',
                'path' => 'media/products/product-2.jpg',
                'url' => '/storage/media/products/product-2.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 312450, // ~305KB
                'alt_text' => 'MacBook Pro Laptop',
                'title' => 'MacBook Pro Product Image',
                'description' => 'Professional product photo of MacBook Pro laptop',
                'folder' => 'products',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 1200,
                    'height' => 800,
                    'orientation' => 'landscape',
                ],
            ],
            [
                'filename' => 'product-3.jpg',
                'original_filename' => 'headphones-wireless.jpg',
                'path' => 'media/products/product-3.jpg',
                'url' => '/storage/media/products/product-3.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 189320, // ~185KB
                'alt_text' => 'Wireless Bluetooth Headphones',
                'title' => 'Wireless Headphones Product Image',
                'description' => 'Studio shot of premium wireless headphones',
                'folder' => 'products',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 600,
                    'height' => 600,
                    'orientation' => 'square',
                ],
            ],
            // Category images
            [
                'filename' => 'category-electronics.jpg',
                'original_filename' => 'electronics-banner.jpg',
                'path' => 'media/categories/category-electronics.jpg',
                'url' => '/storage/media/categories/category-electronics.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 456780, // ~446KB
                'alt_text' => 'Electronics Category Banner',
                'title' => 'Electronics Category Image',
                'description' => 'Banner image for electronics category',
                'folder' => 'categories',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 1920,
                    'height' => 600,
                    'orientation' => 'landscape',
                ],
            ],
            [
                'filename' => 'category-clothing.jpg',
                'original_filename' => 'fashion-banner.jpg',
                'path' => 'media/categories/category-clothing.jpg',
                'url' => '/storage/media/categories/category-clothing.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 398240, // ~389KB
                'alt_text' => 'Clothing & Fashion Category Banner',
                'title' => 'Fashion Category Image',
                'description' => 'Stylish banner for clothing and fashion category',
                'folder' => 'categories',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 1920,
                    'height' => 600,
                    'orientation' => 'landscape',
                ],
            ],
            // Brand logos
            [
                'filename' => 'logo-main.png',
                'original_filename' => 'company-logo.png',
                'path' => 'media/logos/logo-main.png',
                'url' => '/storage/media/logos/logo-main.png',
                'mime_type' => 'image/png',
                'size' => 45680, // ~45KB
                'alt_text' => 'Company Logo',
                'title' => 'Main Company Logo',
                'description' => 'Primary logo for the e-commerce platform',
                'folder' => 'logos',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 300,
                    'height' => 100,
                    'orientation' => 'landscape',
                    'has_transparency' => true,
                ],
            ],
            [
                'filename' => 'favicon.ico',
                'original_filename' => 'site-favicon.ico',
                'path' => 'media/logos/favicon.ico',
                'url' => '/storage/media/logos/favicon.ico',
                'mime_type' => 'image/x-icon',
                'size' => 4286, // ~4KB
                'alt_text' => 'Website Favicon',
                'title' => 'Site Favicon',
                'description' => 'Small icon for browser tabs and bookmarks',
                'folder' => 'logos',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 32,
                    'height' => 32,
                    'orientation' => 'square',
                ],
            ],
            // Documents
            [
                'filename' => 'user-manual.pdf',
                'original_filename' => 'product-user-manual.pdf',
                'path' => 'media/documents/user-manual.pdf',
                'url' => '/storage/media/documents/user-manual.pdf',
                'mime_type' => 'application/pdf',
                'size' => 1245680, // ~1.2MB
                'alt_text' => 'Product User Manual',
                'title' => 'User Manual PDF',
                'description' => 'Comprehensive user manual for product setup and usage',
                'folder' => 'documents',
                'disk' => 'public',
                'is_public' => false,
                'metadata' => [
                    'pages' => 24,
                    'version' => '2.1',
                ],
            ],
            [
                'filename' => 'terms-conditions.pdf',
                'original_filename' => 'terms-and-conditions.pdf',
                'path' => 'media/documents/terms-conditions.pdf',
                'url' => '/storage/media/documents/terms-conditions.pdf',
                'mime_type' => 'application/pdf',
                'size' => 567890, // ~555KB
                'alt_text' => 'Terms and Conditions',
                'title' => 'Terms and Conditions Document',
                'description' => 'Legal terms and conditions for platform usage',
                'folder' => 'documents',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'pages' => 12,
                    'version' => '1.3',
                ],
            ],
            // Banners
            [
                'filename' => 'hero-banner.jpg',
                'original_filename' => 'homepage-hero-banner.jpg',
                'path' => 'media/banners/hero-banner.jpg',
                'url' => '/storage/media/banners/hero-banner.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 678900, // ~663KB
                'alt_text' => 'Homepage Hero Banner',
                'title' => 'Main Hero Banner',
                'description' => 'Eye-catching banner for homepage hero section',
                'folder' => 'banners',
                'disk' => 'public',
                'is_public' => true,
                'metadata' => [
                    'width' => 1920,
                    'height' => 800,
                    'orientation' => 'landscape',
                ],
            ],
        ];

        // Create media records
        foreach ($mediaFiles as $index => $mediaData) {
            $uploader = $adminUsers->random();
            $createdAt = Carbon::now()->subDays(rand(1, 30));

            Media::create(array_merge($mediaData, [
                'uploaded_by' => $uploader->id,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]));
        }

        $this->command->info('✅ Media files seeded successfully!');
        $this->command->info('📊 Created ' . Media::count() . ' media files');
        $this->command->info('📁 Folders: products, categories, logos, documents, banners');
    }
}
