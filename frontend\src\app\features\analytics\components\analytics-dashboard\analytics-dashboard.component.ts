import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subject, interval } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { 
  AnalyticsService, 
  DashboardStats, 
  AnalyticsFilters,
  SalesAnalytics,
  CustomerAnalytics,
  ProductAnalytics,
  TrafficAnalytics
} from '../../services/analytics.service';
import { LoadingService } from '../../../../core/services/loading.service';

@Component({
  selector: 'app-analytics-dashboard',
  templateUrl: './analytics-dashboard.component.html',
  styleUrls: ['./analytics-dashboard.component.scss']
})
export class AnalyticsDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Data
  dashboardStats: DashboardStats | null = null;
  salesAnalytics: SalesAnalytics | null = null;
  customerAnalytics: CustomerAnalytics | null = null;
  productAnalytics: ProductAnalytics | null = null;
  trafficAnalytics: TrafficAnalytics | null = null;

  // Loading states
  loading = false;
  refreshing = false;

  // Filters
  filters: AnalyticsFilters = {
    period: 'last_30_days',
    start_date: '',
    end_date: '',
    compare_period: false
  };

  // UI state
  selectedTab = 'overview';
  autoRefresh = false;
  refreshInterval = 30000; // 30 seconds

  // Chart options
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Period options
  periodOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last_7_days', label: 'Last 7 Days' },
    { value: 'last_30_days', label: 'Last 30 Days' },
    { value: 'last_90_days', label: 'Last 90 Days' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'this_year', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  constructor(
    private analyticsService: AnalyticsService,
    private loadingService: LoadingService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeFilters();
    this.loadAllAnalytics();
    this.setupAutoRefresh();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    const dateRange = this.analyticsService.getDateRangeForPeriod(this.filters.period!);
    this.filters = {
      ...this.filters,
      ...dateRange
    };
  }

  private setupAutoRefresh(): void {
    interval(this.refreshInterval)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.autoRefresh && !this.loading) {
          this.refreshData();
        }
      });
  }

  loadAllAnalytics(): void {
    this.loading = true;
    this.loadingService.setLoading(true);

    Promise.all([
      this.loadDashboardStats(),
      this.loadSalesAnalytics(),
      this.loadCustomerAnalytics(),
      this.loadProductAnalytics(),
      this.loadTrafficAnalytics()
    ]).finally(() => {
      this.loading = false;
      this.refreshing = false;
      this.loadingService.setLoading(false);
    });
  }

  private async loadDashboardStats(): Promise<void> {
    try {
      const response = await this.analyticsService.getDashboardStats(this.filters).toPromise();
      if (response?.success) {
        this.dashboardStats = response.data;
      }
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      this.showError('Failed to load dashboard statistics');
    }
  }

  private async loadSalesAnalytics(): Promise<void> {
    try {
      const response = await this.analyticsService.getSalesAnalytics(this.filters).toPromise();
      if (response?.success) {
        this.salesAnalytics = response.data;
      }
    } catch (error) {
      console.error('Error loading sales analytics:', error);
      this.showError('Failed to load sales analytics');
    }
  }

  private async loadCustomerAnalytics(): Promise<void> {
    try {
      const response = await this.analyticsService.getCustomerAnalytics(this.filters).toPromise();
      if (response?.success) {
        this.customerAnalytics = response.data;
      }
    } catch (error) {
      console.error('Error loading customer analytics:', error);
      this.showError('Failed to load customer analytics');
    }
  }

  private async loadProductAnalytics(): Promise<void> {
    try {
      const response = await this.analyticsService.getProductAnalytics(this.filters).toPromise();
      if (response?.success) {
        this.productAnalytics = response.data;
      }
    } catch (error) {
      console.error('Error loading product analytics:', error);
      this.showError('Failed to load product analytics');
    }
  }

  private async loadTrafficAnalytics(): Promise<void> {
    try {
      const response = await this.analyticsService.getTrafficAnalytics(this.filters).toPromise();
      if (response?.success) {
        this.trafficAnalytics = response.data;
      }
    } catch (error) {
      console.error('Error loading traffic analytics:', error);
      this.showError('Failed to load traffic analytics');
    }
  }

  onPeriodChange(): void {
    if (this.filters.period !== 'custom') {
      const dateRange = this.analyticsService.getDateRangeForPeriod(this.filters.period!);
      this.filters = {
        ...this.filters,
        ...dateRange
      };
    }
    this.loadAllAnalytics();
  }

  onDateRangeChange(): void {
    if (this.filters.start_date && this.filters.end_date) {
      this.filters.period = 'custom';
      this.loadAllAnalytics();
    }
  }

  onTabChange(tab: string): void {
    this.selectedTab = tab;
  }

  refreshData(): void {
    this.refreshing = true;
    this.loadAllAnalytics();
  }

  toggleAutoRefresh(): void {
    this.autoRefresh = !this.autoRefresh;
    
    if (this.autoRefresh) {
      this.snackBar.open('Auto-refresh enabled', 'Close', {
        duration: 2000,
        panelClass: ['success-snackbar']
      });
    } else {
      this.snackBar.open('Auto-refresh disabled', 'Close', {
        duration: 2000,
        panelClass: ['info-snackbar']
      });
    }
  }

  exportReport(type: 'sales' | 'customers' | 'products' | 'traffic', format: 'csv' | 'excel' | 'pdf'): void {
    this.loadingService.setLoading(true);

    this.analyticsService.exportReport(type, format, this.filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${type}-analytics-${new Date().toISOString().split('T')[0]}.${format}`;
        link.click();
        window.URL.revokeObjectURL(url);
        
        this.snackBar.open('Report exported successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error exporting report:', error);
        this.snackBar.open('Error exporting report', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Utility methods
  formatCurrency(amount: number): string {
    return this.analyticsService.formatCurrency(amount);
  }

  formatPercentage(value: number): string {
    return this.analyticsService.formatPercentage(value);
  }

  formatNumber(value: number): string {
    return this.analyticsService.formatNumber(value);
  }

  getGrowthIcon(growth: number): string {
    if (growth > 0) return 'trending_up';
    if (growth < 0) return 'trending_down';
    return 'trending_flat';
  }

  getGrowthColor(growth: number): string {
    if (growth > 0) return 'success';
    if (growth < 0) return 'warn';
    return 'primary';
  }

  getSelectedPeriodLabel(): string {
    const option = this.periodOptions.find(p => p.value === this.filters.period);
    return option ? option.label : 'Custom Range';
  }

  get isCustomPeriod(): boolean {
    return this.filters.period === 'custom';
  }

  get canRefresh(): boolean {
    return !this.loading && !this.refreshing;
  }

  get hasData(): boolean {
    return this.dashboardStats !== null;
  }

  // Chart data getters
  get revenueChartData(): any {
    if (!this.salesAnalytics?.revenue_by_day) return null;

    return {
      labels: this.salesAnalytics.revenue_by_day.map(item => item.label),
      datasets: [
        {
          label: 'Revenue',
          data: this.salesAnalytics.revenue_by_day.map(item => item.value),
          borderColor: '#3f51b5',
          backgroundColor: 'rgba(63, 81, 181, 0.1)',
          fill: true,
          tension: 0.4
        }
      ]
    };
  }

  get ordersChartData(): any {
    if (!this.dashboardStats?.orders_by_status) return null;

    return {
      labels: this.dashboardStats.orders_by_status.map(item => item.status),
      datasets: [
        {
          data: this.dashboardStats.orders_by_status.map(item => item.count),
          backgroundColor: [
            '#4caf50',
            '#ff9800',
            '#f44336',
            '#2196f3',
            '#9c27b0'
          ]
        }
      ]
    };
  }

  get customerSegmentChartData(): any {
    if (!this.customerAnalytics?.customer_segments) return null;

    return {
      labels: this.customerAnalytics.customer_segments.map(item => item.label),
      datasets: [
        {
          data: this.customerAnalytics.customer_segments.map(item => item.value),
          backgroundColor: [
            '#3f51b5',
            '#4caf50',
            '#ff9800',
            '#f44336',
            '#9c27b0'
          ]
        }
      ]
    };
  }

  get topProductsChartData(): any {
    if (!this.productAnalytics?.top_selling_products) return null;

    return {
      labels: this.productAnalytics.top_selling_products.map(item => item.name),
      datasets: [
        {
          label: 'Sales',
          data: this.productAnalytics.top_selling_products.map(item => item.total_sales),
          backgroundColor: '#3f51b5'
        }
      ]
    };
  }

  get trafficSourcesChartData(): any {
    if (!this.trafficAnalytics?.traffic_sources) return null;

    return {
      labels: this.trafficAnalytics.traffic_sources.map(item => item.label),
      datasets: [
        {
          data: this.trafficAnalytics.traffic_sources.map(item => item.value),
          backgroundColor: [
            '#3f51b5',
            '#4caf50',
            '#ff9800',
            '#f44336',
            '#9c27b0',
            '#607d8b'
          ]
        }
      ]
    };
  }
}
