import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { Alert, AppState } from 'react-native';
import OfflineService from '../services/OfflineService';

const OfflineContext = createContext();

export const useOffline = () => {
  const context = useContext(OfflineContext);
  if (!context) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
};

export const OfflineProvider = ({ children }) => {
  const [isOnline, setIsOnline] = useState(true);
  const [pendingRequests, setPendingRequests] = useState([]);
  const [syncInProgress, setSyncInProgress] = useState(false);
  const [offlineData, setOfflineData] = useState({});

  useEffect(() => {
    // Initialize offline service and check connection
    initializeOfflineService();
    
    // Handle app state changes
    const handleAppStateChange = (nextAppState) => {
      if (nextAppState === 'active') {
        checkConnectionAndSync();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, []);

  const initializeOfflineService = async () => {
    try {
      // Check initial connection state
      const connectionState = await OfflineService.checkConnection();
      setIsOnline(connectionState);

      // Add network listener
      const removeListener = OfflineService.addNetworkListener((online) => {
        setIsOnline(online);
        if (online) {
          syncOfflineData();
        }
      });

      // Load initial data
      await loadOfflineData();

      // Preload essential data if online
      if (connectionState) {
        await OfflineService.preloadEssentialData();
      }

      return removeListener;
    } catch (error) {
      console.error('Error initializing offline service:', error);
    }
  };

  const checkConnectionAndSync = async () => {
    try {
      const isConnected = await OfflineService.checkConnection();
      setIsOnline(isConnected);
      
      if (isConnected) {
        await syncOfflineData();
      }
    } catch (error) {
      console.error('Error checking connection:', error);
    }
  };

  const loadOfflineData = async () => {
    try {
      const networkState = OfflineService.getNetworkState();
      setPendingRequests(networkState.pendingCount);
      setSyncInProgress(false);
    } catch (error) {
      console.error('Error loading offline data:', error);
    }
  };

  const syncOfflineData = useCallback(async () => {
    if (syncInProgress || !isOnline) {
      return;
    }

    try {
      setSyncInProgress(true);
      await OfflineService.syncOfflineData();
      
      // Refresh pending requests count
      const networkState = OfflineService.getNetworkState();
      setPendingRequests(networkState.pendingCount);
    } catch (error) {
      console.error('Error syncing offline data:', error);
      Alert.alert('Sync Error', 'Failed to sync offline data. Will retry later.');
    } finally {
      setSyncInProgress(false);
    }
  }, [isOnline, syncInProgress]);

  const storeOfflineData = async (key, data, expiryHours = 24) => {
    try {
      await OfflineService.storeOfflineData(key, data, expiryHours);
      setOfflineData(prev => ({ ...prev, [key]: data }));
    } catch (error) {
      console.error('Error storing offline data:', error);
      throw error;
    }
  };

  const getOfflineData = async (key) => {
    try {
      const data = await OfflineService.getOfflineData(key);
      return data;
    } catch (error) {
      console.error('Error getting offline data:', error);
      return null;
    }
  };

  const queueRequest = async (request) => {
    try {
      const requestId = await OfflineService.queueRequest(request);
      
      // Update pending requests count
      const networkState = OfflineService.getNetworkState();
      setPendingRequests(networkState.pendingCount);
      
      return requestId;
    } catch (error) {
      console.error('Error queuing request:', error);
      throw error;
    }
  };

  const handleOfflineAction = async (actionType, data, fallbackMessage) => {
    if (isOnline) {
      // If online, proceed normally
      return { success: true, online: true };
    }

    // If offline, queue the action
    try {
      await queueRequest({
        method: 'POST',
        url: `/offline-actions/${actionType}`,
        data: data,
        timestamp: Date.now()
      });

      Alert.alert(
        'Offline Mode',
        fallbackMessage || 'Action saved and will be synced when connection is restored.',
        [{ text: 'OK' }]
      );

      return { success: true, online: false, queued: true };
    } catch (error) {
      Alert.alert('Error', 'Failed to save action for later sync.');
      return { success: false, error };
    }
  };

  const addToCartOffline = async (productId, quantity, variationId = null) => {
    try {
      const cartData = {
        product_id: productId,
        quantity: quantity,
        variation_id: variationId,
        price: 0, // Would need to get from offline product data
        timestamp: Date.now()
      };

      const updatedCart = await OfflineService.handleOfflineCart('add', cartData);
      
      return {
        success: true,
        message: 'Item added to cart (offline)',
        cart: updatedCart
      };
    } catch (error) {
      console.error('Error adding to cart offline:', error);
      throw error;
    }
  };

  const getOfflineCart = async () => {
    try {
      return await OfflineService.getOfflineCart();
    } catch (error) {
      console.error('Error getting offline cart:', error);
      return { items: [], total: 0 };
    }
  };

  const clearOfflineData = async () => {
    try {
      await OfflineService.clearOfflineData();
      setOfflineData({});
      setPendingRequests(0);
      Alert.alert('Success', 'All offline data cleared.');
    } catch (error) {
      console.error('Error clearing offline data:', error);
      Alert.alert('Error', 'Failed to clear offline data.');
    }
  };

  const getStorageStats = async () => {
    try {
      return await OfflineService.getStorageStats();
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return null;
    }
  };

  const preloadEssentialData = async () => {
    if (!isOnline) {
      Alert.alert('Offline', 'Cannot preload data while offline.');
      return;
    }

    try {
      await OfflineService.preloadEssentialData();
      Alert.alert('Success', 'Essential data preloaded for offline use.');
    } catch (error) {
      console.error('Error preloading data:', error);
      Alert.alert('Error', 'Failed to preload data.');
    }
  };

  const showOfflineStatus = () => {
    const networkState = OfflineService.getNetworkState();
    
    Alert.alert(
      'Offline Status',
      `Connection: ${isOnline ? 'Online' : 'Offline'}\n` +
      `Pending requests: ${networkState.pendingCount}\n` +
      `Sync in progress: ${syncInProgress ? 'Yes' : 'No'}`,
      [
        { text: 'OK' },
        ...(isOnline && networkState.pendingCount > 0 ? [
          { text: 'Sync Now', onPress: syncOfflineData }
        ] : [])
      ]
    );
  };

  const value = {
    // State
    isOnline,
    pendingRequests,
    syncInProgress,
    offlineData,

    // Actions
    storeOfflineData,
    getOfflineData,
    queueRequest,
    syncOfflineData,
    handleOfflineAction,
    addToCartOffline,
    getOfflineCart,
    clearOfflineData,
    getStorageStats,
    preloadEssentialData,
    showOfflineStatus,

    // Utilities
    checkConnectionAndSync,
    loadOfflineData
  };

  return (
    <OfflineContext.Provider value={value}>
      {children}
    </OfflineContext.Provider>
  );
};

export default OfflineContext;
