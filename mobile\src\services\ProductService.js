import ApiService from './ApiService';

class ProductService {
  /**
   * Get paginated list of products
   */
  async getProducts(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key].toString());
        }
      });

      const response = await ApiService.get(`/products?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  /**
   * Get single product by ID
   */
  async getProduct(id) {
    try {
      const response = await ApiService.get(`/products/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  }

  /**
   * Search products
   */
  async searchProducts(query, limit = 10) {
    try {
      const params = new URLSearchParams({
        search: query,
        per_page: limit.toString()
      });

      const response = await ApiService.get(`/products/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching products:', error);
      throw error;
    }
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit = 10) {
    try {
      const params = new URLSearchParams({
        featured: 'true',
        per_page: limit.toString(),
        sort_by: 'created_at',
        sort_order: 'desc'
      });

      const response = await ApiService.get(`/products?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching featured products:', error);
      throw error;
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(categoryId, filters = {}) {
    try {
      const params = new URLSearchParams({
        category_id: categoryId.toString(),
        ...filters
      });

      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          params.append(key, params[key].toString());
        }
      });

      const response = await ApiService.get(`/products?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching products by category:', error);
      throw error;
    }
  }

  /**
   * Get related products
   */
  async getRelatedProducts(productId, limit = 5) {
    try {
      const response = await ApiService.get(`/products/${productId}/related?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching related products:', error);
      throw error;
    }
  }

  /**
   * Get product categories
   */
  async getCategories() {
    try {
      const response = await ApiService.get('/categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  /**
   * Get category tree
   */
  async getCategoryTree() {
    try {
      const response = await ApiService.get('/categories/tree');
      return response.data;
    } catch (error) {
      console.error('Error fetching category tree:', error);
      throw error;
    }
  }

  /**
   * Get product reviews
   */
  async getProductReviews(productId, page = 1, limit = 10) {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: limit.toString()
      });

      const response = await ApiService.get(`/products/${productId}/reviews?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product reviews:', error);
      throw error;
    }
  }

  /**
   * Add product to wishlist
   */
  async addToWishlist(productId) {
    try {
      const response = await ApiService.post('/wishlist', { product_id: productId });
      return response.data;
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      throw error;
    }
  }

  /**
   * Remove product from wishlist
   */
  async removeFromWishlist(productId) {
    try {
      const response = await ApiService.delete(`/wishlist/${productId}`);
      return response.data;
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      throw error;
    }
  }

  /**
   * Get user's wishlist
   */
  async getWishlist() {
    try {
      const response = await ApiService.get('/wishlist');
      return response.data;
    } catch (error) {
      console.error('Error fetching wishlist:', error);
      throw error;
    }
  }

  /**
   * Add product to cart
   */
  async addToCart(productId, quantity = 1, variationId = null) {
    try {
      const data = {
        product_id: productId,
        quantity: quantity
      };

      if (variationId) {
        data.product_variation_id = variationId;
      }

      const response = await ApiService.post('/cart/add', data);
      return response.data;
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  }

  /**
   * Get product variations
   */
  async getProductVariations(productId) {
    try {
      const response = await ApiService.get(`/products/${productId}/variations`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product variations:', error);
      throw error;
    }
  }

  /**
   * Check product availability
   */
  async checkAvailability(productId, quantity = 1, variationId = null) {
    try {
      const data = {
        product_id: productId,
        quantity: quantity
      };

      if (variationId) {
        data.product_variation_id = variationId;
      }

      const response = await ApiService.post('/products/check-availability', data);
      return response.data;
    } catch (error) {
      console.error('Error checking availability:', error);
      throw error;
    }
  }

  /**
   * Get product filters for category
   */
  async getProductFilters(categoryId = null) {
    try {
      const url = categoryId ? `/products/filters?category_id=${categoryId}` : '/products/filters';
      const response = await ApiService.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching product filters:', error);
      throw error;
    }
  }

  /**
   * Get recently viewed products
   */
  async getRecentlyViewed(limit = 10) {
    try {
      const response = await ApiService.get(`/products/recently-viewed?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching recently viewed products:', error);
      throw error;
    }
  }

  /**
   * Track product view
   */
  async trackProductView(productId) {
    try {
      const response = await ApiService.post(`/products/${productId}/view`);
      return response.data;
    } catch (error) {
      console.error('Error tracking product view:', error);
      // Don't throw error for tracking as it's not critical
      return null;
    }
  }

  /**
   * Get product recommendations
   */
  async getRecommendations(limit = 10) {
    try {
      const response = await ApiService.get(`/products/recommendations?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      throw error;
    }
  }

  /**
   * Get trending products
   */
  async getTrendingProducts(limit = 10) {
    try {
      const response = await ApiService.get(`/products/trending?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching trending products:', error);
      throw error;
    }
  }

  /**
   * Get new arrivals
   */
  async getNewArrivals(limit = 10) {
    try {
      const params = new URLSearchParams({
        per_page: limit.toString(),
        sort_by: 'created_at',
        sort_order: 'desc'
      });

      const response = await ApiService.get(`/products?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching new arrivals:', error);
      throw error;
    }
  }

  /**
   * Get sale products
   */
  async getSaleProducts(limit = 10) {
    try {
      const params = new URLSearchParams({
        on_sale: 'true',
        per_page: limit.toString(),
        sort_by: 'sale_percentage',
        sort_order: 'desc'
      });

      const response = await ApiService.get(`/products?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching sale products:', error);
      throw error;
    }
  }
}

export default new ProductService();
