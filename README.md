# E-Commerce Management Application

A comprehensive e-commerce management system with three main components:

## Project Structure

```
├── backend/                 # Laravel REST API
├── frontend/               # Angular Admin Dashboard
├── mobile/                 # React Native Mobile App
├── docs/                   # Documentation
├── docker-compose.yml      # Development environment
└── README.md              # This file
```

## Components

### 1. Backend (Laravel)
- **Location**: `./backend/`
- **Technology**: Laravel 10.x with JWT Authentication
- **Database**: MySQL
- **Features**: 
  - User Management with Role-based Access Control
  - Product Catalog with Categories and Variations
  - Order Management System
  - Coupon and Discount System
  - Transaction and Invoice Management
  - Media Management
  - Analytics Dashboard
  - Design Configuration

### 2. Frontend (Angular)
- **Location**: `./frontend/`
- **Technology**: Angular 17.x with Angular Material
- **Features**:
  - Responsive Admin Dashboard
  - User and Role Management Interface
  - Product and Category Management
  - Order Processing Interface
  - Analytics and Reporting
  - Design Configuration Panel

### 3. Mobile App (React Native)
- **Location**: `./mobile/`
- **Technology**: React Native with Expo
- **Platforms**: iOS and Android
- **Features**:
  - User Authentication
  - Product Browsing and Search
  - Order Placement and Tracking
  - User Profile Management
  - Offline Support

## Development Setup

### Prerequisites
- Node.js 18.x or higher
- PHP 8.1 or higher
- Composer
- MySQL 8.0 or higher
- Docker (optional)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd amit
   ```

2. **Run Setup Script**
   ```bash
   # Windows PowerShell
   .\setup.ps1

   # Or manual setup:
   ```

3. **Backend Setup**
   ```bash
   cd backend
   composer install
   cp .env.example .env
   # Edit .env file with your database credentials
   php artisan key:generate
   php artisan jwt:secret
   php artisan migrate --seed
   php artisan serve
   ```

4. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ng serve
   ```

5. **Mobile App Setup**
   ```bash
   cd mobile
   npm install
   npx expo start
   ```

### Using Docker

For a complete development environment with all services:

```bash
docker-compose up -d
```

This will start:
- MySQL database on port 3306
- Redis cache on port 6379
- Laravel backend on port 8000
- Angular frontend on port 4200
- Nginx reverse proxy on port 80

## API Documentation
- Base URL: `http://localhost:8000/api`
- Documentation: Available at `/docs` endpoint
- Authentication: JWT Bearer Token

## Security Features
- JWT Authentication
- Role-based Authorization
- Input Validation and Sanitization
- CORS Protection
- Rate Limiting
- SQL Injection Prevention

## Performance Targets
- Page Load Time: < 1 second
- API Response Time: < 200ms
- Mobile App Launch: < 3 seconds

## Testing
- Backend: PHPUnit tests
- Frontend: Jasmine/Karma tests
- Mobile: Jest tests
- E2E: Cypress tests

## Deployment
- Backend: Laravel deployment guide
- Frontend: Angular build and deployment
- Mobile: App Store and Play Store deployment

## License
Proprietary - All rights reserved

## Credits
Web Design and developed By RekTech
