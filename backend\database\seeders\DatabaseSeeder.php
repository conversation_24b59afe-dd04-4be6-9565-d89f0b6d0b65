<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting database seeding...');

        // Seed roles and permissions first (creates users)
        $this->call([
            RolesAndPermissionsSeeder::class,
        ]);

        // Seed user groups (requires users to exist)
        $this->call([
            UserGroupsSeeder::class,
        ]);

        // Seed settings
        $this->call([
            SettingsSeeder::class,
        ]);

        // Seed categories (for products)
        $this->call([
            CategoriesSeeder::class,
        ]);

        // Seed products
        $this->call([
            ProductsSeeder::class,
        ]);

        // Seed sample orders
        $this->call([
            OrdersSeeder::class,
        ]);

        // Seed coupons
        $this->call([
            CouponsSeeder::class,
        ]);

        // Seed sample media files
        $this->call([
            MediaSeeder::class,
        ]);

        // Seed module permissions
        $this->call([
            ModulePermissionsSeeder::class,
        ]);

        $this->command->info('🎉 Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('📋 Default Login Credentials:');
        $this->command->info('Super Admin: <EMAIL> / admin123');
        $this->command->info('Admin: <EMAIL> / password123');
        $this->command->info('Editor: <EMAIL> / password123');
        $this->command->info('');
        $this->command->info('🔗 Access URLs:');
        $this->command->info('API: http://localhost:8000/api');
        $this->command->info('Admin Dashboard: http://localhost:4200');
        $this->command->info('');
        $this->command->info('📚 Documentation:');
        $this->command->info('API Docs: ./docs/API_DOCUMENTATION.md');
        $this->command->info('User Manual: ./docs/USER_MANUAL.md');
        $this->command->info('Deployment Guide: ./docs/DEPLOYMENT_GUIDE.md');
    }
}
