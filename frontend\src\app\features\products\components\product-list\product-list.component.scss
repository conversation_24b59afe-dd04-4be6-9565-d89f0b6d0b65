.product-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    h1 {
      margin: 0 0 8px 0;
      color: #333;
      font-weight: 600;
      font-size: 28px;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 16px;
    }
  }
  
  .header-actions {
    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

.filters-card {
  margin-bottom: 24px;
  
  .filters-container {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto auto;
    gap: 16px;
    align-items: center;
    
    .search-field {
      min-width: 300px;
    }
    
    .featured-filter {
      margin-left: 8px;
    }
    
    .clear-filters-btn {
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

.bulk-actions {
  margin-bottom: 24px;
  
  .bulk-actions-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .selection-count {
      font-weight: 500;
      color: #333;
    }
    
    .bulk-action-buttons {
      display: flex;
      gap: 12px;
      
      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

.table-card {
  .table-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .table-info {
      font-size: 14px;
      color: #666;
    }
    
    .table-action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 24px;
    
    mat-spinner {
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 16px;
    }
  }
  
  .table-container {
    .products-table {
      width: 100%;
      
      .mat-column-select {
        width: 60px;
      }
      
      .mat-column-image {
        width: 80px;
      }
      
      .mat-column-name {
        min-width: 200px;
      }
      
      .mat-column-sku {
        width: 120px;
      }
      
      .mat-column-category {
        width: 150px;
      }
      
      .mat-column-price {
        width: 150px;
      }
      
      .mat-column-stock {
        width: 120px;
      }
      
      .mat-column-status {
        width: 100px;
      }
      
      .mat-column-featured {
        width: 80px;
        text-align: center;
      }
      
      .mat-column-created_at {
        width: 140px;
      }
      
      .mat-column-actions {
        width: 180px;
      }
      
      .product-image {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .product-name {
        .name {
          display: block;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }
        
        .sku {
          display: block;
          font-size: 12px;
          color: #999;
        }
      }
      
      .price-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        .current-price {
          font-weight: 600;
          color: #333;
        }
        
        .original-price {
          font-size: 12px;
          color: #999;
          text-decoration: line-through;
        }
        
        .sale-chip {
          font-size: 10px;
          height: 20px;
          background-color: #ff5722;
          color: white;
        }
      }
      
      .stock-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        .stock-quantity {
          font-weight: 500;
          color: #333;
        }
        
        .stock-status {
          font-size: 11px;
          height: 20px;
        }
      }
      
      .inactive-icon {
        color: #ccc;
      }
      
      .action-buttons {
        display: flex;
        gap: 4px;
      }
    }
    
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 64px 24px;
      text-align: center;
      
      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #ccc;
        margin-bottom: 16px;
      }
      
      h3 {
        margin: 0 0 8px 0;
        color: #333;
        font-weight: 500;
      }
      
      p {
        margin: 0 0 24px 0;
        color: #666;
        font-size: 14px;
      }
      
      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .filters-container {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
    
    .search-field {
      grid-column: 1 / -1;
      min-width: auto;
    }
    
    .clear-filters-btn {
      grid-column: 1 / -1;
      justify-self: start;
    }
  }
}

@media (max-width: 768px) {
  .product-list-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filters-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .bulk-actions-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    
    .bulk-action-buttons {
      flex-wrap: wrap;
    }
  }
  
  .table-actions {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  // Hide some columns on mobile
  .products-table {
    .mat-column-sku,
    .mat-column-category,
    .mat-column-created_at {
      display: none;
    }
  }
}

@media (max-width: 480px) {
  .products-table {
    .mat-column-featured,
    .mat-column-stock {
      display: none;
    }
    
    .action-buttons {
      flex-direction: column;
      gap: 2px;
    }
  }
}

// Material theme customizations
::ng-deep {
  .mat-chip.sale-chip {
    background-color: #ff5722 !important;
    color: white !important;
  }
  
  .mat-chip.stock-status {
    font-size: 11px !important;
    min-height: 20px !important;
    line-height: 20px !important;
  }
  
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }
  
  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
  
  .warning-snackbar {
    background-color: #ff9800 !important;
    color: white !important;
  }
  
  .info-snackbar {
    background-color: #2196f3 !important;
    color: white !important;
  }
}
