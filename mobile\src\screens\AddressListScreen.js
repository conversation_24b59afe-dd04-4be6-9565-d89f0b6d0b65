import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AddressService from '../services/AddressService';
import { colors, typography, spacing } from '../styles/theme';
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';
import AddressCard from '../components/AddressCard';

const AddressListScreen = ({ navigation }) => {
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  useFocusEffect(
    useCallback(() => {
      loadAddresses();
    }, [])
  );

  /**
   * Load all addresses
   */
  const loadAddresses = async () => {
    try {
      setError(null);
      const response = await AddressService.getAddresses();
      if (response.success) {
        setAddresses(response.data);
      } else {
        setError('Failed to load addresses');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  /**
   * Handle refresh
   */
  const onRefresh = () => {
    setRefreshing(true);
    loadAddresses();
  };

  /**
   * Navigate to add address screen
   */
  const navigateToAddAddress = () => {
    navigation.navigate('AddressForm', { 
      title: 'Add Address',
      onSave: loadAddresses 
    });
  };

  /**
   * Navigate to edit address screen
   */
  const navigateToEditAddress = (address) => {
    navigation.navigate('AddressForm', { 
      address,
      title: 'Edit Address',
      onSave: loadAddresses 
    });
  };

  /**
   * Set address as default
   */
  const setAsDefault = async (address) => {
    if (address.is_default) return;

    try {
      const response = await AddressService.setAsDefault(address.id);
      if (response.success) {
        loadAddresses();
        Alert.alert('Success', 'Default address updated');
      } else {
        Alert.alert('Error', 'Failed to update default address');
      }
    } catch (err) {
      Alert.alert('Error', err.message);
    }
  };

  /**
   * Delete address
   */
  const deleteAddress = (address) => {
    Alert.alert(
      'Delete Address',
      `Are you sure you want to delete the address for ${AddressService.getFullName(address)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await AddressService.deleteAddress(address.id);
              if (response.success) {
                loadAddresses();
                Alert.alert('Success', 'Address deleted successfully');
              } else {
                Alert.alert('Error', 'Failed to delete address');
              }
            } catch (err) {
              Alert.alert('Error', err.message);
            }
          },
        },
      ]
    );
  };

  /**
   * Check if address can be deleted
   */
  const canDelete = (address) => {
    if (address.is_default) {
      const sameTypeAddresses = addresses.filter(a => 
        a.type === address.type || a.type === 'both' || address.type === 'both'
      );
      return sameTypeAddresses.length > 1;
    }
    return true;
  };

  /**
   * Get addresses by type
   */
  const getAddressesByType = (type) => {
    return addresses.filter(address => 
      address.type === type || address.type === 'both'
    );
  };

  /**
   * Render address item
   */
  const renderAddressItem = ({ item }) => (
    <AddressCard
      address={item}
      onEdit={() => navigateToEditAddress(item)}
      onSetDefault={() => setAsDefault(item)}
      onDelete={() => deleteAddress(item)}
      canDelete={canDelete(item)}
    />
  );

  /**
   * Render section header
   */
  const renderSectionHeader = (title, count) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <Text style={styles.sectionCount}>({count})</Text>
    </View>
  );

  /**
   * Render empty state
   */
  const renderEmptyState = () => (
    <EmptyState
      icon="location-off"
      title="No Addresses Found"
      message="You haven't added any addresses yet. Add your first address to get started."
      actionText="Add Address"
      onAction={navigateToAddAddress}
    />
  );

  /**
   * Render error state
   */
  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Icon name="error-outline" size={64} color={colors.error} />
      <Text style={styles.errorTitle}>Error Loading Addresses</Text>
      <Text style={styles.errorMessage}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={loadAddresses}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return <LoadingSpinner message="Loading addresses..." />;
  }

  if (error && addresses.length === 0) {
    return renderErrorState();
  }

  const shippingAddresses = getAddressesByType('shipping');
  const billingAddresses = getAddressesByType('billing');

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Addresses</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={navigateToAddAddress}
        >
          <Icon name="add" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {addresses.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={addresses}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderAddressItem}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
            />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={() => (
            <View>
              {shippingAddresses.length > 0 && (
                <View style={styles.section}>
                  {renderSectionHeader('Shipping Addresses', shippingAddresses.length)}
                </View>
              )}
            </View>
          )}
        />
      )}

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={navigateToAddAddress}
        activeOpacity={0.8}
      >
        <Icon name="add" size={24} color={colors.white} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    ...typography.h2,
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    padding: spacing.xs,
  },
  listContainer: {
    padding: spacing.md,
    paddingBottom: 80, // Space for FAB
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    paddingBottom: spacing.xs,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.text,
  },
  sectionCount: {
    ...typography.caption,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  section: {
    marginBottom: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  errorTitle: {
    ...typography.h2,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  errorMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },
  retryButtonText: {
    ...typography.button,
    color: colors.white,
  },
  fab: {
    position: 'absolute',
    bottom: spacing.lg,
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default AddressListScreen;
