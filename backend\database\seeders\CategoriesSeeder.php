<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\User;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the super admin user to set as creator
        $superAdmin = User::where('email', '<EMAIL>')->first();

        // Create main categories
        $categories = [
            [
                'name' => 'Electronics',
                'slug' => 'electronics',
                'description' => 'Electronic devices and gadgets',
                'icon' => 'devices',
                'is_featured' => true,
                'sort_order' => 1,
                'children' => [
                    [
                        'name' => 'Smartphones',
                        'slug' => 'smartphones',
                        'description' => 'Mobile phones and accessories',
                        'icon' => 'smartphone',
                        'sort_order' => 1,
                        'children' => [
                            ['name' => 'iPhone', 'slug' => 'iphone', 'description' => 'Apple iPhone series'],
                            ['name' => 'Samsung Galaxy', 'slug' => 'samsung-galaxy', 'description' => 'Samsung Galaxy series'],
                            ['name' => 'Google Pixel', 'slug' => 'google-pixel', 'description' => 'Google Pixel phones'],
                        ]
                    ],
                    [
                        'name' => 'Laptops',
                        'slug' => 'laptops',
                        'description' => 'Portable computers and notebooks',
                        'icon' => 'laptop',
                        'sort_order' => 2,
                        'children' => [
                            ['name' => 'Gaming Laptops', 'slug' => 'gaming-laptops', 'description' => 'High-performance gaming laptops'],
                            ['name' => 'Business Laptops', 'slug' => 'business-laptops', 'description' => 'Professional business laptops'],
                            ['name' => 'Ultrabooks', 'slug' => 'ultrabooks', 'description' => 'Thin and light laptops'],
                        ]
                    ],
                    [
                        'name' => 'Tablets',
                        'slug' => 'tablets',
                        'description' => 'Tablet computers and e-readers',
                        'icon' => 'tablet',
                        'sort_order' => 3,
                    ],
                    [
                        'name' => 'Audio',
                        'slug' => 'audio',
                        'description' => 'Headphones, speakers, and audio equipment',
                        'icon' => 'headphones',
                        'sort_order' => 4,
                        'children' => [
                            ['name' => 'Headphones', 'slug' => 'headphones', 'description' => 'Over-ear and on-ear headphones'],
                            ['name' => 'Earbuds', 'slug' => 'earbuds', 'description' => 'In-ear wireless earbuds'],
                            ['name' => 'Speakers', 'slug' => 'speakers', 'description' => 'Bluetooth and wired speakers'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Clothing',
                'slug' => 'clothing',
                'description' => 'Fashion and apparel for all occasions',
                'icon' => 'checkroom',
                'is_featured' => true,
                'sort_order' => 2,
                'children' => [
                    [
                        'name' => 'Men\'s Clothing',
                        'slug' => 'mens-clothing',
                        'description' => 'Fashion for men',
                        'sort_order' => 1,
                        'children' => [
                            ['name' => 'Shirts', 'slug' => 'mens-shirts', 'description' => 'Casual and formal shirts'],
                            ['name' => 'Pants', 'slug' => 'mens-pants', 'description' => 'Jeans, chinos, and dress pants'],
                            ['name' => 'Jackets', 'slug' => 'mens-jackets', 'description' => 'Coats, blazers, and outerwear'],
                        ]
                    ],
                    [
                        'name' => 'Women\'s Clothing',
                        'slug' => 'womens-clothing',
                        'description' => 'Fashion for women',
                        'sort_order' => 2,
                        'children' => [
                            ['name' => 'Dresses', 'slug' => 'womens-dresses', 'description' => 'Casual and formal dresses'],
                            ['name' => 'Tops', 'slug' => 'womens-tops', 'description' => 'Blouses, t-shirts, and tops'],
                            ['name' => 'Bottoms', 'slug' => 'womens-bottoms', 'description' => 'Skirts, pants, and shorts'],
                        ]
                    ],
                    [
                        'name' => 'Shoes',
                        'slug' => 'shoes',
                        'description' => 'Footwear for all occasions',
                        'icon' => 'footprint',
                        'sort_order' => 3,
                        'children' => [
                            ['name' => 'Sneakers', 'slug' => 'sneakers', 'description' => 'Athletic and casual sneakers'],
                            ['name' => 'Dress Shoes', 'slug' => 'dress-shoes', 'description' => 'Formal and business shoes'],
                            ['name' => 'Boots', 'slug' => 'boots', 'description' => 'Ankle boots, hiking boots, and more'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Home & Garden',
                'slug' => 'home-garden',
                'description' => 'Home improvement and garden supplies',
                'icon' => 'home',
                'is_featured' => false,
                'sort_order' => 3,
                'children' => [
                    [
                        'name' => 'Furniture',
                        'slug' => 'furniture',
                        'description' => 'Indoor and outdoor furniture',
                        'sort_order' => 1,
                        'children' => [
                            ['name' => 'Living Room', 'slug' => 'living-room-furniture', 'description' => 'Sofas, chairs, and tables'],
                            ['name' => 'Bedroom', 'slug' => 'bedroom-furniture', 'description' => 'Beds, dressers, and nightstands'],
                            ['name' => 'Office', 'slug' => 'office-furniture', 'description' => 'Desks, chairs, and storage'],
                        ]
                    ],
                    [
                        'name' => 'Kitchen',
                        'slug' => 'kitchen',
                        'description' => 'Kitchen appliances and accessories',
                        'sort_order' => 2,
                        'children' => [
                            ['name' => 'Small Appliances', 'slug' => 'small-appliances', 'description' => 'Blenders, toasters, and more'],
                            ['name' => 'Cookware', 'slug' => 'cookware', 'description' => 'Pots, pans, and cooking tools'],
                            ['name' => 'Dinnerware', 'slug' => 'dinnerware', 'description' => 'Plates, bowls, and utensils'],
                        ]
                    ],
                    [
                        'name' => 'Garden',
                        'slug' => 'garden',
                        'description' => 'Gardening tools and outdoor equipment',
                        'sort_order' => 3,
                        'children' => [
                            ['name' => 'Tools', 'slug' => 'garden-tools', 'description' => 'Shovels, rakes, and hand tools'],
                            ['name' => 'Plants', 'slug' => 'plants', 'description' => 'Seeds, seedlings, and potted plants'],
                            ['name' => 'Outdoor Decor', 'slug' => 'outdoor-decor', 'description' => 'Garden ornaments and lighting'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Sports & Outdoors',
                'slug' => 'sports-outdoors',
                'description' => 'Sports equipment and outdoor gear',
                'icon' => 'sports_soccer',
                'is_featured' => false,
                'sort_order' => 4,
                'children' => [
                    [
                        'name' => 'Fitness',
                        'slug' => 'fitness',
                        'description' => 'Exercise equipment and accessories',
                        'sort_order' => 1,
                        'children' => [
                            ['name' => 'Cardio Equipment', 'slug' => 'cardio-equipment', 'description' => 'Treadmills, bikes, and ellipticals'],
                            ['name' => 'Strength Training', 'slug' => 'strength-training', 'description' => 'Weights and resistance equipment'],
                            ['name' => 'Yoga & Pilates', 'slug' => 'yoga-pilates', 'description' => 'Mats, blocks, and accessories'],
                        ]
                    ],
                    [
                        'name' => 'Outdoor Recreation',
                        'slug' => 'outdoor-recreation',
                        'description' => 'Camping, hiking, and outdoor activities',
                        'sort_order' => 2,
                        'children' => [
                            ['name' => 'Camping', 'slug' => 'camping', 'description' => 'Tents, sleeping bags, and gear'],
                            ['name' => 'Hiking', 'slug' => 'hiking', 'description' => 'Backpacks, boots, and accessories'],
                            ['name' => 'Water Sports', 'slug' => 'water-sports', 'description' => 'Kayaks, surfboards, and gear'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Books & Media',
                'slug' => 'books-media',
                'description' => 'Books, movies, music, and digital media',
                'icon' => 'book',
                'is_featured' => false,
                'sort_order' => 5,
                'children' => [
                    [
                        'name' => 'Books',
                        'slug' => 'books',
                        'description' => 'Physical and digital books',
                        'sort_order' => 1,
                        'children' => [
                            ['name' => 'Fiction', 'slug' => 'fiction-books', 'description' => 'Novels and short stories'],
                            ['name' => 'Non-Fiction', 'slug' => 'non-fiction-books', 'description' => 'Educational and reference books'],
                            ['name' => 'Children\'s Books', 'slug' => 'childrens-books', 'description' => 'Books for kids and teens'],
                        ]
                    ],
                    [
                        'name' => 'Movies & TV',
                        'slug' => 'movies-tv',
                        'description' => 'DVDs, Blu-rays, and digital content',
                        'sort_order' => 2,
                    ],
                    [
                        'name' => 'Music',
                        'slug' => 'music',
                        'description' => 'CDs, vinyl, and digital music',
                        'sort_order' => 3,
                    ],
                ]
            ],
        ];

        $this->createCategoriesRecursively($categories, null, $superAdmin->id);

        $this->command->info('Categories seeded successfully!');
        $this->command->info('Created hierarchical category structure with subcategories');
    }

    /**
     * Create categories recursively.
     */
    private function createCategoriesRecursively(array $categories, ?int $parentId, int $createdBy): void
    {
        foreach ($categories as $categoryData) {
            $children = $categoryData['children'] ?? [];
            unset($categoryData['children']);

            $category = Category::create([
                'name' => $categoryData['name'],
                'slug' => $categoryData['slug'],
                'description' => $categoryData['description'] ?? null,
                'parent_id' => $parentId,
                'icon' => $categoryData['icon'] ?? null,
                'is_active' => true,
                'is_featured' => $categoryData['is_featured'] ?? false,
                'sort_order' => $categoryData['sort_order'] ?? 0,
                'created_by' => $createdBy,
            ]);

            // Create children recursively
            if (!empty($children)) {
                $this->createCategoriesRecursively($children, $category->id, $createdBy);
            }
        }
    }
}
