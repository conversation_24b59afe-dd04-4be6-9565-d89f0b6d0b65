<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_id')->unique();
            $table->foreignId('order_id')->nullable()->constrained('orders')->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->enum('type', ['payment', 'refund', 'partial_refund', 'chargeback', 'adjustment'])->default('payment');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded'])->default('pending');
            $table->enum('payment_method', ['cod', 'razorpay', 'gpay', 'bank_transfer', 'wallet', 'upi'])->default('cod');
            $table->enum('gateway', ['razorpay', 'stripe', 'paypal', 'manual'])->default('manual');
            $table->string('gateway_transaction_id')->nullable(); // Gateway's transaction ID
            $table->decimal('amount', 10, 2); // Transaction amount
            $table->string('currency', 3)->default('USD');
            $table->decimal('fee', 10, 2)->nullable()->default(0); // Gateway fee
            $table->decimal('net_amount', 10, 2); // Amount after deducting fees
            $table->string('reference')->nullable(); // Reference to original transaction (for refunds)
            $table->text('description')->nullable();
            $table->json('gateway_response')->nullable(); // Store gateway response data
            $table->text('failure_reason')->nullable(); // Reason for failure
            $table->timestamp('processed_at')->nullable(); // When transaction was processed
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['transaction_id', 'status']);
            $table->index(['order_id', 'type']);
            $table->index(['user_id', 'status']);
            $table->index(['status', 'type']);
            $table->index(['payment_method', 'status']);
            $table->index(['gateway', 'status']);
            $table->index(['created_at', 'status']);
            $table->index('gateway_transaction_id');
            $table->index('reference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
