<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\CouponController;
use App\Http\Controllers\Api\TransactionController;
use App\Http\Controllers\Api\InvoiceController;
use App\Http\Controllers\Api\MediaController;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\AnalyticsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/auth/register', [AuthController::class, 'register']);
Route::post('/auth/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/auth/reset-password', [AuthController::class, 'resetPassword']);

// Public product routes (for mobile app)
Route::get('/products', [ProductController::class, 'index']);
Route::get('/products/{id}', [ProductController::class, 'show']);
Route::get('/categories', [CategoryController::class, 'index']);
Route::get('/categories/{id}/products', [CategoryController::class, 'products']);

// Protected routes
Route::middleware(['auth:api'])->group(function () {
    // Authentication
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::post('/auth/refresh', [AuthController::class, 'refresh']);
    Route::get('/auth/me', [AuthController::class, 'me']);
    
    // User Management
    Route::apiResource('users', UserController::class);
    Route::post('/users/{id}/assign-role', [UserController::class, 'assignRole']);
    Route::post('/users/{id}/assign-group', [UserController::class, 'assignGroup']);
    Route::get('/user-groups', [UserController::class, 'groups']);
    Route::post('/user-groups', [UserController::class, 'createGroup']);
    
    // Product Management
    Route::apiResource('products', ProductController::class)->except(['index', 'show']);
    Route::post('/products/{id}/duplicate', [ProductController::class, 'duplicate']);
    Route::post('/products/bulk-action', [ProductController::class, 'bulkAction']);
    Route::post('/products/{id}/variations', [ProductController::class, 'createVariations']);
    
    // Category Management
    Route::apiResource('categories', CategoryController::class)->except(['index']);
    Route::post('/categories/{id}/reorder', [CategoryController::class, 'reorder']);
    
    // Order Management
    Route::apiResource('orders', OrderController::class);
    Route::post('/orders/{id}/status', [OrderController::class, 'updateStatus']);
    Route::post('/orders/{id}/communicate', [OrderController::class, 'communicate']);
    
    // Coupon Management
    Route::apiResource('coupons', CouponController::class);
    Route::post('/coupons/{id}/toggle', [CouponController::class, 'toggle']);
    
    // Transaction Management
    Route::apiResource('transactions', TransactionController::class);
    Route::get('/transactions/summary', [TransactionController::class, 'summary']);
    
    // Invoice Management
    Route::apiResource('invoices', InvoiceController::class);
    Route::post('/invoices/{id}/convert', [InvoiceController::class, 'convertToFinal']);
    Route::get('/invoices/{id}/pdf', [InvoiceController::class, 'generatePdf']);
    
    // Media Management
    Route::post('/media/upload', [MediaController::class, 'upload']);
    Route::get('/media', [MediaController::class, 'index']);
    Route::delete('/media/{id}', [MediaController::class, 'destroy']);
    Route::post('/media/bulk-delete', [MediaController::class, 'bulkDelete']);
    
    // Settings Management
    Route::get('/settings', [SettingsController::class, 'index']);
    Route::post('/settings', [SettingsController::class, 'update']);
    Route::get('/settings/{group}', [SettingsController::class, 'getGroup']);
    Route::post('/settings/{group}', [SettingsController::class, 'updateGroup']);
    
    // Analytics
    Route::get('/analytics/dashboard', [AnalyticsController::class, 'dashboard']);
    Route::get('/analytics/users', [AnalyticsController::class, 'userAnalytics']);
    Route::get('/analytics/products', [AnalyticsController::class, 'productAnalytics']);
    Route::get('/analytics/orders', [AnalyticsController::class, 'orderAnalytics']);
    Route::get('/analytics/revenue', [AnalyticsController::class, 'revenueAnalytics']);

    // User Addresses
    Route::apiResource('user/addresses', UserAddressController::class);
    Route::post('/user/addresses/{address}/set-default', [UserAddressController::class, 'setDefault']);
    Route::get('/user/addresses/defaults', [UserAddressController::class, 'defaults']);
    Route::get('/user/addresses/type/{type}', [UserAddressController::class, 'byType']);

    // User Profile
    Route::get('/user/profile', [UserProfileController::class, 'show']);
    Route::put('/user/profile', [UserProfileController::class, 'update']);
    Route::post('/user/profile/image', [UserProfileController::class, 'uploadImage']);
    Route::delete('/user/profile/image', [UserProfileController::class, 'deleteImage']);

    // Product Reviews
    Route::get('/products/{product}/reviews', [ProductReviewController::class, 'index']);
    Route::post('/products/{product}/reviews', [ProductReviewController::class, 'store']);
    Route::get('/products/{product}/reviews/{review}', [ProductReviewController::class, 'show']);
    Route::put('/products/{product}/reviews/{review}', [ProductReviewController::class, 'update']);
    Route::delete('/products/{product}/reviews/{review}', [ProductReviewController::class, 'destroy']);
    Route::post('/products/{product}/reviews/{review}/helpful', [ProductReviewController::class, 'markHelpful']);
    Route::post('/products/{product}/reviews/{review}/not-helpful', [ProductReviewController::class, 'markNotHelpful']);
    Route::get('/products/{product}/reviews/statistics', [ProductReviewController::class, 'statistics']);

    // Wishlist
    Route::apiResource('wishlists', WishlistController::class);
    Route::post('/wishlists/{wishlist}/items', [WishlistController::class, 'addItem']);
    Route::delete('/wishlists/{wishlist}/items/{item}', [WishlistController::class, 'removeItem']);
    Route::post('/wishlists/{wishlist}/items/{item}/move-to-cart', [WishlistController::class, 'moveToCart']);

    // Shopping Cart
    Route::get('/cart', [ShoppingCartController::class, 'show']);
    Route::post('/cart/add', [ShoppingCartController::class, 'addItem']);
    Route::put('/cart/items/{item}', [ShoppingCartController::class, 'updateItem']);
    Route::delete('/cart/items/{item}', [ShoppingCartController::class, 'removeItem']);
    Route::delete('/cart/clear', [ShoppingCartController::class, 'clear']);
    Route::post('/cart/apply-coupon', [ShoppingCartController::class, 'applyCoupon']);
    Route::delete('/cart/remove-coupon', [ShoppingCartController::class, 'removeCoupon']);

    // Notifications
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead']);
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead']);
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy']);

    // Design Settings
    Route::get('/design-settings', [DesignSettingsController::class, 'index']);
    Route::put('/design-settings', [DesignSettingsController::class, 'update']);
    Route::post('/design-settings/upload-logo', [DesignSettingsController::class, 'uploadLogo']);
    Route::delete('/design-settings/delete-logo', [DesignSettingsController::class, 'deleteLogo']);
    Route::post('/design-settings/apply-preset', [DesignSettingsController::class, 'applyColorPreset']);
    Route::post('/design-settings/reset', [DesignSettingsController::class, 'resetToDefaults']);

    // Media Library
    Route::get('/media', [MediaLibraryController::class, 'index']);
    Route::post('/media/upload', [MediaLibraryController::class, 'upload']);
    Route::get('/media/{media}', [MediaLibraryController::class, 'show']);
    Route::put('/media/{media}', [MediaLibraryController::class, 'update']);
    Route::delete('/media/{media}', [MediaLibraryController::class, 'destroy']);
    Route::delete('/media/bulk-delete', [MediaLibraryController::class, 'bulkDelete']);
    Route::get('/media/folders/list', [MediaLibraryController::class, 'getFolders']);
    Route::get('/media/collections/list', [MediaLibraryController::class, 'getCollections']);

    // Module Permissions
    Route::get('/permissions/modules', [PermissionController::class, 'getModules']);
    Route::get('/permissions/pages/{module}', [PermissionController::class, 'getPages']);
    Route::apiResource('permissions', PermissionController::class);
    Route::post('/permissions/assign-to-user', [PermissionController::class, 'assignToUser']);
    Route::post('/permissions/assign-to-group', [PermissionController::class, 'assignToGroup']);
    Route::get('/permissions/user/{user}', [PermissionController::class, 'getUserPermissions']);
    Route::get('/permissions/group/{group}', [PermissionController::class, 'getGroupPermissions']);
});

// Admin only routes
Route::middleware(['auth:api', 'role:super-admin'])->group(function () {
    Route::post('/system/modules/toggle', [SettingsController::class, 'toggleModule']);
    Route::get('/system/logs', [SettingsController::class, 'getLogs']);
    Route::post('/system/cache/clear', [SettingsController::class, 'clearCache']);
});
