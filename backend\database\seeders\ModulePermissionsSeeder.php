<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ModulePermission;

class ModulePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔐 Seeding module permissions...');

        // Define all modules and their pages
        $modules = [
            'users' => [
                'list' => 'List Users',
                'view' => 'View User Details',
                'create' => 'Create User',
                'edit' => 'Edit User',
                'delete' => 'Delete User',
                'profile' => 'User Profile',
                'permissions' => 'User Permissions',
                'groups' => 'User Groups',
                'activity' => 'Activity Log',
            ],
            'products' => [
                'list' => 'List Products',
                'view' => 'View Product Details',
                'create' => 'Create Product',
                'edit' => 'Edit Product',
                'delete' => 'Delete Product',
                'variations' => 'Product Variations',
                'attributes' => 'Product Attributes',
                'inventory' => 'Inventory Management',
                'duplicate' => 'Duplicate Product',
            ],
            'categories' => [
                'list' => 'List Categories',
                'view' => 'View Category Details',
                'create' => 'Create Category',
                'edit' => 'Edit Category',
                'delete' => 'Delete Category',
                'reorder' => 'Reorder Categories',
            ],
            'orders' => [
                'list' => 'List Orders',
                'view' => 'View Order Details',
                'create' => 'Create Order',
                'edit' => 'Edit Order',
                'delete' => 'Delete Order',
                'status' => 'Update Status',
                'contact' => 'Contact Customer',
                'tracking' => 'Tracking Information',
                'refund' => 'Process Refund',
            ],
            'invoices' => [
                'list' => 'List Invoices',
                'view' => 'View Invoice Details',
                'create' => 'Create Invoice',
                'edit' => 'Edit Invoice',
                'delete' => 'Delete Invoice',
                'convert' => 'Convert Invoice',
                'pdf' => 'Generate PDF',
            ],
            'coupons' => [
                'list' => 'List Coupons',
                'view' => 'View Coupon Details',
                'create' => 'Create Coupon',
                'edit' => 'Edit Coupon',
                'delete' => 'Delete Coupon',
                'toggle' => 'Toggle Coupon Status',
            ],
            'transactions' => [
                'list' => 'List Transactions',
                'view' => 'View Transaction Details',
                'create' => 'Create Transaction',
                'edit' => 'Edit Transaction',
                'delete' => 'Delete Transaction',
                'summary' => 'View Summary',
            ],
            'media' => [
                'list' => 'List Media Files',
                'view' => 'View Media Details',
                'upload' => 'Upload Files',
                'organize' => 'Organize Files',
                'delete' => 'Delete Files',
                'bulk' => 'Bulk Operations',
            ],
            'settings' => [
                'view' => 'View Settings',
                'edit' => 'Edit Settings',
                'general' => 'General Settings',
                'payment' => 'Payment Settings',
                'shipping' => 'Shipping Settings',
                'email' => 'Email Settings',
            ],
            'design' => [
                'view' => 'View Design Settings',
                'edit' => 'Edit Design Settings',
                'theme' => 'Theme Settings',
                'colors' => 'Color Settings',
                'typography' => 'Typography',
                'layout' => 'Layout Settings',
                'logos' => 'Logo Management',
            ],
            'analytics' => [
                'view' => 'View Analytics',
                'reports' => 'Generate Reports',
                'export' => 'Export Data',
            ],
            'permissions' => [
                'list' => 'List Permissions',
                'view' => 'View Permission Details',
                'create' => 'Create Permission',
                'edit' => 'Edit Permission',
                'delete' => 'Delete Permission',
                'assign' => 'Assign Permissions',
            ],
        ];

        // Define actions for each page
        $actions = ['view', 'create', 'edit', 'delete'];

        $sortOrder = 1;

        foreach ($modules as $moduleName => $pages) {
            $this->command->info("  📁 Creating permissions for {$moduleName} module...");

            foreach ($pages as $pageKey => $pageName) {
                foreach ($actions as $action) {
                    // Skip actions that don't make sense for certain pages
                    if ($this->shouldSkipAction($action, $pageKey)) {
                        continue;
                    }

                    $permissionName = "{$action}_{$moduleName}_{$pageKey}";
                    $description = "Can {$action} {$pageName} in {$moduleName} module";
                    
                    // Determine permission group based on module and action
                    $permissionGroup = $this->getPermissionGroup($moduleName, $action);

                    ModulePermission::updateOrCreate([
                        'module_name' => $moduleName,
                        'page_name' => $pageKey,
                        'permission_name' => $permissionName,
                    ], [
                        'permission_group' => $permissionGroup,
                        'description' => $description,
                        'is_active' => true,
                        'sort_order' => $sortOrder++,
                    ]);
                }
            }
        }

        $this->command->info('✅ Module permissions seeded successfully!');
    }

    /**
     * Determine if an action should be skipped for a specific page.
     */
    private function shouldSkipAction(string $action, string $page): bool
    {
        $skipRules = [
            'create' => ['list', 'view', 'summary', 'reports', 'analytics'],
            'edit' => ['list', 'create', 'summary', 'reports', 'analytics'],
            'delete' => ['list', 'view', 'create', 'summary', 'reports', 'analytics', 'export'],
        ];

        return in_array($page, $skipRules[$action] ?? []);
    }

    /**
     * Get permission group based on module and action.
     */
    private function getPermissionGroup(string $module, string $action): string
    {
        // Super admin only modules
        $superAdminModules = ['permissions', 'settings', 'design'];
        if (in_array($module, $superAdminModules)) {
            return 'super_admin';
        }

        // Admin modules
        $adminModules = ['users', 'analytics'];
        if (in_array($module, $adminModules)) {
            return 'admin';
        }

        // Accountant modules
        $accountantModules = ['transactions', 'invoices'];
        if (in_array($module, $accountantModules)) {
            return 'accountant';
        }

        // Editor modules
        $editorModules = ['products', 'categories', 'media'];
        if (in_array($module, $editorModules)) {
            return 'editor';
        }

        // Data entry modules
        $dataEntryModules = ['orders', 'coupons'];
        if (in_array($module, $dataEntryModules)) {
            return 'data_entry';
        }

        // Default to general
        return 'general';
    }
}
