import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AddressService, UserAddress } from '../../services/address.service';
import { AddressFormComponent } from '../address-form/address-form.component';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-address-list',
  templateUrl: './address-list.component.html',
  styleUrls: ['./address-list.component.scss']
})
export class AddressListComponent implements OnInit {
  addresses: UserAddress[] = [];
  loading = false;
  displayedColumns: string[] = ['name', 'type', 'address', 'default', 'actions'];

  constructor(
    private addressService: AddressService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAddresses();
  }

  /**
   * Load all addresses
   */
  loadAddresses(): void {
    this.loading = true;
    this.addressService.getAddresses().subscribe({
      next: (response) => {
        if (response.success) {
          this.addresses = response.data;
        } else {
          this.showError('Failed to load addresses');
        }
        this.loading = false;
      },
      error: (error) => {
        this.showError('Error loading addresses: ' + error.message);
        this.loading = false;
      }
    });
  }

  /**
   * Open address form dialog
   */
  openAddressForm(address?: UserAddress): void {
    const dialogRef = this.dialog.open(AddressFormComponent, {
      width: '600px',
      data: { address: address || null }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadAddresses();
        this.showSuccess(address ? 'Address updated successfully' : 'Address created successfully');
      }
    });
  }

  /**
   * Set address as default
   */
  setAsDefault(address: UserAddress): void {
    if (address.is_default) {
      return;
    }

    this.addressService.setAsDefault(address.id!).subscribe({
      next: (response) => {
        if (response.success) {
          this.loadAddresses();
          this.showSuccess('Default address updated');
        } else {
          this.showError('Failed to update default address');
        }
      },
      error: (error) => {
        this.showError('Error updating default address: ' + error.message);
      }
    });
  }

  /**
   * Delete address
   */
  deleteAddress(address: UserAddress): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Address',
        message: `Are you sure you want to delete the address for ${this.getFullName(address)}?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.addressService.deleteAddress(address.id!).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadAddresses();
              this.showSuccess('Address deleted successfully');
            } else {
              this.showError('Failed to delete address');
            }
          },
          error: (error) => {
            this.showError('Error deleting address: ' + error.message);
          }
        });
      }
    });
  }

  /**
   * Get full name from address
   */
  getFullName(address: UserAddress): string {
    return this.addressService.getFullName(address);
  }

  /**
   * Format address for display
   */
  formatAddress(address: UserAddress): string {
    return this.addressService.formatAddress(address);
  }

  /**
   * Get address type badge class
   */
  getTypeBadgeClass(type: string): string {
    switch (type) {
      case 'shipping':
        return 'badge-primary';
      case 'billing':
        return 'badge-success';
      case 'both':
        return 'badge-info';
      default:
        return 'badge-secondary';
    }
  }

  /**
   * Get address type display text
   */
  getTypeDisplayText(type: string): string {
    switch (type) {
      case 'shipping':
        return 'Shipping';
      case 'billing':
        return 'Billing';
      case 'both':
        return 'Both';
      default:
        return type;
    }
  }

  /**
   * Check if address can be deleted
   */
  canDelete(address: UserAddress): boolean {
    // Don't allow deletion of default addresses if it's the only one of its type
    if (address.is_default) {
      const sameTypeAddresses = this.addresses.filter(a => 
        a.type === address.type || a.type === 'both' || address.type === 'both'
      );
      return sameTypeAddresses.length > 1;
    }
    return true;
  }

  /**
   * Show success message
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  /**
   * Filter addresses by type
   */
  getAddressesByType(type: string): UserAddress[] {
    return this.addresses.filter(address => 
      address.type === type || address.type === 'both'
    );
  }

  /**
   * Get shipping addresses
   */
  getShippingAddresses(): UserAddress[] {
    return this.getAddressesByType('shipping');
  }

  /**
   * Get billing addresses
   */
  getBillingAddresses(): UserAddress[] {
    return this.getAddressesByType('billing');
  }

  /**
   * Check if there are any addresses
   */
  hasAddresses(): boolean {
    return this.addresses.length > 0;
  }

  /**
   * Get default address for type
   */
  getDefaultAddress(type: string): UserAddress | undefined {
    return this.addresses.find(address => 
      address.is_default && (address.type === type || address.type === 'both')
    );
  }
}
