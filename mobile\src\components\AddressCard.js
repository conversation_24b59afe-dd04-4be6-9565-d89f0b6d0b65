import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../styles/theme';
import AddressService from '../services/AddressService';

const AddressCard = ({ 
  address, 
  onEdit, 
  onSetDefault, 
  onDelete, 
  canDelete = true,
  showActions = true 
}) => {
  /**
   * Get type badge color
   */
  const getTypeBadgeColor = (type) => {
    switch (type) {
      case 'shipping':
        return colors.primary;
      case 'billing':
        return colors.success;
      case 'both':
        return colors.info;
      default:
        return colors.textSecondary;
    }
  };

  /**
   * Get type display text
   */
  const getTypeDisplayText = (type) => {
    switch (type) {
      case 'shipping':
        return 'Shipping';
      case 'billing':
        return 'Billing';
      case 'both':
        return 'Both';
      default:
        return type;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.name}>
            {AddressService.getFullName(address)}
          </Text>
          {address.is_default && (
            <View style={styles.defaultBadge}>
              <Icon name="star" size={12} color={colors.white} />
              <Text style={styles.defaultBadgeText}>DEFAULT</Text>
            </View>
          )}
        </View>
        <View style={[styles.typeBadge, { backgroundColor: getTypeBadgeColor(address.type) }]}>
          <Text style={styles.typeBadgeText}>
            {getTypeDisplayText(address.type)}
          </Text>
        </View>
      </View>

      {/* Company */}
      {address.company && (
        <Text style={styles.company}>{address.company}</Text>
      )}

      {/* Address Details */}
      <View style={styles.addressDetails}>
        <Text style={styles.addressLine}>{address.address_line_1}</Text>
        {address.address_line_2 && (
          <Text style={styles.addressLine}>{address.address_line_2}</Text>
        )}
        <Text style={styles.addressLine}>
          {address.city}, {address.state} {address.postal_code}
        </Text>
        <Text style={styles.addressLine}>{address.country}</Text>
        
        {address.phone && (
          <View style={styles.phoneContainer}>
            <Icon name="phone" size={16} color={colors.textSecondary} />
            <Text style={styles.phoneText}>{address.phone}</Text>
          </View>
        )}
      </View>

      {/* Actions */}
      {showActions && (
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.editButton]}
            onPress={onEdit}
            activeOpacity={0.7}
          >
            <Icon name="edit" size={16} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Edit
            </Text>
          </TouchableOpacity>

          {!address.is_default && (
            <TouchableOpacity
              style={[styles.actionButton, styles.defaultButton]}
              onPress={onSetDefault}
              activeOpacity={0.7}
            >
              <Icon name="star-border" size={16} color={colors.success} />
              <Text style={[styles.actionButtonText, { color: colors.success }]}>
                Set Default
              </Text>
            </TouchableOpacity>
          )}

          {canDelete && (
            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton]}
              onPress={onDelete}
              activeOpacity={0.7}
            >
              <Icon name="delete" size={16} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>
                Delete
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.md,
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  name: {
    ...typography.h4,
    color: colors.text,
    marginRight: spacing.xs,
  },
  defaultBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.success,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 10,
  },
  defaultBadgeText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
    marginLeft: 2,
    fontSize: 10,
  },
  typeBadge: {
    paddingHorizontal: spacing.xs,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeBadgeText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
    fontSize: 11,
    textTransform: 'uppercase',
  },
  company: {
    ...typography.body,
    color: colors.textSecondary,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  addressDetails: {
    marginBottom: spacing.sm,
  },
  addressLine: {
    ...typography.body,
    color: colors.text,
    lineHeight: 20,
    marginBottom: 2,
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  phoneText: {
    ...typography.body,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    borderRadius: 6,
    minWidth: 70,
    justifyContent: 'center',
  },
  editButton: {
    backgroundColor: `${colors.primary}10`,
  },
  defaultButton: {
    backgroundColor: `${colors.success}10`,
  },
  deleteButton: {
    backgroundColor: `${colors.error}10`,
  },
  actionButtonText: {
    ...typography.caption,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default AddressCard;
