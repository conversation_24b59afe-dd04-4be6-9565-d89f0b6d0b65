<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Coupon;
use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use Carbon\Carbon;

class CouponsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the super admin user to set as creator
        $superAdmin = User::where('email', '<EMAIL>')->first();

        if (!$superAdmin) {
            $this->command->warn('Super admin user not found. Please run UsersSeeder first.');
            return;
        }

        // Sample coupons data
        $coupons = [
            // Percentage discount coupons
            [
                'name' => 'Welcome Discount',
                'code' => 'WELCOME10',
                'description' => 'Welcome new customers with 10% off their first order',
                'type' => Coupon::TYPE_PERCENTAGE,
                'value' => 10.00,
                'minimum_amount' => 50.00,
                'maximum_discount' => null,
                'usage_limit' => 1000,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(6),
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => false,
                'individual_use' => false,
                'free_shipping' => false,
            ],
            [
                'name' => 'Summer Sale',
                'code' => 'SUMMER25',
                'description' => 'Summer sale - 25% off on all products',
                'type' => Coupon::TYPE_PERCENTAGE,
                'value' => 25.00,
                'minimum_amount' => 100.00,
                'maximum_discount' => 50.00,
                'usage_limit' => 500,
                'usage_limit_per_user' => 2,
                'is_active' => true,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(3),
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => true,
                'individual_use' => false,
                'free_shipping' => false,
            ],
            [
                'name' => 'Electronics Discount',
                'code' => 'TECH15',
                'description' => '15% off on all electronics',
                'type' => Coupon::TYPE_PERCENTAGE,
                'value' => 15.00,
                'minimum_amount' => 200.00,
                'maximum_discount' => 100.00,
                'usage_limit' => 200,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(2),
                'applies_to' => Coupon::APPLIES_TO_SPECIFIC_CATEGORIES,
                'exclude_sale_items' => false,
                'individual_use' => false,
                'free_shipping' => false,
                'category_names' => ['Electronics'],
            ],

            // Fixed cart discount coupons
            [
                'name' => 'Fixed Cart Discount',
                'code' => 'SAVE20',
                'description' => '$20 off on orders over $150',
                'type' => Coupon::TYPE_FIXED_CART,
                'value' => 20.00,
                'minimum_amount' => 150.00,
                'maximum_discount' => null,
                'usage_limit' => 300,
                'usage_limit_per_user' => 3,
                'is_active' => true,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(4),
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => false,
                'individual_use' => false,
                'free_shipping' => false,
            ],
            [
                'name' => 'Big Spender Discount',
                'code' => 'BIGSPEND',
                'description' => '$50 off on orders over $500',
                'type' => Coupon::TYPE_FIXED_CART,
                'value' => 50.00,
                'minimum_amount' => 500.00,
                'maximum_discount' => null,
                'usage_limit' => 100,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(12),
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => false,
                'individual_use' => true,
                'free_shipping' => true,
            ],

            // Free shipping coupons
            [
                'name' => 'Free Shipping',
                'code' => 'FREESHIP',
                'description' => 'Free shipping on all orders',
                'type' => Coupon::TYPE_FIXED_CART,
                'value' => 0.00,
                'minimum_amount' => 75.00,
                'maximum_discount' => null,
                'usage_limit' => null,
                'usage_limit_per_user' => null,
                'is_active' => true,
                'starts_at' => now(),
                'expires_at' => null,
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => false,
                'individual_use' => false,
                'free_shipping' => true,
            ],

            // Product-specific coupons
            [
                'name' => 'Clothing Sale',
                'code' => 'FASHION20',
                'description' => '20% off on clothing items',
                'type' => Coupon::TYPE_PERCENTAGE,
                'value' => 20.00,
                'minimum_amount' => null,
                'maximum_discount' => 30.00,
                'usage_limit' => 150,
                'usage_limit_per_user' => 2,
                'is_active' => true,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(1),
                'applies_to' => Coupon::APPLIES_TO_SPECIFIC_CATEGORIES,
                'exclude_sale_items' => false,
                'individual_use' => false,
                'free_shipping' => false,
                'category_names' => ['Clothing'],
            ],

            // Expired coupon (for testing)
            [
                'name' => 'Expired Coupon',
                'code' => 'EXPIRED',
                'description' => 'This coupon has expired',
                'type' => Coupon::TYPE_PERCENTAGE,
                'value' => 30.00,
                'minimum_amount' => null,
                'maximum_discount' => null,
                'usage_limit' => 100,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => now()->subMonths(2),
                'expires_at' => now()->subDays(7),
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => false,
                'individual_use' => false,
                'free_shipping' => false,
            ],

            // Inactive coupon (for testing)
            [
                'name' => 'Inactive Coupon',
                'code' => 'INACTIVE',
                'description' => 'This coupon is inactive',
                'type' => Coupon::TYPE_PERCENTAGE,
                'value' => 15.00,
                'minimum_amount' => null,
                'maximum_discount' => null,
                'usage_limit' => 50,
                'usage_limit_per_user' => 1,
                'is_active' => false,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(1),
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => false,
                'individual_use' => false,
                'free_shipping' => false,
            ],

            // Future coupon (scheduled)
            [
                'name' => 'Black Friday Sale',
                'code' => 'BLACKFRI',
                'description' => 'Black Friday mega sale - 40% off everything',
                'type' => Coupon::TYPE_PERCENTAGE,
                'value' => 40.00,
                'minimum_amount' => null,
                'maximum_discount' => 200.00,
                'usage_limit' => 1000,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => now()->addMonths(6),
                'expires_at' => now()->addMonths(6)->addDays(7),
                'applies_to' => Coupon::APPLIES_TO_ALL,
                'exclude_sale_items' => false,
                'individual_use' => true,
                'free_shipping' => true,
            ],
        ];

        foreach ($coupons as $couponData) {
            $categoryNames = $couponData['category_names'] ?? [];
            unset($couponData['category_names']);

            $coupon = Coupon::create(array_merge($couponData, [
                'created_by' => $superAdmin->id,
            ]));

            // Attach categories if specified
            if (!empty($categoryNames)) {
                $categories = Category::whereIn('name', $categoryNames)->get();
                if ($categories->isNotEmpty()) {
                    $coupon->categories()->attach($categories->pluck('id'));
                }
            }

            $this->command->info("Created coupon: {$coupon->name} (Code: {$coupon->code})");
        }

        $this->command->info('Coupons seeded successfully!');
        $this->command->info('Created ' . count($coupons) . ' sample coupons with various configurations');
        $this->command->info('Coupon codes: WELCOME10, SUMMER25, TECH15, SAVE20, BIGSPEND, FREESHIP, FASHION20, EXPIRED, INACTIVE, BLACKFRI');
    }
}
