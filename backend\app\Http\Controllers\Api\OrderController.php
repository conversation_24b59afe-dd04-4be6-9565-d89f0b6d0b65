<?php

namespace App\Http\Controllers\Api;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductVariation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class OrderController extends BaseController
{
    /**
     * Display a listing of orders.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $status = $request->get('status');
        $paymentStatus = $request->get('payment_status');
        $userId = $request->get('user_id');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Order::with(['user:id,name,email', 'items', 'creator:id,name'])
            ->withCount('items');

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($status) {
            $query->status($status);
        }

        // Filter by payment status
        if ($paymentStatus) {
            $query->paymentStatus($paymentStatus);
        }

        // Filter by user
        if ($userId) {
            $query->byUser($userId);
        }

        // Filter by date range
        if ($dateFrom && $dateTo) {
            $query->dateRange($dateFrom, $dateTo);
        } elseif ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        } elseif ($dateTo) {
            $query->where('created_at', '<=', $dateTo);
        }

        // Sorting
        $allowedSortFields = ['order_number', 'total_amount', 'status', 'payment_status', 'created_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $orders = $query->paginate($perPage);

        // Transform order data
        $orders->getCollection()->transform(function ($order) {
            return [
                'id' => $order->id,
                'order_number' => $order->order_number,
                'user' => [
                    'id' => $order->user->id,
                    'name' => $order->user->name,
                    'email' => $order->user->email,
                ],
                'status' => $order->status,
                'status_color' => $order->status_color,
                'payment_status' => $order->payment_status,
                'payment_status_color' => $order->payment_status_color,
                'payment_method' => $order->payment_method,
                'subtotal' => $order->subtotal,
                'tax_amount' => $order->tax_amount,
                'shipping_amount' => $order->shipping_amount,
                'discount_amount' => $order->discount_amount,
                'total_amount' => $order->total_amount,
                'currency' => $order->currency,
                'total_items' => $order->total_items,
                'items_count' => $order->items_count,
                'coupon_code' => $order->coupon_code,
                'formatted_shipping_address' => $order->formatted_shipping_address,
                'can_be_cancelled' => $order->can_be_cancelled,
                'can_be_refunded' => $order->can_be_refunded,
                'creator' => $order->creator ? $order->creator->name : null,
                'created_at' => $order->created_at,
                'updated_at' => $order->updated_at,
            ];
        });

        return $this->sendPaginatedResponse($orders);
    }

    /**
     * Store a newly created order.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|integer|exists:products,id',
            'items.*.product_variation_id' => 'nullable|integer|exists:product_variations,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'nullable|numeric|min:0',
            'shipping_address' => 'required|array',
            'shipping_address.name' => 'required|string|max:255',
            'shipping_address.address' => 'required|string|max:500',
            'shipping_address.city' => 'required|string|max:100',
            'shipping_address.state' => 'required|string|max:100',
            'shipping_address.postal_code' => 'required|string|max:20',
            'shipping_address.country' => 'required|string|max:100',
            'shipping_address.phone' => 'nullable|string|max:20',
            'billing_address' => 'nullable|array',
            'payment_method' => 'required|in:cod,razorpay,gpay,bank_transfer',
            'shipping_amount' => 'nullable|numeric|min:0',
            'coupon_code' => 'nullable|string|exists:coupons,code',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        DB::beginTransaction();
        try {
            // Create order
            $order = Order::create([
                'user_id' => $request->user_id,
                'payment_method' => $request->payment_method,
                'subtotal' => 0, // Will be calculated
                'tax_amount' => 0,
                'shipping_amount' => $request->shipping_amount ?? 0,
                'discount_amount' => 0,
                'total_amount' => 0, // Will be calculated
                'shipping_address' => $request->shipping_address,
                'billing_address' => $request->billing_address ?? $request->shipping_address,
                'coupon_code' => $request->coupon_code,
                'notes' => $request->notes,
                'created_by' => auth()->id(),
            ]);

            // Create order items
            foreach ($request->items as $itemData) {
                $product = Product::find($itemData['product_id']);
                $variation = null;
                
                if (isset($itemData['product_variation_id'])) {
                    $variation = ProductVariation::find($itemData['product_variation_id']);
                }

                // Determine price
                $price = $itemData['price'] ?? ($variation ? $variation->effective_price : $product->effective_price);

                // Get product attributes
                $attributes = $variation ? $variation->attributes : null;

                // Create order item
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_variation_id' => $variation?->id,
                    'product_name' => $product->name,
                    'product_sku' => $variation ? $variation->sku : $product->sku,
                    'product_image' => $variation ? $variation->image : $product->main_image_url,
                    'price' => $price,
                    'quantity' => $itemData['quantity'],
                    'product_attributes' => $attributes,
                ]);

                // Update stock
                if ($variation) {
                    $variation->updateStock($itemData['quantity'], 'decrease');
                } else {
                    $product->updateStock($itemData['quantity'], 'decrease');
                }
            }

            // Calculate order totals
            $order->calculateTotals();

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($order)
                ->log('Order created');

            return $this->sendCreated([
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
                'payment_status' => $order->payment_status,
                'total_amount' => $order->total_amount,
                'user_id' => $order->user_id,
            ], 'Order created successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to create order: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified order.
     */
    public function show(string $id): JsonResponse
    {
        $order = Order::with([
            'user:id,name,email,phone',
            'items.product:id,name,slug',
            'items.productVariation:id,sku,attributes',
            'transactions',
            'invoice',
            'creator:id,name'
        ])->find($id);

        if (!$order) {
            return $this->sendNotFound('Order not found');
        }

        return $this->sendResponse([
            'id' => $order->id,
            'order_number' => $order->order_number,
            'user' => [
                'id' => $order->user->id,
                'name' => $order->user->name,
                'email' => $order->user->email,
                'phone' => $order->user->phone,
            ],
            'status' => $order->status,
            'status_color' => $order->status_color,
            'payment_status' => $order->payment_status,
            'payment_status_color' => $order->payment_status_color,
            'payment_method' => $order->payment_method,
            'subtotal' => $order->subtotal,
            'tax_amount' => $order->tax_amount,
            'shipping_amount' => $order->shipping_amount,
            'discount_amount' => $order->discount_amount,
            'total_amount' => $order->total_amount,
            'currency' => $order->currency,
            'notes' => $order->notes,
            'shipping_address' => $order->shipping_address,
            'billing_address' => $order->billing_address,
            'formatted_shipping_address' => $order->formatted_shipping_address,
            'formatted_billing_address' => $order->formatted_billing_address,
            'coupon_code' => $order->coupon_code,
            'coupon_discount' => $order->coupon_discount,
            'total_items' => $order->total_items,
            'can_be_cancelled' => $order->can_be_cancelled,
            'can_be_refunded' => $order->can_be_refunded,
            'items' => $order->items->map(function ($item) {
                return [
                    'id' => $item->id,
                    'product' => [
                        'id' => $item->product->id,
                        'name' => $item->product->name,
                        'slug' => $item->product->slug,
                    ],
                    'product_name' => $item->product_name,
                    'product_sku' => $item->product_sku,
                    'product_image_url' => $item->product_image_url,
                    'price' => $item->price,
                    'quantity' => $item->quantity,
                    'total' => $item->total,
                    'product_attributes' => $item->product_attributes,
                    'formatted_attributes' => $item->formatted_attributes,
                ];
            }),
            'transactions' => $order->transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status,
                    'reference' => $transaction->reference,
                    'created_at' => $transaction->created_at,
                ];
            }),
            'invoice' => $order->invoice ? [
                'id' => $order->invoice->id,
                'invoice_number' => $order->invoice->invoice_number,
                'status' => $order->invoice->status,
                'created_at' => $order->invoice->created_at,
            ] : null,
            'processed_at' => $order->processed_at,
            'shipped_at' => $order->shipped_at,
            'delivered_at' => $order->delivered_at,
            'cancelled_at' => $order->cancelled_at,
            'refunded_at' => $order->refunded_at,
            'creator' => $order->creator ? [
                'id' => $order->creator->id,
                'name' => $order->creator->name,
            ] : null,
            'created_at' => $order->created_at,
            'updated_at' => $order->updated_at,
        ]);
    }

    /**
     * Update the specified order.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $order = Order::find($id);

        if (!$order) {
            return $this->sendNotFound('Order not found');
        }

        $validator = Validator::make($request->all(), [
            'status' => 'in:pending,processing,shipped,delivered,cancelled,refunded',
            'payment_status' => 'in:pending,paid,failed,refunded,partially_refunded',
            'payment_method' => 'in:cod,razorpay,gpay,bank_transfer',
            'shipping_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'shipping_address' => 'nullable|array',
            'billing_address' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $updateData = $request->only([
                'payment_status', 'payment_method', 'shipping_amount',
                'notes', 'shipping_address', 'billing_address'
            ]);

            $order->update($updateData);

            // Handle status update separately to trigger proper logging
            if ($request->has('status')) {
                $order->updateStatus($request->status, $request->get('status_note'));
            }

            // Recalculate totals if shipping amount changed
            if ($request->has('shipping_amount')) {
                $order->calculateTotals();
            }

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($order)
                ->log('Order updated');

            return $this->sendUpdated([
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
                'payment_status' => $order->payment_status,
                'total_amount' => $order->total_amount,
            ], 'Order updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update order: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, string $id): JsonResponse
    {
        $order = Order::find($id);

        if (!$order) {
            return $this->sendNotFound('Order not found');
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled,refunded',
            'note' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $order->updateStatus($request->status, $request->note);

            return $this->sendResponse([
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
                'status_color' => $order->status_color,
            ], 'Order status updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update order status: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Cancel an order.
     */
    public function cancel(Request $request, string $id): JsonResponse
    {
        $order = Order::find($id);

        if (!$order) {
            return $this->sendNotFound('Order not found');
        }

        if (!$order->can_be_cancelled) {
            return $this->sendError('Order cannot be cancelled in current status');
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        DB::beginTransaction();
        try {
            // Restore stock for all items
            foreach ($order->items as $item) {
                if ($item->product_variation_id) {
                    $item->productVariation->updateStock($item->quantity, 'increase');
                } else {
                    $item->product->updateStock($item->quantity, 'increase');
                }
            }

            // Update order status
            $order->updateStatus(Order::STATUS_CANCELLED, $request->reason);

            DB::commit();

            return $this->sendResponse([
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
            ], 'Order cancelled successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to cancel order: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get order statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = Order::getStatistics();

            return $this->sendResponse($stats);

        } catch (\Exception $e) {
            return $this->sendError('Failed to get order statistics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get orders by user.
     */
    public function userOrders(Request $request, string $userId): JsonResponse
    {
        $user = User::find($userId);

        if (!$user) {
            return $this->sendNotFound('User not found');
        }

        $perPage = $request->get('per_page', 20);
        $status = $request->get('status');

        $query = $user->orders()->with(['items.product:id,name,slug'])
            ->withCount('items');

        if ($status) {
            $query->status($status);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Transform order data
        $orders->getCollection()->transform(function ($order) {
            return [
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
                'status_color' => $order->status_color,
                'payment_status' => $order->payment_status,
                'payment_method' => $order->payment_method,
                'total_amount' => $order->total_amount,
                'currency' => $order->currency,
                'total_items' => $order->total_items,
                'items_count' => $order->items_count,
                'created_at' => $order->created_at,
            ];
        });

        return $this->sendPaginatedResponse($orders);
    }
}
