import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SelectionModel } from '@angular/cdk/collections';
import { Router } from '@angular/router';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

import { OrderService, Order, OrderFilters } from '../../services/order.service';
import { LoadingService } from '../../../../core/services/loading.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-order-list',
  templateUrl: './order-list.component.html',
  styleUrls: ['./order-list.component.scss']
})
export class OrderListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'select',
    'order_number',
    'customer',
    'status',
    'payment_status',
    'payment_method',
    'total_amount',
    'items_count',
    'created_at',
    'actions'
  ];

  dataSource = new MatTableDataSource<Order>();
  selection = new SelectionModel<Order>(true, []);
  
  // Filters
  filters: OrderFilters = {
    per_page: 20,
    page: 1,
    sort_by: 'created_at',
    sort_order: 'desc'
  };

  searchSubject = new Subject<string>();
  
  // Pagination
  totalItems = 0;
  pageSize = 20;
  pageSizeOptions = [10, 20, 50, 100];

  // Loading states
  loading = false;
  bulkActionLoading = false;

  // Filter options
  statusOptions: { value: string; label: string; color: string }[] = [];
  paymentStatusOptions: { value: string; label: string; color: string }[] = [];
  paymentMethodOptions: { value: string; label: string }[] = [];

  constructor(
    private orderService: OrderService,
    private loadingService: LoadingService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeFilterOptions();
    this.loadOrders();
    this.setupSearch();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    // Handle sorting
    this.sort.sortChange.subscribe(() => {
      this.filters.sort_by = this.sort.active;
      this.filters.sort_order = this.sort.direction as 'asc' | 'desc';
      this.filters.page = 1;
      this.loadOrders();
    });

    // Handle pagination
    this.paginator.page.subscribe(() => {
      this.filters.page = this.paginator.pageIndex + 1;
      this.filters.per_page = this.paginator.pageSize;
      this.loadOrders();
    });
  }

  private initializeFilterOptions(): void {
    this.statusOptions = [
      { value: '', label: 'All Status', color: '' },
      ...this.orderService.getStatusOptions()
    ];

    this.paymentStatusOptions = [
      { value: '', label: 'All Payment Status', color: '' },
      ...this.orderService.getPaymentStatusOptions()
    ];

    this.paymentMethodOptions = [
      { value: '', label: 'All Payment Methods' },
      ...this.orderService.getPaymentMethodOptions()
    ];
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.filters.search = searchTerm;
      this.filters.page = 1;
      this.loadOrders();
    });
  }

  loadOrders(): void {
    this.loading = true;
    this.loadingService.setLoading(true);

    this.orderService.getOrders(this.filters).subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.data;
          this.totalItems = response.meta.total;
          
          // Update paginator
          this.paginator.length = this.totalItems;
          this.paginator.pageIndex = response.meta.current_page - 1;
          this.paginator.pageSize = response.meta.per_page;
        }
        this.loading = false;
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error loading orders:', error);
        this.snackBar.open('Error loading orders', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loading = false;
        this.loadingService.setLoading(false);
      }
    });
  }

  onSearch(searchTerm: string): void {
    this.searchSubject.next(searchTerm);
  }

  onFilterChange(): void {
    this.filters.page = 1;
    this.loadOrders();
  }

  clearFilters(): void {
    this.filters = {
      per_page: 20,
      page: 1,
      sort_by: 'created_at',
      sort_order: 'desc'
    };
    this.loadOrders();
  }

  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  // Navigation methods
  createOrder(): void {
    this.router.navigate(['/orders/create']);
  }

  viewOrder(order: Order): void {
    this.router.navigate(['/orders', order.id]);
  }

  editOrder(order: Order): void {
    this.router.navigate(['/orders', order.id, 'edit']);
  }

  // Order actions
  updateOrderStatus(order: Order, status: string): void {
    this.loadingService.setLoading(true);

    this.orderService.updateOrderStatus(order.id, status).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Order status updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadOrders();
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error updating order status:', error);
        this.snackBar.open('Error updating order status', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  cancelOrder(order: Order): void {
    if (!order.can_be_cancelled) {
      this.snackBar.open('This order cannot be cancelled', 'Close', {
        duration: 3000,
        panelClass: ['warning-snackbar']
      });
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Cancel Order',
        message: `Are you sure you want to cancel order ${order.order_number}?`,
        confirmText: 'Cancel Order',
        cancelText: 'Keep Order',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadingService.setLoading(true);

        this.orderService.cancelOrder(order.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Order cancelled successfully', 'Close', {
                duration: 3000,
                panelClass: ['success-snackbar']
              });
              this.loadOrders();
            }
            this.loadingService.setLoading(false);
          },
          error: (error) => {
            console.error('Error cancelling order:', error);
            this.snackBar.open('Error cancelling order', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            this.loadingService.setLoading(false);
          }
        });
      }
    });
  }

  generateInvoice(order: Order): void {
    this.loadingService.setLoading(true);

    this.orderService.generateInvoice(order.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Invoice generated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          // Optionally download the invoice
          this.downloadInvoice(order);
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error generating invoice:', error);
        this.snackBar.open('Error generating invoice', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  downloadInvoice(order: Order): void {
    this.orderService.downloadInvoice(order.id).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `invoice-${order.order_number}.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading invoice:', error);
        this.snackBar.open('Error downloading invoice', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  sendConfirmationEmail(order: Order): void {
    this.loadingService.setLoading(true);

    this.orderService.sendConfirmationEmail(order.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Confirmation email sent successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error sending confirmation email:', error);
        this.snackBar.open('Error sending confirmation email', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  // Bulk actions
  bulkUpdateStatus(status: string): void {
    if (this.selection.selected.length === 0) {
      this.snackBar.open('Please select orders to update', 'Close', {
        duration: 3000,
        panelClass: ['warning-snackbar']
      });
      return;
    }

    const orderIds = this.selection.selected.map(order => order.id);
    this.bulkActionLoading = true;

    this.orderService.bulkUpdateStatus(orderIds, status).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(`${orderIds.length} orders updated successfully`, 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.selection.clear();
          this.loadOrders();
        }
        this.bulkActionLoading = false;
      },
      error: (error) => {
        console.error('Error updating orders:', error);
        this.snackBar.open('Error updating orders', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.bulkActionLoading = false;
      }
    });
  }

  // Utility methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  getStatusColor(status: string): string {
    const statusOption = this.statusOptions.find(option => option.value === status);
    return statusOption ? statusOption.color : '';
  }

  getPaymentStatusColor(paymentStatus: string): string {
    const statusOption = this.paymentStatusOptions.find(option => option.value === paymentStatus);
    return statusOption ? statusOption.color : '';
  }

  getPaymentMethodLabel(paymentMethod: string): string {
    const methodOption = this.paymentMethodOptions.find(option => option.value === paymentMethod);
    return methodOption ? methodOption.label : paymentMethod;
  }

  // Export functionality
  exportOrders(): void {
    this.loadingService.setLoading(true);

    this.orderService.exportOrders('csv', this.filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `orders-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        window.URL.revokeObjectURL(url);
        
        this.snackBar.open('Orders exported successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error exporting orders:', error);
        this.snackBar.open('Error exporting orders', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }
}
