-- <PERSON><PERSON> Script to Add Missing Product Fields
-- Run this script to ensure all product fields are present

-- Add is_active field to products table if it doesn't exist
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE AFTER status;

-- Add mrp field if it doesn't exist (Maximum Retail Price)
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS mrp DECIMAL(10,2) NOT NULL DEFAULT 0 AFTER category_id;

-- Add selling_price field if it doesn't exist
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS selling_price DECIMAL(10,2) NOT NULL DEFAULT 0 AFTER mrp;

-- Add discount_percentage field if it doesn't exist
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS discount_percentage DECIMAL(5,2) DEFAULT 0 AFTER cost_price;

-- Add main_image field if it doesn't exist
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS main_image VARCHAR(255) NULL AFTER gallery;

-- Update existing products to have proper default values
UPDATE products SET 
    mrp = CASE WHEN mrp IS NULL OR mrp = 0 THEN price ELSE mrp END,
    selling_price = CASE WHEN selling_price IS NULL OR selling_price = 0 THEN price ELSE selling_price END,
    is_active = CASE WHEN is_active IS NULL THEN TRUE ELSE is_active END
WHERE id > 0;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_mrp ON products(mrp);
CREATE INDEX IF NOT EXISTS idx_products_selling_price ON products(selling_price);
CREATE INDEX IF NOT EXISTS idx_products_discount_percentage ON products(discount_percentage);

-- Verify all fields exist
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'products' 
    AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
