<div class="user-form-dialog">
  <div mat-dialog-title class="dialog-header">
    <h2>{{ dialogTitle }}</h2>
    <button mat-icon-button mat-dialog-close>
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div mat-dialog-content class="dialog-content">
    <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
      <div class="form-grid">
        <!-- Name Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Full Name</mat-label>
          <input
            matInput
            formControlName="name"
            placeholder="Enter full name"
            autocomplete="name"
          >
          <mat-error *ngIf="name?.invalid && name?.touched">
            {{ getNameErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Email Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email Address</mat-label>
          <input
            matInput
            type="email"
            formControlName="email"
            placeholder="Enter email address"
            autocomplete="email"
          >
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="email?.invalid && email?.touched">
            {{ getEmailErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Phone Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Phone Number</mat-label>
          <input
            matInput
            type="tel"
            formControlName="phone"
            placeholder="Enter phone number (optional)"
            autocomplete="tel"
          >
          <mat-icon matSuffix>phone</mat-icon>
          <mat-error *ngIf="phone?.invalid && phone?.touched">
            {{ getPhoneErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Role Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Role</mat-label>
          <mat-select formControlName="role">
            <mat-option *ngFor="let option of getRoleOptions()" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
          <mat-icon matSuffix>security</mat-icon>
        </mat-form-field>

        <!-- Password Fields (Create Mode Only) -->
        <ng-container *ngIf="data.mode === 'create'">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Password</mat-label>
            <input
              matInput
              [type]="hidePassword ? 'password' : 'text'"
              formControlName="password"
              placeholder="Enter password"
              autocomplete="new-password"
            >
            <button
              mat-icon-button
              matSuffix
              type="button"
              (click)="hidePassword = !hidePassword"
              [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hidePassword"
            >
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="password?.invalid && password?.touched">
              {{ getPasswordErrorMessage() }}
            </mat-error>
            <mat-hint>
              Password must contain at least 8 characters with uppercase, lowercase, number, and special character
            </mat-hint>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Confirm Password</mat-label>
            <input
              matInput
              [type]="hideConfirmPassword ? 'password' : 'text'"
              formControlName="password_confirmation"
              placeholder="Confirm password"
              autocomplete="new-password"
            >
            <button
              mat-icon-button
              matSuffix
              type="button"
              (click)="hideConfirmPassword = !hideConfirmPassword"
              [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hideConfirmPassword"
            >
              <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="passwordConfirmation?.invalid && passwordConfirmation?.touched">
              {{ getPasswordConfirmationErrorMessage() }}
            </mat-error>
          </mat-form-field>
        </ng-container>

        <!-- Status Field -->
        <div class="status-field full-width">
          <mat-slide-toggle formControlName="is_active" color="primary">
            <span class="toggle-label">
              <mat-icon>{{ isActive?.value ? 'check_circle' : 'cancel' }}</mat-icon>
              {{ isActive?.value ? 'Active' : 'Inactive' }}
            </span>
          </mat-slide-toggle>
          <mat-hint class="status-hint">
            {{ isActive?.value ? 'User can log in and access the system' : 'User cannot log in or access the system' }}
          </mat-hint>
        </div>
      </div>
    </form>

    <!-- Role Information -->
    <div class="role-info" *ngIf="role?.value">
      <mat-card class="info-card">
        <mat-card-header>
          <mat-icon mat-card-avatar>info</mat-icon>
          <mat-card-title>Role Information</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p><strong>Selected Role:</strong> {{ getRoleOptions().find(r => r.value === role?.value)?.label }}</p>
          <p class="role-description">
            <ng-container [ngSwitch]="role?.value">
              <span *ngSwitchCase="'super_admin'">
                Has full system access and can manage all users, settings, and data.
              </span>
              <span *ngSwitchCase="'admin'">
                Can manage users, products, orders, and most system settings.
              </span>
              <span *ngSwitchCase="'manager'">
                Can manage products, orders, and view reports. Limited user management.
              </span>
              <span *ngSwitchCase="'customer'">
                Can place orders, view order history, and manage their own profile.
              </span>
              <span *ngSwitchDefault>
                Standard user with basic access permissions.
              </span>
            </ng-container>
          </p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button
      mat-button
      type="button"
      (click)="onCancel()"
      [disabled]="loading"
    >
      Cancel
    </button>
    
    <button
      mat-raised-button
      color="primary"
      type="submit"
      (click)="onSubmit()"
      [disabled]="loading || userForm.invalid"
    >
      <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
      <span *ngIf="!loading">{{ submitButtonText }}</span>
    </button>
  </div>
</div>
