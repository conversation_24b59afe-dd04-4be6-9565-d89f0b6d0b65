<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\User;
use Carbon\Carbon;

class InvoicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some orders to create invoices for
        $orders = Order::with(['user', 'items'])->take(15)->get();

        if ($orders->isEmpty()) {
            $this->command->warn('No orders found. Please run OrdersSeeder first.');
            return;
        }

        // Get the super admin user to set as creator
        $superAdmin = User::where('email', '<EMAIL>')->first();

        $invoiceCount = 0;

        foreach ($orders as $order) {
            // Skip some orders (70% chance to create invoice)
            if (rand(1, 100) > 70) {
                continue;
            }

            $invoice = Invoice::create([
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'status' => $this->getRandomStatus(),
                'type' => $this->getRandomType(),
                'subtotal' => $order->subtotal,
                'tax_amount' => $order->tax_amount,
                'discount_amount' => $order->discount_amount,
                'total_amount' => $order->total_amount,
                'currency' => $order->currency,
                'due_date' => $this->getRandomDueDate(),
                'paid_at' => $this->getRandomPaidAt(),
                'notes' => $this->getRandomNotes(),
                'terms_conditions' => $this->getDefaultTerms(),
                'billing_address' => $order->billing_address,
                'shipping_address' => $order->shipping_address,
                'created_by' => $superAdmin->id,
                'created_at' => $order->created_at->addDays(rand(0, 5)),
            ]);

            // Create invoice items from order items
            foreach ($order->items as $orderItem) {
                $invoice->items()->create([
                    'product_name' => $orderItem->product_name,
                    'product_sku' => $orderItem->product_sku,
                    'description' => $orderItem->product->description ?? '',
                    'quantity' => $orderItem->quantity,
                    'unit_price' => $orderItem->price,
                    'total_price' => $orderItem->total,
                ]);
            }

            $invoiceCount++;
            $this->command->info("Created invoice: {$invoice->invoice_number} for order {$order->order_number}");
        }

        // Create some standalone invoices (not linked to orders)
        $users = User::where('role', 'customer')->take(5)->get();
        
        foreach ($users as $user) {
            if (rand(1, 100) <= 30) { // 30% chance
                $invoice = Invoice::create([
                    'order_id' => null,
                    'user_id' => $user->id,
                    'status' => $this->getRandomStatus(),
                    'type' => rand(1, 10) <= 2 ? Invoice::TYPE_CREDIT_NOTE : Invoice::TYPE_INVOICE, // 20% credit notes
                    'subtotal' => $subtotal = rand(5000, 50000) / 100, // $50 - $500
                    'tax_amount' => $taxAmount = round($subtotal * 0.1, 2), // 10% tax
                    'discount_amount' => 0,
                    'total_amount' => $subtotal + $taxAmount,
                    'currency' => 'USD',
                    'due_date' => $this->getRandomDueDate(),
                    'paid_at' => $this->getRandomPaidAt(),
                    'notes' => $this->getRandomNotes(),
                    'terms_conditions' => $this->getDefaultTerms(),
                    'billing_address' => [
                        'name' => $user->name,
                        'address' => fake()->streetAddress(),
                        'city' => fake()->city(),
                        'state' => fake()->state(),
                        'postal_code' => fake()->postcode(),
                        'country' => 'United States',
                        'phone' => fake()->phoneNumber(),
                    ],
                    'shipping_address' => null,
                    'created_by' => $superAdmin->id,
                    'created_at' => now()->subDays(rand(1, 90)),
                ]);

                // Create random invoice items
                $itemCount = rand(1, 4);
                for ($i = 0; $i < $itemCount; $i++) {
                    $unitPrice = rand(1000, 15000) / 100; // $10 - $150
                    $quantity = rand(1, 3);
                    
                    $invoice->items()->create([
                        'product_name' => fake()->words(rand(2, 4), true),
                        'product_sku' => 'SKU-' . strtoupper(fake()->bothify('??###')),
                        'description' => fake()->sentence(),
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'total_price' => $unitPrice * $quantity,
                    ]);
                }

                $invoiceCount++;
                $this->command->info("Created standalone invoice: {$invoice->invoice_number}");
            }
        }

        $this->command->info("Invoices seeded successfully!");
        $this->command->info("Created {$invoiceCount} sample invoices");
        $this->command->info("Includes various statuses, types, and payment states");
    }

    /**
     * Get random invoice status.
     */
    private function getRandomStatus(): string
    {
        $statuses = [
            Invoice::STATUS_PAID => 40,      // 40% chance
            Invoice::STATUS_SENT => 25,      // 25% chance
            Invoice::STATUS_DRAFT => 15,     // 15% chance
            Invoice::STATUS_OVERDUE => 15,   // 15% chance
            Invoice::STATUS_CANCELLED => 5,  // 5% chance
        ];

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($statuses as $status => $percentage) {
            $cumulative += $percentage;
            if ($rand <= $cumulative) {
                return $status;
            }
        }

        return Invoice::STATUS_DRAFT;
    }

    /**
     * Get random invoice type.
     */
    private function getRandomType(): string
    {
        $types = [
            Invoice::TYPE_INVOICE => 85,      // 85% chance
            Invoice::TYPE_CREDIT_NOTE => 10,  // 10% chance
            Invoice::TYPE_PROFORMA => 5,      // 5% chance
        ];

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($types as $type => $percentage) {
            $cumulative += $percentage;
            if ($rand <= $cumulative) {
                return $type;
            }
        }

        return Invoice::TYPE_INVOICE;
    }

    /**
     * Get random due date.
     */
    private function getRandomDueDate(): Carbon
    {
        $daysFromNow = rand(-30, 60); // -30 to +60 days from now
        return now()->addDays($daysFromNow);
    }

    /**
     * Get random paid at date.
     */
    private function getRandomPaidAt(): ?Carbon
    {
        // 60% chance of being paid
        if (rand(1, 100) <= 60) {
            return now()->subDays(rand(1, 30));
        }

        return null;
    }

    /**
     * Get random notes.
     */
    private function getRandomNotes(): ?string
    {
        $notes = [
            'Thank you for your business!',
            'Please pay within 30 days of invoice date.',
            'Contact us if you have any questions about this invoice.',
            'Payment terms: Net 30 days.',
            'Late payment fees may apply after due date.',
            null, // No notes
            null,
            null,
        ];

        return $notes[array_rand($notes)];
    }

    /**
     * Get default terms and conditions.
     */
    private function getDefaultTerms(): string
    {
        return 'Payment is due within 30 days of invoice date. Late payments may be subject to a 1.5% monthly service charge. All sales are final unless otherwise agreed upon in writing. Disputes must be reported within 10 days of invoice date.';
    }
}
