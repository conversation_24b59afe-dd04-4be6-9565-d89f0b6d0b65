import ApiService from './ApiService';

class OrderService {
  /**
   * Get paginated list of orders
   */
  async getOrders(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key].toString());
        }
      });

      const response = await ApiService.get(`/orders?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  /**
   * Get single order by ID
   */
  async getOrder(id) {
    try {
      const response = await ApiService.get(`/orders/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  /**
   * Create new order
   */
  async createOrder(orderData) {
    try {
      const response = await ApiService.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId, status, notes = null) {
    try {
      const data = { status };
      if (notes) {
        data.notes = notes;
      }

      const response = await ApiService.put(`/orders/${orderId}/status`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId, reason = null) {
    try {
      const data = {};
      if (reason) {
        data.reason = reason;
      }

      const response = await ApiService.post(`/orders/${orderId}/cancel`, data);
      return response.data;
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw error;
    }
  }

  /**
   * Get order tracking information
   */
  async getOrderTracking(orderId) {
    try {
      const response = await ApiService.get(`/orders/${orderId}/tracking`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order tracking:', error);
      throw error;
    }
  }

  /**
   * Get order invoice
   */
  async getOrderInvoice(orderId) {
    try {
      const response = await ApiService.get(`/orders/${orderId}/invoice`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order invoice:', error);
      throw error;
    }
  }

  /**
   * Download order invoice PDF
   */
  async downloadInvoicePdf(orderId) {
    try {
      const response = await ApiService.get(`/orders/${orderId}/invoice/download`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error downloading invoice:', error);
      throw error;
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStatistics() {
    try {
      const response = await ApiService.get('/orders/statistics');
      return response.data;
    } catch (error) {
      console.error('Error fetching order statistics:', error);
      throw error;
    }
  }

  /**
   * Search orders
   */
  async searchOrders(query, limit = 10) {
    try {
      const params = new URLSearchParams({
        search: query,
        per_page: limit.toString()
      });

      const response = await ApiService.get(`/orders/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching orders:', error);
      throw error;
    }
  }

  /**
   * Get recent orders
   */
  async getRecentOrders(limit = 5) {
    try {
      const params = new URLSearchParams({
        per_page: limit.toString(),
        sort_by: 'created_at',
        sort_order: 'desc'
      });

      const response = await ApiService.get(`/orders?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching recent orders:', error);
      throw error;
    }
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(status, limit = 20) {
    try {
      const params = new URLSearchParams({
        status: status,
        per_page: limit.toString(),
        sort_by: 'created_at',
        sort_order: 'desc'
      });

      const response = await ApiService.get(`/orders?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching orders by status:', error);
      throw error;
    }
  }

  /**
   * Get order items
   */
  async getOrderItems(orderId) {
    try {
      const response = await ApiService.get(`/orders/${orderId}/items`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order items:', error);
      throw error;
    }
  }

  /**
   * Add note to order
   */
  async addOrderNote(orderId, note, isCustomerNote = false) {
    try {
      const data = {
        note: note,
        is_customer_note: isCustomerNote
      };

      const response = await ApiService.post(`/orders/${orderId}/notes`, data);
      return response.data;
    } catch (error) {
      console.error('Error adding order note:', error);
      throw error;
    }
  }

  /**
   * Get order notes
   */
  async getOrderNotes(orderId) {
    try {
      const response = await ApiService.get(`/orders/${orderId}/notes`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order notes:', error);
      throw error;
    }
  }

  /**
   * Request order refund
   */
  async requestRefund(orderId, amount, reason, items = []) {
    try {
      const data = {
        amount: amount,
        reason: reason,
        items: items
      };

      const response = await ApiService.post(`/orders/${orderId}/refund`, data);
      return response.data;
    } catch (error) {
      console.error('Error requesting refund:', error);
      throw error;
    }
  }

  /**
   * Get order refunds
   */
  async getOrderRefunds(orderId) {
    try {
      const response = await ApiService.get(`/orders/${orderId}/refunds`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order refunds:', error);
      throw error;
    }
  }

  /**
   * Rate order/product
   */
  async rateOrder(orderId, productId, rating, review = null) {
    try {
      const data = {
        order_id: orderId,
        product_id: productId,
        rating: rating
      };

      if (review) {
        data.review = review;
      }

      const response = await ApiService.post('/reviews', data);
      return response.data;
    } catch (error) {
      console.error('Error rating order:', error);
      throw error;
    }
  }

  /**
   * Get order status options
   */
  getOrderStatusOptions() {
    return [
      { value: 'pending', label: 'Pending', color: '#ff9800' },
      { value: 'processing', label: 'Processing', color: '#2196f3' },
      { value: 'shipped', label: 'Shipped', color: '#9c27b0' },
      { value: 'delivered', label: 'Delivered', color: '#4caf50' },
      { value: 'cancelled', label: 'Cancelled', color: '#f44336' },
      { value: 'refunded', label: 'Refunded', color: '#607d8b' }
    ];
  }

  /**
   * Get payment status options
   */
  getPaymentStatusOptions() {
    return [
      { value: 'pending', label: 'Pending', color: '#ff9800' },
      { value: 'paid', label: 'Paid', color: '#4caf50' },
      { value: 'failed', label: 'Failed', color: '#f44336' },
      { value: 'refunded', label: 'Refunded', color: '#607d8b' },
      { value: 'partially_refunded', label: 'Partially Refunded', color: '#9c27b0' }
    ];
  }

  /**
   * Get order status color
   */
  getOrderStatusColor(status) {
    const statusOption = this.getOrderStatusOptions().find(option => option.value === status);
    return statusOption ? statusOption.color : '#666666';
  }

  /**
   * Get payment status color
   */
  getPaymentStatusColor(status) {
    const statusOption = this.getPaymentStatusOptions().find(option => option.value === status);
    return statusOption ? statusOption.color : '#666666';
  }

  /**
   * Format order number
   */
  formatOrderNumber(orderNumber) {
    return `#${orderNumber}`;
  }

  /**
   * Calculate order total
   */
  calculateOrderTotal(order) {
    if (!order || !order.items) return 0;
    
    const subtotal = order.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
    
    const tax = order.tax_amount || 0;
    const shipping = order.shipping_amount || 0;
    const discount = order.discount_amount || 0;
    
    return subtotal + tax + shipping - discount;
  }

  /**
   * Check if order can be cancelled
   */
  canCancelOrder(order) {
    const cancellableStatuses = ['pending', 'processing'];
    return cancellableStatuses.includes(order.status);
  }

  /**
   * Check if order can be refunded
   */
  canRefundOrder(order) {
    const refundableStatuses = ['delivered', 'completed'];
    return refundableStatuses.includes(order.status) && order.payment_status === 'paid';
  }

  /**
   * Check if order can be rated
   */
  canRateOrder(order) {
    const ratableStatuses = ['delivered', 'completed'];
    return ratableStatuses.includes(order.status);
  }
}

export default new OrderService();
