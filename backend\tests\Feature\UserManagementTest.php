<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class UserManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $adminUser;
    protected $adminToken;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
        
        $this->adminUser = User::where('email', '<EMAIL>')->first();
        $this->adminToken = JWTAuth::fromUser($this->adminUser);
    }

    /** @test */
    public function admin_can_list_users()
    {
        User::factory()->count(5)->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/users');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'email',
                        'phone',
                        'roles',
                        'groups',
                        'is_active',
                        'created_at',
                    ],
                ],
                'pagination',
            ]);
    }

    /** @test */
    public function admin_can_create_user()
    {
        $userData = [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '+1234567890',
            'address' => '123 Test St',
            'discount_percentage' => 10.5,
            'roles' => ['customer'],
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/users', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'roles',
                ],
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'New User',
        ]);
    }

    /** @test */
    public function admin_can_view_user_details()
    {
        $user = User::factory()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson("/api/users/{$user->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'phone',
                    'address',
                    'roles',
                    'permissions',
                    'groups',
                    'statistics',
                ],
            ]);

        $this->assertEquals($user->id, $response->json('data.id'));
    }

    /** @test */
    public function admin_can_update_user()
    {
        $user = User::factory()->create();

        $updateData = [
            'name' => 'Updated Name',
            'phone' => '+9876543210',
            'discount_percentage' => 15.0,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->putJson("/api/users/{$user->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User updated successfully',
            ]);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'phone' => '+9876543210',
        ]);
    }

    /** @test */
    public function admin_can_delete_user()
    {
        $user = User::factory()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->deleteJson("/api/users/{$user->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User deleted successfully',
            ]);

        $this->assertSoftDeleted('users', ['id' => $user->id]);
    }

    /** @test */
    public function admin_cannot_delete_themselves()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->deleteJson("/api/users/{$this->adminUser->id}");

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Cannot delete your own account',
            ]);
    }

    /** @test */
    public function admin_can_assign_role_to_user()
    {
        $user = User::factory()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson("/api/users/{$user->id}/assign-role", [
            'role' => 'editor',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Role assigned successfully',
            ]);

        $this->assertTrue($user->fresh()->hasRole('editor'));
    }

    /** @test */
    public function admin_can_create_user_group()
    {
        $groupData = [
            'name' => 'Test Group',
            'description' => 'A test user group',
            'color' => '#FF5722',
            'discount_percentage' => 12.5,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/user-groups', $groupData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'description',
                    'color',
                    'discount_percentage',
                ],
            ]);

        $this->assertDatabaseHas('user_groups', [
            'name' => 'Test Group',
            'color' => '#FF5722',
        ]);
    }

    /** @test */
    public function admin_can_assign_user_to_group()
    {
        $user = User::factory()->create();
        $group = UserGroup::factory()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson("/api/users/{$user->id}/assign-group", [
            'group_id' => $group->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User assigned to group successfully',
            ]);

        $this->assertTrue($group->hasUser($user));
    }

    /** @test */
    public function admin_can_search_users()
    {
        User::factory()->create(['name' => 'John Doe', 'email' => '<EMAIL>']);
        User::factory()->create(['name' => 'Jane Smith', 'email' => '<EMAIL>']);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/users?search=john');

        $response->assertStatus(200);
        
        $users = $response->json('data');
        $this->assertCount(1, array_filter($users, function ($user) {
            return str_contains(strtolower($user['name']), 'john');
        }));
    }

    /** @test */
    public function admin_can_filter_users_by_role()
    {
        $customer = User::factory()->create();
        $customer->assignRole('customer');

        $editor = User::factory()->create();
        $editor->assignRole('editor');

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/users?filters[role]=customer');

        $response->assertStatus(200);
        
        $users = $response->json('data');
        foreach ($users as $user) {
            $this->assertContains('customer', $user['roles']);
        }
    }

    /** @test */
    public function user_creation_requires_valid_data()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/users', [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123', // Too short
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'email', 'password']);
    }

    /** @test */
    public function cannot_create_user_with_duplicate_email()
    {
        $existingUser = User::factory()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/users', [
            'name' => 'New User',
            'email' => $existingUser->email,
            'password' => 'password123',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function non_admin_cannot_access_user_management()
    {
        $customer = User::factory()->create();
        $customer->assignRole('customer');
        $customerToken = JWTAuth::fromUser($customer);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $customerToken,
        ])->getJson('/api/users');

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_get_user_groups()
    {
        UserGroup::factory()->count(3)->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/user-groups');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'color',
                        'users_count',
                        'discount_percentage',
                    ],
                ],
            ]);
    }
}
