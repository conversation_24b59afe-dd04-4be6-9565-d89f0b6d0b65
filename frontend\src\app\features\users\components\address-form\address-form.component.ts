import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AddressService, UserAddress } from '../../services/address.service';

@Component({
  selector: 'app-address-form',
  templateUrl: './address-form.component.html',
  styleUrls: ['./address-form.component.scss']
})
export class AddressFormComponent implements OnInit {
  addressForm: FormGroup;
  loading = false;
  isEdit = false;
  countries: {code: string, name: string}[] = [];
  usStates: {code: string, name: string}[] = [];
  showStates = false;

  constructor(
    private fb: FormBuilder,
    private addressService: AddressService,
    private dialogRef: MatDialogRef<AddressFormComponent>,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: { address: UserAddress | null }
  ) {
    this.isEdit = !!data.address;
    this.countries = this.addressService.getCountries();
    this.usStates = this.addressService.getUSStates();
    
    this.addressForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.data.address) {
      this.populateForm(this.data.address);
    }
    
    // Watch country changes to show/hide states
    this.addressForm.get('country')?.valueChanges.subscribe(country => {
      this.showStates = country === 'US';
      if (!this.showStates) {
        this.addressForm.get('state')?.setValue('');
      }
    });
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    return this.fb.group({
      type: ['shipping', [Validators.required]],
      first_name: ['', [Validators.required, Validators.maxLength(255)]],
      last_name: ['', [Validators.required, Validators.maxLength(255)]],
      company: ['', [Validators.maxLength(255)]],
      address_line_1: ['', [Validators.required, Validators.maxLength(255)]],
      address_line_2: ['', [Validators.maxLength(255)]],
      city: ['', [Validators.required, Validators.maxLength(255)]],
      state: ['', [Validators.required, Validators.maxLength(255)]],
      postal_code: ['', [Validators.required, Validators.maxLength(20)]],
      country: ['US', [Validators.required]],
      phone: ['', [Validators.maxLength(20)]],
      is_default: [false]
    });
  }

  /**
   * Populate form with existing address data
   */
  private populateForm(address: UserAddress): void {
    this.addressForm.patchValue({
      type: address.type,
      first_name: address.first_name,
      last_name: address.last_name,
      company: address.company || '',
      address_line_1: address.address_line_1,
      address_line_2: address.address_line_2 || '',
      city: address.city,
      state: address.state,
      postal_code: address.postal_code,
      country: address.country,
      phone: address.phone || '',
      is_default: address.is_default
    });
    
    this.showStates = address.country === 'US';
  }

  /**
   * Submit form
   */
  onSubmit(): void {
    if (this.addressForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const formData = this.addressForm.value;
    
    // Validate address
    const validationErrors = this.addressService.validateAddress(formData);
    if (validationErrors.length > 0) {
      this.showError(validationErrors.join(', '));
      return;
    }

    this.loading = true;

    const request = this.isEdit 
      ? this.addressService.updateAddress(this.data.address!.id!, formData)
      : this.addressService.createAddress(formData);

    request.subscribe({
      next: (response) => {
        if (response.success) {
          this.dialogRef.close(response.data);
        } else {
          this.showError('Failed to save address');
        }
        this.loading = false;
      },
      error: (error) => {
        this.showError('Error saving address: ' + error.message);
        this.loading = false;
      }
    });
  }

  /**
   * Cancel form
   */
  onCancel(): void {
    this.dialogRef.close();
  }

  /**
   * Mark all form fields as touched
   */
  private markFormGroupTouched(): void {
    Object.keys(this.addressForm.controls).forEach(key => {
      const control = this.addressForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Get form control error message
   */
  getErrorMessage(controlName: string): string {
    const control = this.addressForm.get(controlName);
    
    if (control?.hasError('required')) {
      return `${this.getFieldLabel(controlName)} is required`;
    }
    
    if (control?.hasError('maxlength')) {
      const maxLength = control.errors?.['maxlength'].requiredLength;
      return `${this.getFieldLabel(controlName)} cannot exceed ${maxLength} characters`;
    }
    
    if (control?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    
    return '';
  }

  /**
   * Get field label for error messages
   */
  private getFieldLabel(controlName: string): string {
    const labels: {[key: string]: string} = {
      type: 'Address type',
      first_name: 'First name',
      last_name: 'Last name',
      company: 'Company',
      address_line_1: 'Address line 1',
      address_line_2: 'Address line 2',
      city: 'City',
      state: 'State',
      postal_code: 'Postal code',
      country: 'Country',
      phone: 'Phone number'
    };
    
    return labels[controlName] || controlName;
  }

  /**
   * Check if form control has error
   */
  hasError(controlName: string): boolean {
    const control = this.addressForm.get(controlName);
    return !!(control?.invalid && control?.touched);
  }

  /**
   * Get address types
   */
  getAddressTypes(): {value: string, label: string}[] {
    return [
      { value: 'shipping', label: 'Shipping Address' },
      { value: 'billing', label: 'Billing Address' },
      { value: 'both', label: 'Both Shipping & Billing' }
    ];
  }

  /**
   * Format postal code based on country
   */
  onPostalCodeChange(): void {
    const country = this.addressForm.get('country')?.value;
    const postalCode = this.addressForm.get('postal_code')?.value;
    
    if (country === 'US' && postalCode) {
      // Format US ZIP codes
      const cleaned = postalCode.replace(/\D/g, '');
      if (cleaned.length >= 5) {
        const formatted = cleaned.length > 5 
          ? `${cleaned.slice(0, 5)}-${cleaned.slice(5, 9)}`
          : cleaned.slice(0, 5);
        this.addressForm.get('postal_code')?.setValue(formatted);
      }
    }
  }

  /**
   * Format phone number
   */
  onPhoneChange(): void {
    const phone = this.addressForm.get('phone')?.value;
    if (phone) {
      // Basic phone formatting (US format)
      const cleaned = phone.replace(/\D/g, '');
      if (cleaned.length === 10) {
        const formatted = `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        this.addressForm.get('phone')?.setValue(formatted);
      }
    }
  }

  /**
   * Auto-fill city and state from postal code (US only)
   */
  onPostalCodeBlur(): void {
    const country = this.addressForm.get('country')?.value;
    const postalCode = this.addressForm.get('postal_code')?.value;
    
    if (country === 'US' && postalCode && postalCode.length >= 5) {
      // In a real application, you would call a postal code lookup service
      // For now, we'll just validate the format
      const zipRegex = /^\d{5}(-\d{4})?$/;
      if (!zipRegex.test(postalCode)) {
        this.addressForm.get('postal_code')?.setErrors({ 'invalidFormat': true });
      }
    }
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  /**
   * Get dialog title
   */
  getDialogTitle(): string {
    return this.isEdit ? 'Edit Address' : 'Add New Address';
  }

  /**
   * Get submit button text
   */
  getSubmitButtonText(): string {
    return this.isEdit ? 'Update Address' : 'Add Address';
  }
}
