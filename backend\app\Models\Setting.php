<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Cache key for settings.
     */
    const CACHE_KEY = 'app_settings';

    /**
     * Cache TTL in seconds (1 hour).
     */
    const CACHE_TTL = 3600;

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when settings are modified
        static::saved(function () {
            Cache::forget(self::CACHE_KEY);
        });

        static::deleted(function () {
            Cache::forget(self::CACHE_KEY);
        });
    }

    /**
     * Get a setting value by key.
     */
    public static function get(string $key, $default = null)
    {
        $settings = self::getAllCached();
        return $settings[$key] ?? $default;
    }

    /**
     * Set a setting value.
     */
    public static function set(string $key, $value, string $type = 'string', string $group = 'general'): void
    {
        self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group,
            ]
        );
    }

    /**
     * Get all settings cached.
     */
    public static function getAllCached(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_TTL, function () {
            return self::pluck('value', 'key')->toArray();
        });
    }

    /**
     * Get settings by group.
     */
    public static function getGroup(string $group): array
    {
        return self::where('group', $group)
            ->orderBy('sort_order')
            ->orderBy('key')
            ->get()
            ->mapWithKeys(function ($setting) {
                return [$setting->key => $setting->getTypedValue()];
            })
            ->toArray();
    }

    /**
     * Get public settings (for frontend).
     */
    public static function getPublic(): array
    {
        return self::where('is_public', true)
            ->get()
            ->mapWithKeys(function ($setting) {
                return [$setting->key => $setting->getTypedValue()];
            })
            ->toArray();
    }

    /**
     * Get typed value based on setting type.
     */
    public function getTypedValue()
    {
        switch ($this->type) {
            case 'boolean':
                return filter_var($this->value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $this->value;
            case 'float':
                return (float) $this->value;
            case 'array':
            case 'json':
                return json_decode($this->value, true);
            default:
                return $this->value;
        }
    }

    /**
     * Set multiple settings at once.
     */
    public static function setMany(array $settings): void
    {
        foreach ($settings as $key => $data) {
            if (is_array($data)) {
                self::set(
                    $key,
                    $data['value'],
                    $data['type'] ?? 'string',
                    $data['group'] ?? 'general'
                );
            } else {
                self::set($key, $data);
            }
        }
    }

    /**
     * Clear settings cache.
     */
    public static function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY);
    }

    /**
     * Get all settings grouped by group.
     */
    public static function getAllGrouped(): array
    {
        return self::orderBy('group')
            ->orderBy('sort_order')
            ->orderBy('key')
            ->get()
            ->groupBy('group')
            ->map(function ($settings) {
                return $settings->mapWithKeys(function ($setting) {
                    return [$setting->key => [
                        'value' => $setting->getTypedValue(),
                        'type' => $setting->type,
                        'description' => $setting->description,
                        'is_public' => $setting->is_public,
                    ]];
                });
            })
            ->toArray();
    }

    /**
     * Scope for public settings.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for settings by group.
     */
    public function scopeGroup($query, string $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Get setting groups.
     */
    public static function getGroups(): array
    {
        return self::distinct('group')
            ->orderBy('group')
            ->pluck('group')
            ->toArray();
    }

    /**
     * Check if a setting exists.
     */
    public static function has(string $key): bool
    {
        return self::where('key', $key)->exists();
    }

    /**
     * Remove a setting.
     */
    public static function remove(string $key): bool
    {
        return self::where('key', $key)->delete();
    }

    /**
     * Get default settings structure.
     */
    public static function getDefaults(): array
    {
        return [
            'general' => [
                'site_title' => 'E-Commerce Management System',
                'site_tagline' => 'Comprehensive e-commerce solution',
                'admin_email' => '<EMAIL>',
                'timezone' => 'UTC',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i:s',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'currency_position' => 'before',
            ],
            'design' => [
                'theme_primary_color' => '#3f51b5',
                'theme_secondary_color' => '#ff4081',
                'theme_background_color' => '#ffffff',
                'theme_text_color' => '#333333',
                'theme_font_family' => 'Roboto',
                'logo_header' => '',
                'logo_footer' => '',
                'logo_mobile' => '',
                'favicon' => '',
            ],
            'contact' => [
                'company_name' => 'RekTech',
                'company_phone' => '',
                'company_whatsapp' => '',
                'company_email' => '',
                'company_address' => '',
                'company_map_url' => '',
            ],
            'social' => [
                'facebook_url' => '',
                'twitter_url' => '',
                'instagram_url' => '',
                'youtube_url' => '',
                'linkedin_url' => '',
            ],
            'payment' => [
                'gpay_upi_id' => '',
                'razorpay_key_id' => '',
                'razorpay_key_secret' => '',
                'cod_enabled' => true,
                'min_order_amount' => 0,
            ],
            'analytics' => [
                'google_analytics_id' => '',
                'google_tag_manager_id' => '',
                'facebook_pixel_id' => '',
            ],
            'email' => [
                'mail_driver' => 'smtp',
                'mail_host' => '',
                'mail_port' => 587,
                'mail_username' => '',
                'mail_password' => '',
                'mail_encryption' => 'tls',
                'mail_from_address' => '',
                'mail_from_name' => '',
            ],
            'modules' => [
                'module_users_enabled' => true,
                'module_products_enabled' => true,
                'module_orders_enabled' => true,
                'module_coupons_enabled' => true,
                'module_analytics_enabled' => true,
                'module_media_enabled' => true,
                'module_invoices_enabled' => true,
            ],
        ];
    }
}
