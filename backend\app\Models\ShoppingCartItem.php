<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShoppingCartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'cart_id',
        'product_id',
        'product_variation_id',
        'quantity',
        'unit_price',
        'total_price',
        'product_options'
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'product_options' => 'array',
    ];

    /**
     * Get the cart that owns the item.
     */
    public function cart(): BelongsTo
    {
        return $this->belongsTo(ShoppingCart::class, 'cart_id');
    }

    /**
     * Get the product.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the product variation.
     */
    public function variation(): BelongsTo
    {
        return $this->belongsTo(ProductVariation::class, 'product_variation_id');
    }

    /**
     * Get the current price of the product.
     */
    public function getCurrentPriceAttribute(): float
    {
        if ($this->variation) {
            return $this->variation->current_price;
        }
        
        return $this->product->current_price;
    }

    /**
     * Check if price has changed since added to cart.
     */
    public function getHasPriceChangedAttribute(): bool
    {
        return $this->unit_price != $this->current_price;
    }

    /**
     * Get the price difference.
     */
    public function getPriceDifferenceAttribute(): float
    {
        return $this->current_price - $this->unit_price;
    }

    /**
     * Get the display name (product + variation).
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->product->name;
        
        if ($this->variation) {
            $name .= ' - ' . $this->variation->name;
        }
        
        return $name;
    }

    /**
     * Get the product image URL.
     */
    public function getImageUrlAttribute(): ?string
    {
        if ($this->variation && $this->variation->image) {
            return $this->variation->image_url;
        }
        
        return $this->product->main_image_url;
    }

    /**
     * Check if item is in stock.
     */
    public function getIsInStockAttribute(): bool
    {
        if ($this->variation) {
            return $this->variation->stock_quantity >= $this->quantity;
        }
        
        return $this->product->stock_quantity >= $this->quantity && 
               $this->product->stock_status === 'in_stock';
    }

    /**
     * Get available stock quantity.
     */
    public function getAvailableStockAttribute(): int
    {
        if ($this->variation) {
            return $this->variation->stock_quantity;
        }
        
        return $this->product->stock_quantity;
    }

    /**
     * Check if item is available for purchase.
     */
    public function isAvailable(): bool
    {
        if (!$this->product->is_active || $this->product->status !== 'published') {
            return false;
        }
        
        if ($this->variation && !$this->variation->is_active) {
            return false;
        }
        
        return $this->is_in_stock;
    }

    /**
     * Get availability status message.
     */
    public function getAvailabilityStatusAttribute(): string
    {
        if (!$this->product->is_active || $this->product->status !== 'published') {
            return 'Product no longer available';
        }
        
        if ($this->variation && !$this->variation->is_active) {
            return 'Variation no longer available';
        }
        
        if (!$this->is_in_stock) {
            return 'Insufficient stock';
        }
        
        return 'Available';
    }

    /**
     * Update quantity and recalculate total.
     */
    public function updateQuantity(int $quantity): void
    {
        $this->update([
            'quantity' => $quantity,
            'total_price' => $quantity * $this->unit_price
        ]);
        
        $this->cart->recalculateTotal();
    }

    /**
     * Update price to current product price.
     */
    public function updatePrice(): void
    {
        $currentPrice = $this->current_price;
        
        $this->update([
            'unit_price' => $currentPrice,
            'total_price' => $this->quantity * $currentPrice
        ]);
        
        $this->cart->recalculateTotal();
    }

    /**
     * Move item to wishlist.
     */
    public function moveToWishlist(int $wishlistId = null): array
    {
        $user = $this->cart->user;
        
        if (!$wishlistId) {
            $wishlist = $user->wishlists()->where('is_default', true)->first();
            if (!$wishlist) {
                $wishlist = $user->wishlists()->create([
                    'name' => 'My Wishlist',
                    'is_default' => true
                ]);
            }
        } else {
            $wishlist = $user->wishlists()->findOrFail($wishlistId);
        }
        
        try {
            $wishlistItem = $wishlist->addProduct(
                $this->product_id,
                $this->product_variation_id,
                'Moved from cart'
            );
            
            // Remove from cart
            $this->delete();
            $this->cart->recalculateTotal();
            
            return [
                'success' => true,
                'message' => 'Item moved to wishlist successfully',
                'wishlist_item' => $wishlistItem
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to move item to wishlist: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get item weight for shipping calculation.
     */
    public function getTotalWeightAttribute(): float
    {
        $weight = $this->variation ? $this->variation->weight : $this->product->weight;
        return ($weight ?? 0) * $this->quantity;
    }

    /**
     * Check if item requires shipping.
     */
    public function getRequiresShippingAttribute(): bool
    {
        return $this->product->requires_shipping ?? true;
    }

    /**
     * Scope for available items.
     */
    public function scopeAvailable($query)
    {
        return $query->whereHas('product', function ($q) {
            $q->where('status', 'published')
              ->where('stock_status', 'in_stock');
        });
    }

    /**
     * Scope for items with price changes.
     */
    public function scopePriceChanged($query)
    {
        return $query->whereRaw('unit_price != (SELECT current_price FROM products WHERE id = shopping_cart_items.product_id)');
    }
}
