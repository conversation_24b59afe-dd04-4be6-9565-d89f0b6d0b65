{"9": "Tab;", "10": "NewLine;", "33": "excl;", "34": "quot;", "35": "num;", "36": "dollar;", "37": "percnt;", "38": "amp;", "39": "apos;", "40": "lpar;", "41": "rpar;", "42": "midast;", "43": "plus;", "44": "comma;", "46": "period;", "47": "sol;", "58": "colon;", "59": "semi;", "60": "lt;", "61": "equals;", "62": "gt;", "63": "quest;", "64": "commat;", "91": "lsqb;", "92": "bsol;", "93": "rsqb;", "94": "Hat;", "95": "UnderBar;", "96": "grave;", "123": "lcub;", "124": "VerticalLine;", "125": "rcub;", "160": "NonBreakingSpace;", "161": "iexcl;", "162": "cent;", "163": "pound;", "164": "curren;", "165": "yen;", "166": "brvbar;", "167": "sect;", "168": "uml;", "169": "copy;", "170": "ordf;", "171": "laquo;", "172": "not;", "173": "shy;", "174": "reg;", "175": "strns;", "176": "deg;", "177": "pm;", "178": "sup2;", "179": "sup3;", "180": "DiacriticalAcute;", "181": "micro;", "182": "para;", "183": "middot;", "184": "<PERSON><PERSON><PERSON>;", "185": "sup1;", "186": "ordm;", "187": "raquo;", "188": "frac14;", "189": "half;", "190": "frac34;", "191": "iquest;", "192": "<PERSON><PERSON>;", "193": "Aacute;", "194": "Acirc;", "195": "<PERSON><PERSON>;", "196": "Auml;", "197": "<PERSON>ng;", "198": "AElig;", "199": "Ccedil;", "200": "<PERSON><PERSON>;", "201": "Eacute;", "202": "Ecirc;", "203": "Euml;", "204": "<PERSON><PERSON>;", "205": "I<PERSON>ute;", "206": "Icirc;", "207": "Iuml;", "208": "ETH;", "209": "Ntilde;", "210": "<PERSON><PERSON>;", "211": "Oacute;", "212": "Ocirc;", "213": "<PERSON><PERSON><PERSON>;", "214": "Ouml;", "215": "times;", "216": "<PERSON><PERSON><PERSON>;", "217": "<PERSON><PERSON>;", "218": "Uacute;", "219": "Ucirc;", "220": "Uuml;", "221": "Yacute;", "222": "THORN;", "223": "szlig;", "224": "agrave;", "225": "aacute;", "226": "acirc;", "227": "atilde;", "228": "auml;", "229": "aring;", "230": "aelig;", "231": "ccedil;", "232": "egrave;", "233": "eacute;", "234": "ecirc;", "235": "euml;", "236": "i<PERSON>;", "237": "iacute;", "238": "icirc;", "239": "iuml;", "240": "eth;", "241": "ntilde;", "242": "ograve;", "243": "oacute;", "244": "ocirc;", "245": "otilde;", "246": "ouml;", "247": "divide;", "248": "oslash;", "249": "u<PERSON>;", "250": "uacute;", "251": "ucirc;", "252": "uuml;", "253": "yacute;", "254": "thorn;", "255": "yuml;", "256": "Amacr;", "257": "amacr;", "258": "Abreve;", "259": "abreve;", "260": "Aogon;", "261": "aogon;", "262": "Cacute;", "263": "cacute;", "264": "Ccirc;", "265": "ccirc;", "266": "Cdot;", "267": "cdot;", "268": "C<PERSON>on;", "269": "ccaron;", "270": "D<PERSON><PERSON>;", "271": "dcaron;", "272": "Dstrok;", "273": "dstrok;", "274": "Emacr;", "275": "emacr;", "278": "<PERSON><PERSON>;", "279": "edot;", "280": "Eogon;", "281": "eogon;", "282": "Ecaron;", "283": "ecaron;", "284": "Gcirc;", "285": "gcirc;", "286": "Gbreve;", "287": "gbreve;", "288": "Gdot;", "289": "gdot;", "290": "Gcedil;", "292": "Hcirc;", "293": "hcirc;", "294": "Hstrok;", "295": "hstrok;", "296": "Itilde;", "297": "itilde;", "298": "Imacr;", "299": "imacr;", "302": "Iogon;", "303": "iogon;", "304": "Idot;", "305": "inodot;", "306": "IJlig;", "307": "ijlig;", "308": "Jcirc;", "309": "jcirc;", "310": "Kcedil;", "311": "kcedil;", "312": "kgreen;", "313": "<PERSON><PERSON>;", "314": "lacute;", "315": "Lcedil;", "316": "lcedil;", "317": "<PERSON><PERSON><PERSON>;", "318": "lcaron;", "319": "L<PERSON><PERSON><PERSON>;", "320": "lmidot;", "321": "Lstrok;", "322": "lstrok;", "323": "Nacute;", "324": "nacute;", "325": "Ncedil;", "326": "ncedil;", "327": "<PERSON><PERSON><PERSON>;", "328": "ncaron;", "329": "napos;", "330": "ENG;", "331": "eng;", "332": "Omacr;", "333": "omacr;", "336": "Odblac;", "337": "odblac;", "338": "OElig;", "339": "oelig;", "340": "Racute;", "341": "racute;", "342": "Rcedil;", "343": "rcedil;", "344": "<PERSON><PERSON><PERSON>;", "345": "rcaron;", "346": "Sacute;", "347": "sacute;", "348": "Scirc;", "349": "scirc;", "350": "Scedil;", "351": "scedil;", "352": "<PERSON><PERSON><PERSON>;", "353": "scaron;", "354": "Tcedil;", "355": "tcedil;", "356": "<PERSON><PERSON><PERSON>;", "357": "tcaron;", "358": "Tstrok;", "359": "tstrok;", "360": "Utilde;", "361": "utilde;", "362": "Umacr;", "363": "umacr;", "364": "Ubreve;", "365": "ubreve;", "366": "<PERSON><PERSON>;", "367": "uring;", "368": "Udblac;", "369": "udblac;", "370": "Uogon;", "371": "uogon;", "372": "Wcirc;", "373": "wcirc;", "374": "Ycirc;", "375": "ycirc;", "376": "Yuml;", "377": "Zacute;", "378": "zacute;", "379": "Zdot;", "380": "zdot;", "381": "<PERSON><PERSON><PERSON>;", "382": "zcaron;", "402": "fnof;", "437": "imped;", "501": "gacute;", "567": "jmath;", "710": "circ;", "711": "Hacek;", "728": "breve;", "729": "dot;", "730": "ring;", "731": "ogon;", "732": "tilde;", "733": "DiacriticalDoubleAcute;", "785": "DownBreve;", "913": "Alpha;", "914": "Beta;", "915": "Gamma;", "916": "Delta;", "917": "Epsilon;", "918": "Zeta;", "919": "Eta;", "920": "Theta;", "921": "Iota;", "922": "Kappa;", "923": "Lambda;", "924": "Mu;", "925": "Nu;", "926": "Xi;", "927": "Omicron;", "928": "Pi;", "929": "Rho;", "931": "Sigma;", "932": "Tau;", "933": "Upsilon;", "934": "Phi;", "935": "Chi;", "936": "Psi;", "937": "Omega;", "945": "alpha;", "946": "beta;", "947": "gamma;", "948": "delta;", "949": "epsilon;", "950": "zeta;", "951": "eta;", "952": "theta;", "953": "iota;", "954": "kappa;", "955": "lambda;", "956": "mu;", "957": "nu;", "958": "xi;", "959": "omicron;", "960": "pi;", "961": "rho;", "962": "varsigma;", "963": "sigma;", "964": "tau;", "965": "upsilon;", "966": "phi;", "967": "chi;", "968": "psi;", "969": "omega;", "977": "var<PERSON>ta;", "978": "upsih;", "981": "varphi;", "982": "varpi;", "988": "Gammad;", "989": "gammad;", "1008": "var<PERSON><PERSON>;", "1009": "varrho;", "1013": "varepsilon;", "1014": "bepsi;", "1025": "IOcy;", "1026": "DJcy;", "1027": "GJcy;", "1028": "<PERSON><PERSON><PERSON>;", "1029": "DScy;", "1030": "<PERSON><PERSON><PERSON>;", "1031": "YIcy;", "1032": "Jsercy;", "1033": "LJcy;", "1034": "NJcy;", "1035": "TSHcy;", "1036": "KJcy;", "1038": "Ubrcy;", "1039": "DZcy;", "1040": "Acy;", "1041": "Bcy;", "1042": "Vcy;", "1043": "Gcy;", "1044": "Dcy;", "1045": "IEcy;", "1046": "ZHcy;", "1047": "Z<PERSON>;", "1048": "<PERSON><PERSON>;", "1049": "Jcy;", "1050": "Kcy;", "1051": "Lcy;", "1052": "M<PERSON>;", "1053": "Ncy;", "1054": "<PERSON><PERSON>;", "1055": "Pcy;", "1056": "Rcy;", "1057": "<PERSON><PERSON>;", "1058": "Tcy;", "1059": "Ucy;", "1060": "Fcy;", "1061": "KHcy;", "1062": "TScy;", "1063": "CHcy;", "1064": "SHcy;", "1065": "SHCHcy;", "1066": "HARDcy;", "1067": "<PERSON><PERSON>;", "1068": "SOFTcy;", "1069": "E<PERSON>;", "1070": "YUcy;", "1071": "YAcy;", "1072": "acy;", "1073": "bcy;", "1074": "vcy;", "1075": "gcy;", "1076": "dcy;", "1077": "iecy;", "1078": "zhcy;", "1079": "zcy;", "1080": "icy;", "1081": "jcy;", "1082": "kcy;", "1083": "lcy;", "1084": "mcy;", "1085": "ncy;", "1086": "ocy;", "1087": "pcy;", "1088": "rcy;", "1089": "scy;", "1090": "tcy;", "1091": "ucy;", "1092": "fcy;", "1093": "khcy;", "1094": "tscy;", "1095": "chcy;", "1096": "shcy;", "1097": "shchcy;", "1098": "hardcy;", "1099": "ycy;", "1100": "softcy;", "1101": "ecy;", "1102": "yucy;", "1103": "yacy;", "1105": "iocy;", "1106": "djcy;", "1107": "gjcy;", "1108": "jukcy;", "1109": "dscy;", "1110": "iukcy;", "1111": "yicy;", "1112": "jsercy;", "1113": "ljcy;", "1114": "njcy;", "1115": "tshcy;", "1116": "kjcy;", "1118": "ubrcy;", "1119": "dzcy;", "8194": "ensp;", "8195": "emsp;", "8196": "emsp13;", "8197": "emsp14;", "8199": "numsp;", "8200": "puncsp;", "8201": "ThinSpace;", "8202": "VeryThinSpace;", "8203": "ZeroWidthSpace;", "8204": "zwnj;", "8205": "zwj;", "8206": "lrm;", "8207": "rlm;", "8208": "hyphen;", "8211": "ndash;", "8212": "mdash;", "8213": "horbar;", "8214": "Vert;", "8216": "OpenCurlyQuote;", "8217": "rsquor;", "8218": "sbquo;", "8220": "OpenCurlyDoubleQuote;", "8221": "rdquor;", "8222": "ldquor;", "8224": "dagger;", "8225": "ddagger;", "8226": "bullet;", "8229": "nldr;", "8230": "mldr;", "8240": "permil;", "8241": "pertenk;", "8242": "prime;", "8243": "Prime;", "8244": "tprime;", "8245": "bprime;", "8249": "lsaquo;", "8250": "rsaquo;", "8254": "OverBar;", "8257": "caret;", "8259": "hybull;", "8260": "frasl;", "8271": "bsemi;", "8279": "qprime;", "8287": "MediumSpace;", "8288": "NoBreak;", "8289": "ApplyFunction;", "8290": "it;", "8291": "InvisibleComma;", "8364": "euro;", "8411": "TripleDot;", "8412": "DotDot;", "8450": "<PERSON><PERSON>;", "8453": "incare;", "8458": "gscr;", "8459": "Hscr;", "8460": "Poincareplane;", "8461": "quaternions;", "8462": "planckh;", "8463": "plankv;", "8464": "Iscr;", "8465": "imagpart;", "8466": "Lscr;", "8467": "ell;", "8469": "Nopf;", "8470": "numero;", "8471": "copysr;", "8472": "wp;", "8473": "primes;", "8474": "rationals;", "8475": "Rscr;", "8476": "Rfr;", "8477": "Ropf;", "8478": "rx;", "8482": "trade;", "8484": "Zopf;", "8487": "mho;", "8488": "Zfr;", "8489": "iiota;", "8492": "Bscr;", "8493": "Cfr;", "8495": "escr;", "8496": "expectation;", "8497": "Fscr;", "8499": "phmmat;", "8500": "oscr;", "8501": "aleph;", "8502": "beth;", "8503": "gimel;", "8504": "daleth;", "8517": "DD;", "8518": "DifferentialD;", "8519": "exponentiale;", "8520": "ImaginaryI;", "8531": "frac13;", "8532": "frac23;", "8533": "frac15;", "8534": "frac25;", "8535": "frac35;", "8536": "frac45;", "8537": "frac16;", "8538": "frac56;", "8539": "frac18;", "8540": "frac38;", "8541": "frac58;", "8542": "frac78;", "8592": "slarr;", "8593": "uparrow;", "8594": "srarr;", "8595": "ShortDownArrow;", "8596": "leftrightarrow;", "8597": "varr;", "8598": "UpperLeftArrow;", "8599": "UpperRightArrow;", "8600": "searrow;", "8601": "swarrow;", "8602": "nleftarrow;", "8603": "n<PERSON><PERSON><PERSON>;", "8605": "rights<PERSON><PERSON>row;", "8606": "twoheadleftarrow;", "8607": "<PERSON><PERSON><PERSON>;", "8608": "two<PERSON><PERSON><PERSON><PERSON>;", "8609": "<PERSON><PERSON>;", "8610": "leftarrowtail;", "8611": "rightarrowtail;", "8612": "mapstoleft;", "8613": "UpTeeArrow;", "8614": "RightTeeArrow;", "8615": "mapstodown;", "8617": "larrhk;", "8618": "rarrhk;", "8619": "looparrowleft;", "8620": "rarrlp;", "8621": "leftright<PERSON><PERSON><PERSON><PERSON>;", "8622": "nleftrightarrow;", "8624": "lsh;", "8625": "rsh;", "8626": "ldsh;", "8627": "rdsh;", "8629": "crarr;", "8630": "curvearrowleft;", "8631": "<PERSON><PERSON><PERSON><PERSON>;", "8634": "olarr;", "8635": "orarr;", "8636": "lharu;", "8637": "lhard;", "8638": "upharpoonright;", "8639": "upharpoonleft;", "8640": "RightVector;", "8641": "rightharpoondown;", "8642": "RightDownVector;", "8643": "LeftDownVector;", "8644": "rlarr;", "8645": "UpArrowDownArrow;", "8646": "lrarr;", "8647": "llarr;", "8648": "uuarr;", "8649": "rrarr;", "8650": "downdownarrows;", "8651": "ReverseEquilibrium;", "8652": "rlhar;", "8653": "nLeftarrow;", "8654": "nLeftrightarrow;", "8655": "nRightarrow;", "8656": "Leftarrow;", "8657": "Uparrow;", "8658": "Rightarrow;", "8659": "Downarrow;", "8660": "Leftrightarrow;", "8661": "vArr;", "8662": "nwArr;", "8663": "neArr;", "8664": "seArr;", "8665": "swArr;", "8666": "Lleftarrow;", "8667": "<PERSON><PERSON><PERSON><PERSON>;", "8669": "zigrarr;", "8676": "LeftArrowBar;", "8677": "RightArrowBar;", "8693": "duarr;", "8701": "loarr;", "8702": "roarr;", "8703": "hoarr;", "8704": "forall;", "8705": "complement;", "8706": "PartialD;", "8707": "Exists;", "8708": "NotExists;", "8709": "varnothing;", "8711": "nabla;", "8712": "isinv;", "8713": "notinva;", "8715": "SuchThat;", "8716": "NotReverseElement;", "8719": "Product;", "8720": "Coproduct;", "8721": "sum;", "8722": "minus;", "8723": "mp;", "8724": "plusdo;", "8726": "ssetmn;", "8727": "lowast;", "8728": "SmallCircle;", "8730": "Sqrt;", "8733": "vprop;", "8734": "infin;", "8735": "angrt;", "8736": "angle;", "8737": "measuredangle;", "8738": "angsph;", "8739": "VerticalBar;", "8740": "nsmid;", "8741": "spar;", "8742": "nspar;", "8743": "wedge;", "8744": "vee;", "8745": "cap;", "8746": "cup;", "8747": "Integral;", "8748": "Int;", "8749": "tint;", "8750": "oint;", "8751": "DoubleContourIntegral;", "8752": "Cconint;", "8753": "cwint;", "8754": "cwconint;", "8755": "CounterClockwiseContourIntegral;", "8756": "therefore;", "8757": "because;", "8758": "ratio;", "8759": "Proportion;", "8760": "minusd;", "8762": "mDDot;", "8763": "homtht;", "8764": "<PERSON><PERSON>;", "8765": "bsim;", "8766": "mstpos;", "8767": "acd;", "8768": "wreath;", "8769": "nsim;", "8770": "esim;", "8771": "TildeEqual;", "8772": "nsimeq;", "8773": "TildeFullEqual;", "8774": "simne;", "8775": "NotTildeFullEqual;", "8776": "T<PERSON><PERSON><PERSON>;", "8777": "NotTildeTilde;", "8778": "approxeq;", "8779": "apid;", "8780": "bcong;", "8781": "CupCap;", "8782": "HumpDownHump;", "8783": "HumpEqual;", "8784": "esdot;", "8785": "eDot;", "8786": "fallingdotseq;", "8787": "risingdotseq;", "8788": "coloneq;", "8789": "eqcolon;", "8790": "eqcirc;", "8791": "cire;", "8793": "wedgeq;", "8794": "veeeq;", "8796": "trie;", "8799": "questeq;", "8800": "NotEqual;", "8801": "equiv;", "8802": "NotCongruent;", "8804": "leq;", "8805": "GreaterEqual;", "8806": "LessFullEqual;", "8807": "GreaterFullEqual;", "8808": "lneqq;", "8809": "gneqq;", "8810": "NestedLessLess;", "8811": "NestedGreaterGreater;", "8812": "twixt;", "8813": "NotCupCap;", "8814": "NotLess;", "8815": "NotGreater;", "8816": "NotLessEqual;", "8817": "NotGreaterEqual;", "8818": "lsim;", "8819": "gtrsim;", "8820": "NotLessTilde;", "8821": "NotGreaterTilde;", "8822": "lg;", "8823": "gtrless;", "8824": "ntlg;", "8825": "ntgl;", "8826": "Precedes;", "8827": "Succeeds;", "8828": "PrecedesSlantEqual;", "8829": "SucceedsSlantEqual;", "8830": "prsim;", "8831": "succsim;", "8832": "nprec;", "8833": "nsucc;", "8834": "subset;", "8835": "supset;", "8836": "nsub;", "8837": "nsup;", "8838": "SubsetEqual;", "8839": "supseteq;", "8840": "nsubseteq;", "8841": "nsupseteq;", "8842": "subsetneq;", "8843": "supsetneq;", "8845": "cupdot;", "8846": "uplus;", "8847": "SquareSubset;", "8848": "SquareSuperset;", "8849": "SquareSubsetEqual;", "8850": "SquareSupersetEqual;", "8851": "SquareIntersection;", "8852": "SquareUnion;", "8853": "oplus;", "8854": "ominus;", "8855": "otimes;", "8856": "osol;", "8857": "odot;", "8858": "ocir;", "8859": "oast;", "8861": "odash;", "8862": "plusb;", "8863": "minusb;", "8864": "timesb;", "8865": "sdotb;", "8866": "vdash;", "8867": "LeftTee;", "8868": "top;", "8869": "UpTee;", "8871": "models;", "8872": "vDash;", "8873": "Vdash;", "8874": "Vvdash;", "8875": "VDash;", "8876": "nvdash;", "8877": "nvDash;", "8878": "nVdash;", "8879": "nVDash;", "8880": "p<PERSON><PERSON>;", "8882": "vltri;", "8883": "vrtri;", "8884": "trianglelefteq;", "8885": "trianglerighteq;", "8886": "origof;", "8887": "imof;", "8888": "mumap;", "8889": "hercon;", "8890": "intercal;", "8891": "veebar;", "8893": "barvee;", "8894": "angrtvb;", "8895": "lrtri;", "8896": "xwedge;", "8897": "xvee;", "8898": "xcap;", "8899": "xcup;", "8900": "diamond;", "8901": "sdot;", "8902": "Star;", "8903": "divon<PERSON>;", "8904": "bowtie;", "8905": "ltimes;", "8906": "rtimes;", "8907": "lthree;", "8908": "rthree;", "8909": "bsime;", "8910": "cuvee;", "8911": "cuwed;", "8912": "Subset;", "8913": "<PERSON><PERSON><PERSON>;", "8914": "Cap;", "8915": "Cup;", "8916": "pitchfork;", "8917": "epar;", "8918": "ltdot;", "8919": "gtrdot;", "8920": "Ll;", "8921": "ggg;", "8922": "LessEqualGreater;", "8923": "gtreqless;", "8926": "curlyeqprec;", "8927": "curlyeqsucc;", "8928": "nprcue;", "8929": "nsccue;", "8930": "nsqsube;", "8931": "nsqsupe;", "8934": "lnsim;", "8935": "gnsim;", "8936": "p<PERSON><PERSON>;", "8937": "succnsim;", "8938": "ntriangleleft;", "8939": "ntriangleright;", "8940": "ntrianglelefteq;", "8941": "ntrianglerighteq;", "8942": "vellip;", "8943": "ctdot;", "8944": "utdot;", "8945": "dtdot;", "8946": "disin;", "8947": "isinsv;", "8948": "isins;", "8949": "isindot;", "8950": "notinvc;", "8951": "notinvb;", "8953": "isinE;", "8954": "nisd;", "8955": "xnis;", "8956": "nis;", "8957": "notnivc;", "8958": "notnivb;", "8965": "barwedge;", "8966": "doublebarwedge;", "8968": "LeftCeiling;", "8969": "RightCeiling;", "8970": "lfloor;", "8971": "RightFloor;", "8972": "drcrop;", "8973": "dlcrop;", "8974": "urcrop;", "8975": "ulcrop;", "8976": "bnot;", "8978": "profline;", "8979": "profsurf;", "8981": "telrec;", "8982": "target;", "8988": "ulcorner;", "8989": "urc<PERSON><PERSON>;", "8990": "llcorner;", "8991": "l<PERSON><PERSON><PERSON>;", "8994": "sfrown;", "8995": "ssmile;", "9005": "cylcty;", "9006": "profalar;", "9014": "topbot;", "9021": "ovbar;", "9023": "solbar;", "9084": "ang<PERSON>r;", "9136": "lmoustache;", "9137": "rmoustache;", "9140": "tbrk;", "9141": "UnderBracket;", "9142": "bbrktbrk;", "9180": "OverParenthesis;", "9181": "UnderParenthesis;", "9182": "OverBrace;", "9183": "UnderBrace;", "9186": "trpezium;", "9191": "elinters;", "9251": "blank;", "9416": "oS;", "9472": "HorizontalLine;", "9474": "boxv;", "9484": "boxdr;", "9488": "boxdl;", "9492": "boxur;", "9496": "boxul;", "9500": "boxvr;", "9508": "boxvl;", "9516": "boxhd;", "9524": "boxhu;", "9532": "boxvh;", "9552": "boxH;", "9553": "boxV;", "9554": "boxdR;", "9555": "boxDr;", "9556": "boxDR;", "9557": "boxdL;", "9558": "boxDl;", "9559": "boxDL;", "9560": "boxuR;", "9561": "boxUr;", "9562": "boxUR;", "9563": "boxuL;", "9564": "boxUl;", "9565": "boxUL;", "9566": "boxvR;", "9567": "boxVr;", "9568": "boxVR;", "9569": "boxvL;", "9570": "boxVl;", "9571": "boxVL;", "9572": "boxHd;", "9573": "boxhD;", "9574": "boxHD;", "9575": "boxHu;", "9576": "boxhU;", "9577": "boxHU;", "9578": "boxvH;", "9579": "boxVh;", "9580": "boxVH;", "9600": "uhblk;", "9604": "lhblk;", "9608": "block;", "9617": "blk14;", "9618": "blk12;", "9619": "blk34;", "9633": "square;", "9642": "squf;", "9643": "EmptyVerySmallSquare;", "9645": "rect;", "9646": "marker;", "9649": "fltns;", "9651": "xutri;", "9652": "utrif;", "9653": "utri;", "9656": "rtrif;", "9657": "triangleright;", "9661": "xdtri;", "9662": "dtrif;", "9663": "triangledown;", "9666": "ltrif;", "9667": "triangleleft;", "9674": "lozenge;", "9675": "cir;", "9708": "tridot;", "9711": "xcirc;", "9720": "ultri;", "9721": "urtri;", "9722": "lltri;", "9723": "EmptySmallSquare;", "9724": "FilledSmallSquare;", "9733": "starf;", "9734": "star;", "9742": "phone;", "9792": "female;", "9794": "male;", "9824": "spadesuit;", "9827": "clubsuit;", "9829": "heartsuit;", "9830": "diams;", "9834": "sung;", "9837": "flat;", "9838": "natural;", "9839": "sharp;", "10003": "checkmark;", "10007": "cross;", "10016": "maltese;", "10038": "sext;", "10072": "VerticalSeparator;", "10098": "lbbrk;", "10099": "rbbrk;", "10184": "b<PERSON><PERSON><PERSON>;", "10185": "suphsol;", "10214": "lobrk;", "10215": "robrk;", "10216": "LeftAngleBracket;", "10217": "RightAngleBracket;", "10218": "<PERSON>;", "10219": "Rang;", "10220": "loang;", "10221": "roang;", "10229": "xlarr;", "10230": "xrarr;", "10231": "xharr;", "10232": "xlArr;", "10233": "xrArr;", "10234": "xhArr;", "10236": "xmap;", "10239": "<PERSON><PERSON><PERSON><PERSON>;", "10498": "nvlArr;", "10499": "nvrArr;", "10500": "nvHarr;", "10501": "Map;", "10508": "lbarr;", "10509": "rbarr;", "10510": "l<PERSON>arr;", "10511": "r<PERSON><PERSON><PERSON>;", "10512": "<PERSON><PERSON><PERSON>;", "10513": "DDotrahd;", "10514": "UpArrowBar;", "10515": "DownArrowBar;", "10518": "Rarrtl;", "10521": "latail;", "10522": "ratail;", "10523": "lAtail;", "10524": "rAtail;", "10525": "larrfs;", "10526": "rarrfs;", "10527": "larrbfs;", "10528": "rarrbfs;", "10531": "nwarhk;", "10532": "nearhk;", "10533": "searhk;", "10534": "swarhk;", "10535": "nwnear;", "10536": "toea;", "10537": "tosa;", "10538": "swnwar;", "10547": "rarrc;", "10549": "cudarrr;", "10550": "ldca;", "10551": "rdca;", "10552": "cudarrl;", "10553": "larrpl;", "10556": "curarrm;", "10557": "cularrp;", "10565": "rarrpl;", "10568": "harrcir;", "10569": "<PERSON><PERSON><PERSON><PERSON><PERSON>;", "10570": "lurdshar;", "10571": "l<PERSON><PERSON><PERSON>;", "10574": "LeftRightVector;", "10575": "RightUpDownVector;", "10576": "DownLeftRightVector;", "10577": "LeftUpDownVector;", "10578": "LeftVectorBar;", "10579": "RightVectorBar;", "10580": "RightUpVectorBar;", "10581": "RightDownVectorBar;", "10582": "DownLeftVectorBar;", "10583": "DownRightVectorBar;", "10584": "LeftUpVectorBar;", "10585": "LeftDownVectorBar;", "10586": "LeftTeeVector;", "10587": "RightTeeVector;", "10588": "RightUpTeeVector;", "10589": "RightDownTeeVector;", "10590": "DownLeftTeeVector;", "10591": "DownRightTeeVector;", "10592": "LeftUpTeeVector;", "10593": "LeftDownTeeVector;", "10594": "lHar;", "10595": "uHar;", "10596": "rHar;", "10597": "dHar;", "10598": "luruhar;", "10599": "l<PERSON><PERSON><PERSON>;", "10600": "ruluhar;", "10601": "rdldhar;", "10602": "l<PERSON>ul;", "10603": "llhard;", "10604": "r<PERSON><PERSON>;", "10605": "lrhard;", "10606": "UpEquilibrium;", "10607": "ReverseUpEquilibrium;", "10608": "RoundImplies;", "10609": "erarr;", "10610": "sim<PERSON><PERSON>;", "10611": "larrsim;", "10612": "rar<PERSON><PERSON>;", "10613": "rarrap;", "10614": "ltlarr;", "10616": "gtrarr;", "10617": "subrarr;", "10619": "suplarr;", "10620": "lfisht;", "10621": "rfisht;", "10622": "ufisht;", "10623": "dfisht;", "10629": "lopar;", "10630": "ropar;", "10635": "lbrke;", "10636": "rbrke;", "10637": "lbrkslu;", "10638": "rbrksld;", "10639": "lbrksld;", "10640": "rbrkslu;", "10641": "langd;", "10642": "rangd;", "10643": "lparlt;", "10644": "rpargt;", "10645": "gtlPar;", "10646": "ltrPar;", "10650": "vzigzag;", "10652": "vang<PERSON>;", "10653": "angrtvbd;", "10660": "ange;", "10661": "range;", "10662": "dwangle;", "10663": "uwangle;", "10664": "angmsdaa;", "10665": "angmsdab;", "10666": "angmsdac;", "10667": "angmsdad;", "10668": "angmsdae;", "10669": "angmsdaf;", "10670": "angmsdag;", "10671": "angmsdah;", "10672": "bemptyv;", "10673": "demptyv;", "10674": "cemptyv;", "10675": "raemptyv;", "10676": "laemptyv;", "10677": "ohbar;", "10678": "omid;", "10679": "opar;", "10681": "operp;", "10683": "olcross;", "10684": "odsold;", "10686": "olcir;", "10687": "ofcir;", "10688": "olt;", "10689": "ogt;", "10690": "cirscir;", "10691": "cirE;", "10692": "solb;", "10693": "bsolb;", "10697": "boxbox;", "10701": "trisb;", "10702": "r<PERSON>ltri;", "10703": "LeftTriangleBar;", "10704": "RightTriangleBar;", "10716": "iinfin;", "10717": "infintie;", "10718": "nvinfin;", "10723": "eparsl;", "10724": "smeparsl;", "10725": "eqvparsl;", "10731": "lozf;", "10740": "RuleDelayed;", "10742": "dsol;", "10752": "xodot;", "10753": "xoplus;", "10754": "xotime;", "10756": "xuplus;", "10758": "xsqcup;", "10764": "qint;", "10765": "fpartint;", "10768": "cirfnint;", "10769": "awint;", "10770": "rppolint;", "10771": "scpolint;", "10772": "npolint;", "10773": "pointint;", "10774": "quatint;", "10775": "intlarhk;", "10786": "pluscir;", "10787": "plusacir;", "10788": "simplus;", "10789": "plusdu;", "10790": "plussim;", "10791": "plustwo;", "10793": "mcomma;", "10794": "minusdu;", "10797": "loplus;", "10798": "roplus;", "10799": "Cross;", "10800": "timesd;", "10801": "timesbar;", "10803": "smashp;", "10804": "lotimes;", "10805": "rotimes;", "10806": "otimesas;", "10807": "Otimes;", "10808": "odiv;", "10809": "triplus;", "10810": "triminus;", "10811": "tritime;", "10812": "iprod;", "10815": "amalg;", "10816": "capdot;", "10818": "ncup;", "10819": "ncap;", "10820": "capand;", "10821": "cupor;", "10822": "cupcap;", "10823": "capcup;", "10824": "cupbrcap;", "10825": "capbrcup;", "10826": "cupcup;", "10827": "capcap;", "10828": "ccups;", "10829": "ccaps;", "10832": "ccupssm;", "10835": "And;", "10836": "Or;", "10837": "andand;", "10838": "oror;", "10839": "orslope;", "10840": "andslope;", "10842": "andv;", "10843": "orv;", "10844": "andd;", "10845": "ord;", "10847": "wedbar;", "10854": "sdote;", "10858": "simdot;", "10861": "congdot;", "10862": "easter;", "10863": "apacir;", "10864": "apE;", "10865": "eplus;", "10866": "pluse;", "10867": "Esim;", "10868": "Colone;", "10869": "Equal;", "10871": "eDDot;", "10872": "equivDD;", "10873": "ltcir;", "10874": "gtcir;", "10875": "ltquest;", "10876": "gtquest;", "10877": "LessSlantEqual;", "10878": "GreaterSlantEqual;", "10879": "lesdo<PERSON>;", "10880": "gesdot;", "10881": "les<PERSON><PERSON>;", "10882": "gesdoto;", "10883": "les<PERSON><PERSON>;", "10884": "gesdotol;", "10885": "lessapprox;", "10886": "gtrapprox;", "10887": "lneq;", "10888": "gneq;", "10889": "lnapprox;", "10890": "gnapprox;", "10891": "lesseqqgtr;", "10892": "gtreqqless;", "10893": "lsime;", "10894": "gsime;", "10895": "lsimg;", "10896": "gsiml;", "10897": "lgE;", "10898": "glE;", "10899": "lesges;", "10900": "gesles;", "10901": "eqslantless;", "10902": "eqslantgtr;", "10903": "elsdot;", "10904": "egsdot;", "10905": "el;", "10906": "eg;", "10909": "siml;", "10910": "simg;", "10911": "simlE;", "10912": "simgE;", "10913": "LessLess;", "10914": "GreaterGreater;", "10916": "glj;", "10917": "gla;", "10918": "ltcc;", "10919": "gtcc;", "10920": "lescc;", "10921": "gescc;", "10922": "smt;", "10923": "lat;", "10924": "smte;", "10925": "late;", "10926": "bumpE;", "10927": "preceq;", "10928": "succeq;", "10931": "prE;", "10932": "scE;", "10933": "prnE;", "10934": "succneqq;", "10935": "precapprox;", "10936": "succapprox;", "10937": "prnap;", "10938": "succnapprox;", "10939": "Pr;", "10940": "Sc;", "10941": "subdot;", "10942": "supdot;", "10943": "subplus;", "10944": "supplus;", "10945": "submult;", "10946": "supmult;", "10947": "subedot;", "10948": "supedot;", "10949": "subseteqq;", "10950": "supseteqq;", "10951": "subsim;", "10952": "supsim;", "10955": "subsetneqq;", "10956": "supsetneqq;", "10959": "csub;", "10960": "csup;", "10961": "csube;", "10962": "csupe;", "10963": "subsup;", "10964": "supsub;", "10965": "subsub;", "10966": "supsup;", "10967": "<PERSON><PERSON><PERSON>;", "10968": "supdsub;", "10969": "forkv;", "10970": "topfork;", "10971": "mlcp;", "10980": "DoubleLeftTee;", "10982": "Vdashl;", "10983": "Barv;", "10984": "vBar;", "10985": "vBarv;", "10987": "Vbar;", "10988": "Not;", "10989": "bNot;", "10990": "rnmid;", "10991": "cirmid;", "10992": "midcir;", "10993": "topcir;", "10994": "nhpar;", "10995": "parsim;", "11005": "parsl;", "64256": "fflig;", "64257": "filig;", "64258": "fllig;", "64259": "ffilig;", "64260": "ffllig;"}