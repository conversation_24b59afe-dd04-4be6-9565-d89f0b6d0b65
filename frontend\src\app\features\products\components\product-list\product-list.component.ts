import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SelectionModel } from '@angular/cdk/collections';
import { Router } from '@angular/router';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

import { ProductService, Product, Category, ProductFilters } from '../../services/product.service';
import { LoadingService } from '../../../../core/services/loading.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-product-list',
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss']
})
export class ProductListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'select',
    'image',
    'name',
    'sku',
    'category',
    'price',
    'stock',
    'status',
    'featured',
    'created_at',
    'actions'
  ];

  dataSource = new MatTableDataSource<Product>();
  selection = new SelectionModel<Product>(true, []);
  
  // Filters
  filters: ProductFilters = {
    per_page: 20,
    page: 1,
    sort_by: 'created_at',
    sort_order: 'desc'
  };

  categories: Category[] = [];
  searchSubject = new Subject<string>();
  
  // Pagination
  totalItems = 0;
  pageSize = 20;
  pageSizeOptions = [10, 20, 50, 100];

  // Loading states
  loading = false;
  bulkActionLoading = false;

  // Filter options
  statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'published', label: 'Published' },
    { value: 'draft', label: 'Draft' },
    { value: 'archived', label: 'Archived' }
  ];

  stockStatusOptions = [
    { value: '', label: 'All Stock Status' },
    { value: 'in_stock', label: 'In Stock' },
    { value: 'out_of_stock', label: 'Out of Stock' },
    { value: 'on_backorder', label: 'On Backorder' }
  ];

  constructor(
    private productService: ProductService,
    private loadingService: LoadingService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCategories();
    this.loadProducts();
    this.setupSearch();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    // Handle sorting
    this.sort.sortChange.subscribe(() => {
      this.filters.sort_by = this.sort.active;
      this.filters.sort_order = this.sort.direction as 'asc' | 'desc';
      this.filters.page = 1;
      this.loadProducts();
    });

    // Handle pagination
    this.paginator.page.subscribe(() => {
      this.filters.page = this.paginator.pageIndex + 1;
      this.filters.per_page = this.paginator.pageSize;
      this.loadProducts();
    });
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.filters.search = searchTerm;
      this.filters.page = 1;
      this.loadProducts();
    });
  }

  private loadCategories(): void {
    this.productService.getCategories().subscribe({
      next: (response) => {
        if (response.success) {
          this.categories = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
  }

  loadProducts(): void {
    this.loading = true;
    this.loadingService.setLoading(true);

    this.productService.getProducts(this.filters).subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.data;
          this.totalItems = response.meta.total;
          
          // Update paginator
          this.paginator.length = this.totalItems;
          this.paginator.pageIndex = response.meta.current_page - 1;
          this.paginator.pageSize = response.meta.per_page;
        }
        this.loading = false;
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.snackBar.open('Error loading products', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loading = false;
        this.loadingService.setLoading(false);
      }
    });
  }

  onSearch(searchTerm: string): void {
    this.searchSubject.next(searchTerm);
  }

  onFilterChange(): void {
    this.filters.page = 1;
    this.loadProducts();
  }

  clearFilters(): void {
    this.filters = {
      per_page: 20,
      page: 1,
      sort_by: 'created_at',
      sort_order: 'desc'
    };
    this.loadProducts();
  }

  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  // Navigation methods
  createProduct(): void {
    this.router.navigate(['/products/create']);
  }

  editProduct(product: Product): void {
    this.router.navigate(['/products', product.id, 'edit']);
  }

  viewProduct(product: Product): void {
    this.router.navigate(['/products', product.id]);
  }

  duplicateProduct(product: Product): void {
    this.loadingService.setLoading(true);

    this.productService.duplicateProduct(product.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Product duplicated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadProducts();
        }
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error duplicating product:', error);
        this.snackBar.open('Error duplicating product', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }

  deleteProduct(product: Product): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Product',
        message: `Are you sure you want to delete "${product.name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadingService.setLoading(true);

        this.productService.deleteProduct(product.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Product deleted successfully', 'Close', {
                duration: 3000,
                panelClass: ['success-snackbar']
              });
              this.loadProducts();
            }
            this.loadingService.setLoading(false);
          },
          error: (error) => {
            console.error('Error deleting product:', error);
            this.snackBar.open('Error deleting product', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            this.loadingService.setLoading(false);
          }
        });
      }
    });
  }

  // Bulk actions
  bulkUpdateStatus(status: string): void {
    if (this.selection.selected.length === 0) {
      this.snackBar.open('Please select products to update', 'Close', {
        duration: 3000,
        panelClass: ['warning-snackbar']
      });
      return;
    }

    const productIds = this.selection.selected.map(product => product.id);
    this.bulkActionLoading = true;

    this.productService.bulkUpdateStatus(productIds, status).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(`${productIds.length} products updated successfully`, 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.selection.clear();
          this.loadProducts();
        }
        this.bulkActionLoading = false;
      },
      error: (error) => {
        console.error('Error updating products:', error);
        this.snackBar.open('Error updating products', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.bulkActionLoading = false;
      }
    });
  }

  bulkDelete(): void {
    if (this.selection.selected.length === 0) {
      this.snackBar.open('Please select products to delete', 'Close', {
        duration: 3000,
        panelClass: ['warning-snackbar']
      });
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Products',
        message: `Are you sure you want to delete ${this.selection.selected.length} selected products? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Implementation would require a bulk delete endpoint
        this.snackBar.open('Bulk delete functionality coming soon', 'Close', {
          duration: 3000,
          panelClass: ['info-snackbar']
        });
      }
    });
  }

  // Utility methods
  getCategoryName(categoryId: number): string {
    const category = this.categories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Unknown';
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'published': return 'primary';
      case 'draft': return 'accent';
      case 'archived': return 'warn';
      default: return '';
    }
  }

  getStockStatusColor(stockStatus: string): string {
    switch (stockStatus) {
      case 'in_stock': return 'primary';
      case 'out_of_stock': return 'warn';
      case 'on_backorder': return 'accent';
      default: return '';
    }
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  // Export functionality
  exportProducts(): void {
    this.loadingService.setLoading(true);

    this.productService.exportProducts('csv').subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `products-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        window.URL.revokeObjectURL(url);
        
        this.snackBar.open('Products exported successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadingService.setLoading(false);
      },
      error: (error) => {
        console.error('Error exporting products:', error);
        this.snackBar.open('Error exporting products', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.loadingService.setLoading(false);
      }
    });
  }
}
