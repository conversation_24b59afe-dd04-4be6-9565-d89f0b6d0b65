<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaxRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'rate',
        'type',
        'country',
        'state',
        'city',
        'postal_code',
        'is_compound',
        'priority',
        'is_active',
        'effective_from',
        'effective_to',
        'description',
        'applicable_categories',
        'exempt_categories'
    ];

    protected $casts = [
        'rate' => 'decimal:4',
        'is_compound' => 'boolean',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'applicable_categories' => 'array',
        'exempt_categories' => 'array',
    ];

    /**
     * Calculate tax amount for given parameters.
     */
    public function calculateTax(float $amount, array $productCategories = []): float
    {
        // Check if tax rate is active and within effective dates
        if (!$this->isEffective()) {
            return 0;
        }

        // Check category applicability
        if (!$this->isApplicableToCategories($productCategories)) {
            return 0;
        }

        if ($this->type === 'percentage') {
            return $amount * $this->rate;
        } elseif ($this->type === 'fixed') {
            return $this->rate;
        }

        return 0;
    }

    /**
     * Check if tax rate is currently effective.
     */
    public function isEffective(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now()->toDateString();

        if ($this->effective_from && $now < $this->effective_from->toDateString()) {
            return false;
        }

        if ($this->effective_to && $now > $this->effective_to->toDateString()) {
            return false;
        }

        return true;
    }

    /**
     * Check if tax rate applies to given categories.
     */
    public function isApplicableToCategories(array $categories): bool
    {
        // If exempt categories are specified and any category is exempt, not applicable
        if ($this->exempt_categories) {
            foreach ($categories as $category) {
                if (in_array($category, $this->exempt_categories)) {
                    return false;
                }
            }
        }

        // If applicable categories are specified, at least one category must be applicable
        if ($this->applicable_categories) {
            foreach ($categories as $category) {
                if (in_array($category, $this->applicable_categories)) {
                    return true;
                }
            }
            return false;
        }

        return true;
    }

    /**
     * Check if tax rate matches address.
     */
    public function matchesAddress(array $address): bool
    {
        // Country is required
        if ($this->country !== ($address['country'] ?? null)) {
            return false;
        }

        // State check (if specified)
        if ($this->state && $this->state !== ($address['state'] ?? null)) {
            return false;
        }

        // City check (if specified)
        if ($this->city && $this->city !== ($address['city'] ?? null)) {
            return false;
        }

        // Postal code check (if specified)
        if ($this->postal_code && $this->postal_code !== ($address['postal_code'] ?? null)) {
            return false;
        }

        return true;
    }

    /**
     * Get formatted rate for display.
     */
    public function getFormattedRateAttribute(): string
    {
        if ($this->type === 'percentage') {
            return number_format($this->rate * 100, 2) . '%';
        } elseif ($this->type === 'fixed') {
            return '$' . number_format($this->rate, 2);
        }

        return (string) $this->rate;
    }

    /**
     * Get location description.
     */
    public function getLocationDescriptionAttribute(): string
    {
        $parts = [$this->country];

        if ($this->state) {
            $parts[] = $this->state;
        }

        if ($this->city) {
            $parts[] = $this->city;
        }

        if ($this->postal_code) {
            $parts[] = $this->postal_code;
        }

        return implode(', ', $parts);
    }

    /**
     * Scope for active tax rates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for effective tax rates.
     */
    public function scopeEffective($query)
    {
        $now = now()->toDateString();
        
        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('effective_from')
                  ->orWhere('effective_from', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('effective_to')
                  ->orWhere('effective_to', '>=', $now);
            });
    }

    /**
     * Scope for tax rates matching address.
     */
    public function scopeForAddress($query, array $address)
    {
        return $query->where('country', $address['country'] ?? null)
            ->where(function ($q) use ($address) {
                $q->whereNull('state')
                  ->orWhere('state', $address['state'] ?? null);
            })
            ->where(function ($q) use ($address) {
                $q->whereNull('city')
                  ->orWhere('city', $address['city'] ?? null);
            })
            ->where(function ($q) use ($address) {
                $q->whereNull('postal_code')
                  ->orWhere('postal_code', $address['postal_code'] ?? null);
            });
    }

    /**
     * Scope ordered by priority.
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority')->orderBy('name');
    }

    /**
     * Calculate total tax for order.
     */
    public static function calculateOrderTax(float $subtotal, array $address, array $items = []): array
    {
        $taxRates = static::effective()
            ->forAddress($address)
            ->byPriority()
            ->get();

        $taxes = [];
        $totalTax = 0;
        $taxableAmount = $subtotal;

        foreach ($taxRates as $taxRate) {
            // Get categories for this tax calculation
            $categories = collect($items)->pluck('category')->unique()->toArray();
            
            if (!$taxRate->isApplicableToCategories($categories)) {
                continue;
            }

            $taxAmount = $taxRate->calculateTax($taxableAmount, $categories);
            
            if ($taxAmount > 0) {
                $taxes[] = [
                    'name' => $taxRate->name,
                    'code' => $taxRate->code,
                    'rate' => $taxRate->formatted_rate,
                    'amount' => $taxAmount,
                    'is_compound' => $taxRate->is_compound,
                ];

                $totalTax += $taxAmount;

                // If compound tax, add to taxable amount for next calculations
                if ($taxRate->is_compound) {
                    $taxableAmount += $taxAmount;
                }
            }
        }

        return [
            'taxes' => $taxes,
            'total_tax' => $totalTax,
            'taxable_amount' => $subtotal,
        ];
    }

    /**
     * Get tax rates for country.
     */
    public static function getForCountry(string $country): \Illuminate\Database\Eloquent\Collection
    {
        return static::effective()
            ->where('country', $country)
            ->byPriority()
            ->get();
    }

    /**
     * Get default tax rate for country.
     */
    public static function getDefaultForCountry(string $country): ?self
    {
        return static::effective()
            ->where('country', $country)
            ->whereNull('state')
            ->whereNull('city')
            ->whereNull('postal_code')
            ->byPriority()
            ->first();
    }

    /**
     * Validate tax rate configuration.
     */
    public function validateConfiguration(): array
    {
        $errors = [];

        if ($this->rate < 0) {
            $errors[] = 'Tax rate cannot be negative';
        }

        if ($this->type === 'percentage' && $this->rate > 1) {
            $errors[] = 'Percentage tax rate should be between 0 and 1 (e.g., 0.08 for 8%)';
        }

        if ($this->effective_from && $this->effective_to && $this->effective_from > $this->effective_to) {
            $errors[] = 'Effective from date cannot be after effective to date';
        }

        if (!in_array($this->country, ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE'])) {
            // Add more countries as needed
            $errors[] = 'Country code not supported';
        }

        return $errors;
    }
}
