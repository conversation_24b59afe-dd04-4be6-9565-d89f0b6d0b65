import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

export interface Setting {
  id: number;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json' | 'text' | 'email' | 'url' | 'file';
  group: string;
  name: string;
  description?: string;
  is_public: boolean;
  validation_rules?: string;
  options?: any;
  created_at: string;
  updated_at: string;
}

export interface SettingGroup {
  name: string;
  label: string;
  description?: string;
  icon: string;
  settings: Setting[];
}

export interface GeneralSettings {
  app_name: string;
  app_description: string;
  app_url: string;
  app_logo: string;
  app_favicon: string;
  contact_email: string;
  contact_phone: string;
  contact_address: string;
  timezone: string;
  date_format: string;
  time_format: string;
  currency: string;
  currency_symbol: string;
  currency_position: 'before' | 'after';
  decimal_places: number;
  thousand_separator: string;
  decimal_separator: string;
}

export interface EcommerceSettings {
  enable_guest_checkout: boolean;
  require_account_verification: boolean;
  auto_approve_reviews: boolean;
  enable_wishlist: boolean;
  enable_compare: boolean;
  enable_coupons: boolean;
  enable_inventory_tracking: boolean;
  low_stock_threshold: number;
  out_of_stock_threshold: number;
  enable_backorders: boolean;
  enable_reviews: boolean;
  max_review_rating: number;
  enable_product_questions: boolean;
  enable_related_products: boolean;
  max_related_products: number;
}

export interface EmailSettings {
  mail_driver: string;
  mail_host: string;
  mail_port: number;
  mail_username: string;
  mail_password: string;
  mail_encryption: string;
  mail_from_address: string;
  mail_from_name: string;
  enable_email_notifications: boolean;
  enable_order_confirmation: boolean;
  enable_order_status_updates: boolean;
  enable_low_stock_alerts: boolean;
  enable_new_user_welcome: boolean;
  enable_password_reset: boolean;
}

export interface PaymentSettings {
  default_payment_method: string;
  enable_cod: boolean;
  enable_bank_transfer: boolean;
  enable_razorpay: boolean;
  razorpay_key_id: string;
  razorpay_key_secret: string;
  razorpay_webhook_secret: string;
  enable_stripe: boolean;
  stripe_publishable_key: string;
  stripe_secret_key: string;
  stripe_webhook_secret: string;
  enable_paypal: boolean;
  paypal_client_id: string;
  paypal_client_secret: string;
  paypal_mode: 'sandbox' | 'live';
}

export interface ShippingSettings {
  enable_shipping: boolean;
  default_shipping_method: string;
  free_shipping_threshold: number;
  enable_local_pickup: boolean;
  enable_flat_rate: boolean;
  flat_rate_cost: number;
  enable_weight_based: boolean;
  weight_unit: 'kg' | 'lb';
  dimension_unit: 'cm' | 'in';
  enable_shipping_calculator: boolean;
  enable_shipping_zones: boolean;
}

export interface TaxSettings {
  enable_tax: boolean;
  tax_calculation: 'inclusive' | 'exclusive';
  default_tax_rate: number;
  tax_display_cart: 'including' | 'excluding' | 'both';
  tax_display_shop: 'including' | 'excluding' | 'both';
  enable_tax_by_location: boolean;
  tax_based_on: 'shipping' | 'billing' | 'shop';
  enable_compound_tax: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private readonly API_URL = environment.apiUrl;
  private settingsSubject = new BehaviorSubject<Setting[]>([]);
  public settings$ = this.settingsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all settings
   */
  getSettings(): Observable<ApiResponse<Setting[]>> {
    return this.http.get<ApiResponse<Setting[]>>(`${this.API_URL}/settings`).pipe(
      tap(response => {
        if (response.success) {
          this.settingsSubject.next(response.data);
        }
      })
    );
  }

  /**
   * Get settings grouped by category
   */
  getSettingsGrouped(): Observable<ApiResponse<SettingGroup[]>> {
    return this.http.get<ApiResponse<SettingGroup[]>>(`${this.API_URL}/settings/grouped`);
  }

  /**
   * Get settings by group
   */
  getSettingsByGroup(group: string): Observable<ApiResponse<Setting[]>> {
    return this.http.get<ApiResponse<Setting[]>>(`${this.API_URL}/settings/group/${group}`);
  }

  /**
   * Get single setting by key
   */
  getSetting(key: string): Observable<ApiResponse<Setting>> {
    return this.http.get<ApiResponse<Setting>>(`${this.API_URL}/settings/${key}`);
  }

  /**
   * Update multiple settings
   */
  updateSettings(settings: { [key: string]: any }): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/settings/bulk-update`, { settings }).pipe(
      tap(response => {
        if (response.success) {
          // Refresh settings cache
          this.getSettings().subscribe();
        }
      })
    );
  }

  /**
   * Update single setting
   */
  updateSetting(key: string, value: any): Observable<ApiResponse<Setting>> {
    return this.http.put<ApiResponse<Setting>>(`${this.API_URL}/settings/${key}`, { value }).pipe(
      tap(response => {
        if (response.success) {
          // Update local cache
          const currentSettings = this.settingsSubject.value;
          const updatedSettings = currentSettings.map(setting => 
            setting.key === key ? { ...setting, value } : setting
          );
          this.settingsSubject.next(updatedSettings);
        }
      })
    );
  }

  /**
   * Reset settings to default values
   */
  resetSettings(group?: string): Observable<ApiResponse<any>> {
    const url = group ? `${this.API_URL}/settings/reset/${group}` : `${this.API_URL}/settings/reset`;
    return this.http.post<ApiResponse<any>>(url, {}).pipe(
      tap(response => {
        if (response.success) {
          // Refresh settings cache
          this.getSettings().subscribe();
        }
      })
    );
  }

  /**
   * Upload file for setting
   */
  uploadFile(key: string, file: File): Observable<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('key', key);

    return this.http.post<ApiResponse<any>>(`${this.API_URL}/settings/upload`, formData);
  }

  /**
   * Test email configuration
   */
  testEmailConfiguration(emailSettings: Partial<EmailSettings>): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/settings/test-email`, emailSettings);
  }

  /**
   * Test payment gateway configuration
   */
  testPaymentGateway(gateway: string, settings: any): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/settings/test-payment`, {
      gateway,
      settings
    });
  }

  /**
   * Get available timezones
   */
  getTimezones(): Observable<ApiResponse<{ [key: string]: string }>> {
    return this.http.get<ApiResponse<{ [key: string]: string }>>(`${this.API_URL}/settings/timezones`);
  }

  /**
   * Get available currencies
   */
  getCurrencies(): Observable<ApiResponse<{ [key: string]: any }>> {
    return this.http.get<ApiResponse<{ [key: string]: any }>>(`${this.API_URL}/settings/currencies`);
  }

  /**
   * Get available countries
   */
  getCountries(): Observable<ApiResponse<{ [key: string]: string }>> {
    return this.http.get<ApiResponse<{ [key: string]: string }>>(`${this.API_URL}/settings/countries`);
  }

  /**
   * Export settings
   */
  exportSettings(): Observable<Blob> {
    return this.http.get(`${this.API_URL}/settings/export`, {
      responseType: 'blob'
    });
  }

  /**
   * Import settings
   */
  importSettings(file: File): Observable<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<ApiResponse<any>>(`${this.API_URL}/settings/import`, formData).pipe(
      tap(response => {
        if (response.success) {
          // Refresh settings cache
          this.getSettings().subscribe();
        }
      })
    );
  }

  /**
   * Clear cache
   */
  clearCache(): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/settings/clear-cache`, {});
  }

  /**
   * Get setting value by key from cache
   */
  getSettingValue(key: string): any {
    const settings = this.settingsSubject.value;
    const setting = settings.find(s => s.key === key);
    return setting ? setting.value : null;
  }

  /**
   * Get setting groups configuration
   */
  getSettingGroups(): SettingGroup[] {
    return [
      {
        name: 'general',
        label: 'General',
        description: 'Basic application settings',
        icon: 'settings',
        settings: []
      },
      {
        name: 'ecommerce',
        label: 'E-commerce',
        description: 'Online store configuration',
        icon: 'store',
        settings: []
      },
      {
        name: 'email',
        label: 'Email',
        description: 'Email server and notification settings',
        icon: 'email',
        settings: []
      },
      {
        name: 'payment',
        label: 'Payment',
        description: 'Payment gateway configuration',
        icon: 'payment',
        settings: []
      },
      {
        name: 'shipping',
        label: 'Shipping',
        description: 'Shipping methods and rates',
        icon: 'local_shipping',
        settings: []
      },
      {
        name: 'tax',
        label: 'Tax',
        description: 'Tax calculation and display settings',
        icon: 'receipt',
        settings: []
      },
      {
        name: 'security',
        label: 'Security',
        description: 'Security and authentication settings',
        icon: 'security',
        settings: []
      },
      {
        name: 'advanced',
        label: 'Advanced',
        description: 'Advanced system configuration',
        icon: 'tune',
        settings: []
      }
    ];
  }
}
