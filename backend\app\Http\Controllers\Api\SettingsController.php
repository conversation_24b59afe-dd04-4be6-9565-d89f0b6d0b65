<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use App\Models\Setting;

class SettingsController extends BaseController
{
    /**
     * Get all settings.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $group = $request->get('group');
            
            if ($group) {
                $settings = Setting::where('group', $group)->get();
            } else {
                $settings = Setting::all();
            }

            $formattedSettings = $settings->groupBy('group')->map(function ($groupSettings) {
                return $groupSettings->mapWithKeys(function ($setting) {
                    return [$setting->key => $this->formatSettingValue($setting)];
                });
            });

            return $this->sendResponse($formattedSettings, 'Settings retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to get settings: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Update settings.
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $settings = $request->all();

            foreach ($settings as $key => $value) {
                if (is_array($value)) {
                    $value = json_encode($value);
                    $type = 'json';
                } elseif (is_bool($value)) {
                    $type = 'boolean';
                } elseif (is_numeric($value)) {
                    $type = 'number';
                } else {
                    $type = 'string';
                }

                Setting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $value,
                        'type' => $type,
                        'group' => $this->getSettingGroup($key),
                    ]
                );
            }

            // Clear settings cache
            Cache::forget(Setting::CACHE_KEY);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->withProperties(['settings_updated' => array_keys($settings)])
                ->log('Settings updated');

            return $this->sendResponse(null, 'Settings updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update settings: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get settings by group.
     */
    public function getGroup(string $group): JsonResponse
    {
        try {
            $settings = Setting::where('group', $group)->get();

            $formattedSettings = $settings->mapWithKeys(function ($setting) {
                return [$setting->key => $this->formatSettingValue($setting)];
            });

            return $this->sendResponse($formattedSettings, "Settings for group '{$group}' retrieved successfully");

        } catch (\Exception $e) {
            return $this->sendError('Failed to get settings group: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Update settings by group.
     */
    public function updateGroup(Request $request, string $group): JsonResponse
    {
        try {
            $settings = $request->all();

            foreach ($settings as $key => $value) {
                if (is_array($value)) {
                    $value = json_encode($value);
                    $type = 'json';
                } elseif (is_bool($value)) {
                    $type = 'boolean';
                } elseif (is_numeric($value)) {
                    $type = 'number';
                } else {
                    $type = 'string';
                }

                Setting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $value,
                        'type' => $type,
                        'group' => $group,
                    ]
                );
            }

            // Clear settings cache
            Cache::forget(Setting::CACHE_KEY);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->withProperties([
                    'group' => $group,
                    'settings_updated' => array_keys($settings)
                ])
                ->log("Settings group '{$group}' updated");

            return $this->sendResponse(null, "Settings for group '{$group}' updated successfully");

        } catch (\Exception $e) {
            return $this->sendError('Failed to update settings group: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Toggle module status.
     */
    public function toggleModule(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'module' => 'required|string',
            'enabled' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $module = $request->module;
            $enabled = $request->enabled;

            Setting::updateOrCreate(
                ['key' => "module_{$module}_enabled"],
                [
                    'value' => $enabled,
                    'type' => 'boolean',
                    'group' => 'modules',
                ]
            );

            // Clear settings cache
            Cache::forget(Setting::CACHE_KEY);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->withProperties([
                    'module' => $module,
                    'enabled' => $enabled
                ])
                ->log("Module '{$module}' " . ($enabled ? 'enabled' : 'disabled'));

            return $this->sendResponse(null, "Module '{$module}' " . ($enabled ? 'enabled' : 'disabled') . ' successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to toggle module: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get system logs.
     */
    public function getLogs(Request $request): JsonResponse
    {
        try {
            $lines = $request->get('lines', 100);
            $level = $request->get('level', 'all');

            $logFile = storage_path('logs/laravel.log');
            
            if (!file_exists($logFile)) {
                return $this->sendResponse([], 'No logs found');
            }

            $logs = [];
            $handle = fopen($logFile, 'r');
            
            if ($handle) {
                $logLines = [];
                while (($line = fgets($handle)) !== false) {
                    $logLines[] = $line;
                }
                fclose($handle);

                // Get last N lines
                $logLines = array_slice($logLines, -$lines);

                foreach ($logLines as $line) {
                    if (preg_match('/\[(.*?)\] (\w+)\.(\w+): (.*)/', $line, $matches)) {
                        $logEntry = [
                            'timestamp' => $matches[1],
                            'environment' => $matches[2],
                            'level' => $matches[3],
                            'message' => $matches[4],
                        ];

                        if ($level === 'all' || strtolower($logEntry['level']) === strtolower($level)) {
                            $logs[] = $logEntry;
                        }
                    }
                }
            }

            return $this->sendResponse(array_reverse($logs), 'System logs retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to get system logs: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Clear application cache.
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $cacheTypes = $request->get('types', ['config', 'route', 'view', 'cache']);

            $clearedCaches = [];

            foreach ($cacheTypes as $type) {
                switch ($type) {
                    case 'config':
                        Artisan::call('config:clear');
                        $clearedCaches[] = 'Configuration cache';
                        break;
                    case 'route':
                        Artisan::call('route:clear');
                        $clearedCaches[] = 'Route cache';
                        break;
                    case 'view':
                        Artisan::call('view:clear');
                        $clearedCaches[] = 'View cache';
                        break;
                    case 'cache':
                        Artisan::call('cache:clear');
                        $clearedCaches[] = 'Application cache';
                        break;
                }
            }

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->withProperties(['cleared_caches' => $clearedCaches])
                ->log('System cache cleared');

            return $this->sendResponse([
                'cleared_caches' => $clearedCaches
            ], 'Cache cleared successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to clear cache: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Format setting value based on type.
     */
    private function formatSettingValue(Setting $setting)
    {
        switch ($setting->type) {
            case 'boolean':
                return (bool) $setting->value;
            case 'number':
                return is_numeric($setting->value) ? (float) $setting->value : $setting->value;
            case 'json':
                return json_decode($setting->value, true);
            default:
                return $setting->value;
        }
    }

    /**
     * Get setting group based on key.
     */
    private function getSettingGroup(string $key): string
    {
        $groupMappings = [
            'site_' => 'general',
            'admin_' => 'general',
            'timezone' => 'general',
            'date_' => 'general',
            'time_' => 'general',
            'currency' => 'general',
            'theme_' => 'design',
            'logo_' => 'design',
            'favicon' => 'design',
            'company_' => 'contact',
            'facebook_' => 'social',
            'twitter_' => 'social',
            'instagram_' => 'social',
            'youtube_' => 'social',
            'linkedin_' => 'social',
            'gpay_' => 'payment',
            'razorpay_' => 'payment',
            'cod_' => 'payment',
            'min_order_' => 'payment',
            'google_' => 'analytics',
            'facebook_pixel_' => 'analytics',
            'mail_' => 'email',
            'module_' => 'modules',
        ];

        foreach ($groupMappings as $prefix => $group) {
            if (str_starts_with($key, $prefix)) {
                return $group;
            }
        }

        return 'general';
    }
}
