import ApiService from './ApiService';

class AddressService {
  /**
   * Get all user addresses
   */
  async getAddresses() {
    try {
      const response = await ApiService.get('/user/addresses');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get address by ID
   */
  async getAddress(id) {
    try {
      const response = await ApiService.get(`/user/addresses/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Create new address
   */
  async createAddress(addressData) {
    try {
      const response = await ApiService.post('/user/addresses', addressData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Update address
   */
  async updateAddress(id, addressData) {
    try {
      const response = await ApiService.put(`/user/addresses/${id}`, addressData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Delete address
   */
  async deleteAddress(id) {
    try {
      const response = await ApiService.delete(`/user/addresses/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Set address as default
   */
  async setAsDefault(id) {
    try {
      const response = await ApiService.post(`/user/addresses/${id}/set-default`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get default addresses
   */
  async getDefaultAddresses() {
    try {
      const response = await ApiService.get('/user/addresses/defaults');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get addresses by type
   */
  async getAddressesByType(type) {
    try {
      const response = await ApiService.get(`/user/addresses/type/${type}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Validate address data
   */
  validateAddress(address) {
    const errors = [];

    if (!address.first_name?.trim()) {
      errors.push('First name is required');
    }

    if (!address.last_name?.trim()) {
      errors.push('Last name is required');
    }

    if (!address.address_line_1?.trim()) {
      errors.push('Address line 1 is required');
    }

    if (!address.city?.trim()) {
      errors.push('City is required');
    }

    if (!address.state?.trim()) {
      errors.push('State is required');
    }

    if (!address.postal_code?.trim()) {
      errors.push('Postal code is required');
    }

    if (!address.country?.trim()) {
      errors.push('Country is required');
    }

    if (!address.type || !['shipping', 'billing', 'both'].includes(address.type)) {
      errors.push('Valid address type is required');
    }

    // Validate postal code format for US
    if (address.postal_code && address.country === 'US') {
      const usZipRegex = /^\d{5}(-\d{4})?$/;
      if (!usZipRegex.test(address.postal_code)) {
        errors.push('Invalid US postal code format');
      }
    }

    // Validate phone number format
    if (address.phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(address.phone.replace(/[\s\-\(\)]/g, ''))) {
        errors.push('Invalid phone number format');
      }
    }

    return errors;
  }

  /**
   * Format address for display
   */
  formatAddress(address) {
    const parts = [
      address.address_line_1,
      address.address_line_2,
      address.city,
      `${address.state} ${address.postal_code}`,
      address.country
    ].filter(part => part && part.trim());

    return parts.join(', ');
  }

  /**
   * Get full name from address
   */
  getFullName(address) {
    return `${address.first_name} ${address.last_name}`.trim();
  }

  /**
   * Get countries list
   */
  getCountries() {
    return [
      { code: 'US', name: 'United States' },
      { code: 'CA', name: 'Canada' },
      { code: 'GB', name: 'United Kingdom' },
      { code: 'AU', name: 'Australia' },
      { code: 'DE', name: 'Germany' },
      { code: 'FR', name: 'France' },
      { code: 'IT', name: 'Italy' },
      { code: 'ES', name: 'Spain' },
      { code: 'NL', name: 'Netherlands' },
      { code: 'BE', name: 'Belgium' },
      { code: 'CH', name: 'Switzerland' },
      { code: 'AT', name: 'Austria' },
      { code: 'SE', name: 'Sweden' },
      { code: 'NO', name: 'Norway' },
      { code: 'DK', name: 'Denmark' },
      { code: 'FI', name: 'Finland' },
      { code: 'IE', name: 'Ireland' },
      { code: 'PT', name: 'Portugal' },
      { code: 'GR', name: 'Greece' },
      { code: 'PL', name: 'Poland' }
    ];
  }

  /**
   * Get US states
   */
  getUSStates() {
    return [
      { code: 'AL', name: 'Alabama' },
      { code: 'AK', name: 'Alaska' },
      { code: 'AZ', name: 'Arizona' },
      { code: 'AR', name: 'Arkansas' },
      { code: 'CA', name: 'California' },
      { code: 'CO', name: 'Colorado' },
      { code: 'CT', name: 'Connecticut' },
      { code: 'DE', name: 'Delaware' },
      { code: 'FL', name: 'Florida' },
      { code: 'GA', name: 'Georgia' },
      { code: 'HI', name: 'Hawaii' },
      { code: 'ID', name: 'Idaho' },
      { code: 'IL', name: 'Illinois' },
      { code: 'IN', name: 'Indiana' },
      { code: 'IA', name: 'Iowa' },
      { code: 'KS', name: 'Kansas' },
      { code: 'KY', name: 'Kentucky' },
      { code: 'LA', name: 'Louisiana' },
      { code: 'ME', name: 'Maine' },
      { code: 'MD', name: 'Maryland' },
      { code: 'MA', name: 'Massachusetts' },
      { code: 'MI', name: 'Michigan' },
      { code: 'MN', name: 'Minnesota' },
      { code: 'MS', name: 'Mississippi' },
      { code: 'MO', name: 'Missouri' },
      { code: 'MT', name: 'Montana' },
      { code: 'NE', name: 'Nebraska' },
      { code: 'NV', name: 'Nevada' },
      { code: 'NH', name: 'New Hampshire' },
      { code: 'NJ', name: 'New Jersey' },
      { code: 'NM', name: 'New Mexico' },
      { code: 'NY', name: 'New York' },
      { code: 'NC', name: 'North Carolina' },
      { code: 'ND', name: 'North Dakota' },
      { code: 'OH', name: 'Ohio' },
      { code: 'OK', name: 'Oklahoma' },
      { code: 'OR', name: 'Oregon' },
      { code: 'PA', name: 'Pennsylvania' },
      { code: 'RI', name: 'Rhode Island' },
      { code: 'SC', name: 'South Carolina' },
      { code: 'SD', name: 'South Dakota' },
      { code: 'TN', name: 'Tennessee' },
      { code: 'TX', name: 'Texas' },
      { code: 'UT', name: 'Utah' },
      { code: 'VT', name: 'Vermont' },
      { code: 'VA', name: 'Virginia' },
      { code: 'WA', name: 'Washington' },
      { code: 'WV', name: 'West Virginia' },
      { code: 'WI', name: 'Wisconsin' },
      { code: 'WY', name: 'Wyoming' }
    ];
  }

  /**
   * Handle API errors
   */
  handleError(error) {
    if (error.response) {
      return new Error(error.response.data.message || 'An error occurred');
    } else if (error.request) {
      return new Error('Network error. Please check your connection.');
    } else {
      return new Error('An unexpected error occurred');
    }
  }
}

export default new AddressService();
