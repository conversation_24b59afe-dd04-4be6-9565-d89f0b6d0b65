<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, LogsActivity, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'discount_percentage',
        'is_active',
        'avatar',
        'email_verified_at',
        'last_login_at',
        'last_login_ip',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'is_active' => 'boolean',
        'discount_percentage' => 'decimal:2',
        'password' => 'hashed',
    ];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'phone', 'is_active'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the user's avatar URL.
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/avatars/' . $this->avatar);
        }
        
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF';
    }

    /**
     * Get the user's full address.
     */
    public function getFullAddressAttribute(): string
    {
        return $this->address ?? 'No address provided';
    }

    /**
     * Check if user has specific permission.
     */
    public function hasPermissionTo($permission): bool
    {
        return $this->can($permission);
    }

    /**
     * Get user groups.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(UserGroup::class, 'user_group_members');
    }

    /**
     * Get user orders.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get user transactions.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get user coupons.
     */
    public function coupons(): BelongsToMany
    {
        return $this->belongsToMany(Coupon::class, 'coupon_users');
    }

    /**
     * Get user invoices.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Scope for active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for users with specific role.
     */
    public function scopeWithRole($query, $role)
    {
        return $query->role($role);
    }

    /**
     * Scope for users in specific group.
     */
    public function scopeInGroup($query, $groupId)
    {
        return $query->whereHas('groups', function ($q) use ($groupId) {
            $q->where('user_groups.id', $groupId);
        });
    }

    /**
     * Get user's total orders count.
     */
    public function getTotalOrdersAttribute(): int
    {
        return $this->orders()->count();
    }

    /**
     * Get user's total spent amount.
     */
    public function getTotalSpentAttribute(): float
    {
        return $this->orders()->where('status', 'completed')->sum('total_amount');
    }

    /**
     * Get user's average order value.
     */
    public function getAverageOrderValueAttribute(): float
    {
        $completedOrders = $this->orders()->where('status', 'completed');
        $totalAmount = $completedOrders->sum('total_amount');
        $orderCount = $completedOrders->count();
        
        return $orderCount > 0 ? $totalAmount / $orderCount : 0;
    }

    /**
     * Check if user can access specific module.
     */
    public function canAccessModule(string $module): bool
    {
        // Super admin can access everything
        if ($this->hasRole('super-admin')) {
            return true;
        }

        // Check if module is enabled globally
        $moduleSettings = Setting::where('key', "module_{$module}_enabled")->first();
        if (!$moduleSettings || !$moduleSettings->value) {
            return false;
        }

        // Check user permissions for the module
        return $this->can("access_{$module}");
    }

    /**
     * Apply user discount to amount.
     */
    public function applyDiscount(float $amount): float
    {
        if ($this->discount_percentage > 0) {
            $discount = ($amount * $this->discount_percentage) / 100;
            return $amount - $discount;
        }
        
        return $amount;
    }

    /**
     * Update last login information.
     */
    public function updateLastLogin(string $ip = null): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip ?? request()->ip(),
        ]);
    }
}
