version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: ecommerce_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ecommerce_db
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_USER: ecommerce_user
      MYSQL_PASSWORD: ecommerce_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/database/init:/docker-entrypoint-initdb.d
    networks:
      - ecommerce_network

  # Redis for Caching
  redis:
    image: redis:7-alpine
    container_name: ecommerce_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ecommerce_network

  # Laravel Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ecommerce_backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/var/www/html
      - ./backend/storage:/var/www/html/storage
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=ecommerce_db
      - DB_USERNAME=ecommerce_user
      - DB_PASSWORD=ecommerce_password
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - ecommerce_network

  # Angular Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ecommerce_frontend
    restart: unless-stopped
    ports:
      - "4200:4200"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - API_URL=http://backend:8000/api
    depends_on:
      - backend
    networks:
      - ecommerce_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ecommerce_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - ecommerce_network

volumes:
  mysql_data:
  redis_data:

networks:
  ecommerce_network:
    driver: bridge
