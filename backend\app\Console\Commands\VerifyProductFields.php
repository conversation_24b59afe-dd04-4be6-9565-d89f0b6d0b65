<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class VerifyProductFields extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:verify-fields {--fix : Fix missing fields automatically}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify that all required product fields exist in the database';

    /**
     * Required product fields with their specifications.
     */
    protected $requiredFields = [
        'id' => ['type' => 'bigint', 'nullable' => false, 'primary' => true],
        'name' => ['type' => 'varchar', 'nullable' => false],
        'slug' => ['type' => 'varchar', 'nullable' => false, 'unique' => true],
        'description' => ['type' => 'text', 'nullable' => true],
        'short_description' => ['type' => 'text', 'nullable' => true],
        'sku' => ['type' => 'varchar', 'nullable' => false, 'unique' => true],
        'category_id' => ['type' => 'bigint', 'nullable' => false, 'foreign' => 'categories.id'],
        'mrp' => ['type' => 'decimal', 'precision' => '10,2', 'nullable' => false],
        'selling_price' => ['type' => 'decimal', 'precision' => '10,2', 'nullable' => false],
        'price' => ['type' => 'decimal', 'precision' => '10,2', 'nullable' => false],
        'sale_price' => ['type' => 'decimal', 'precision' => '10,2', 'nullable' => true],
        'cost_price' => ['type' => 'decimal', 'precision' => '10,2', 'nullable' => true],
        'discount_percentage' => ['type' => 'decimal', 'precision' => '5,2', 'nullable' => true, 'default' => 0],
        'stock_quantity' => ['type' => 'int', 'nullable' => false, 'default' => 0],
        'manage_stock' => ['type' => 'boolean', 'nullable' => false, 'default' => true],
        'stock_status' => ['type' => 'enum', 'values' => ['in_stock', 'out_of_stock', 'on_backorder'], 'default' => 'in_stock'],
        'low_stock_threshold' => ['type' => 'int', 'nullable' => false, 'default' => 5],
        'weight' => ['type' => 'decimal', 'precision' => '8,2', 'nullable' => true],
        'length' => ['type' => 'decimal', 'precision' => '8,2', 'nullable' => true],
        'width' => ['type' => 'decimal', 'precision' => '8,2', 'nullable' => true],
        'height' => ['type' => 'decimal', 'precision' => '8,2', 'nullable' => true],
        'status' => ['type' => 'enum', 'values' => ['draft', 'published', 'archived'], 'default' => 'draft'],
        'is_active' => ['type' => 'boolean', 'nullable' => false, 'default' => true],
        'featured' => ['type' => 'boolean', 'nullable' => false, 'default' => false],
        'meta_title' => ['type' => 'varchar', 'nullable' => true],
        'meta_description' => ['type' => 'text', 'nullable' => true],
        'meta_keywords' => ['type' => 'text', 'nullable' => true],
        'purchase_note' => ['type' => 'text', 'nullable' => true],
        'attributes' => ['type' => 'json', 'nullable' => true],
        'gallery' => ['type' => 'json', 'nullable' => true],
        'main_image' => ['type' => 'varchar', 'nullable' => true],
        'created_at' => ['type' => 'timestamp', 'nullable' => true],
        'updated_at' => ['type' => 'timestamp', 'nullable' => true],
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Verifying product table fields...');
        $this->newLine();

        if (!Schema::hasTable('products')) {
            $this->error('❌ Products table does not exist!');
            return 1;
        }

        $existingColumns = $this->getExistingColumns();
        $missingFields = [];
        $presentFields = [];

        foreach ($this->requiredFields as $fieldName => $specs) {
            if (isset($existingColumns[$fieldName])) {
                $presentFields[] = $fieldName;
                $this->line("✅ {$fieldName} - EXISTS");
            } else {
                $missingFields[] = $fieldName;
                $this->line("❌ {$fieldName} - MISSING");
            }
        }

        $this->newLine();
        $this->info("📊 Summary:");
        $this->info("   Present fields: " . count($presentFields) . "/" . count($this->requiredFields));
        $this->info("   Missing fields: " . count($missingFields));

        if (count($missingFields) > 0) {
            $this->newLine();
            $this->warn("⚠️  Missing fields detected:");
            foreach ($missingFields as $field) {
                $this->warn("   - {$field}");
            }

            if ($this->option('fix')) {
                $this->newLine();
                $this->info("🔧 Attempting to fix missing fields...");
                $this->fixMissingFields($missingFields);
            } else {
                $this->newLine();
                $this->info("💡 To automatically fix missing fields, run:");
                $this->info("   php artisan products:verify-fields --fix");
            }
        } else {
            $this->newLine();
            $this->info("🎉 All required product fields are present!");
        }

        // Clear cache after verification
        $this->info("🧹 Clearing application cache...");
        $this->call('cache:clear');
        $this->call('config:clear');
        $this->call('route:clear');
        $this->call('view:clear');
        
        return 0;
    }

    /**
     * Get existing columns from the products table.
     */
    private function getExistingColumns(): array
    {
        $columns = DB::select("
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'products' 
                AND TABLE_SCHEMA = DATABASE()
        ");

        $result = [];
        foreach ($columns as $column) {
            $result[$column->COLUMN_NAME] = [
                'type' => $column->DATA_TYPE,
                'nullable' => $column->IS_NULLABLE === 'YES',
                'default' => $column->COLUMN_DEFAULT,
            ];
        }

        return $result;
    }

    /**
     * Fix missing fields by adding them to the table.
     */
    private function fixMissingFields(array $missingFields): void
    {
        foreach ($missingFields as $fieldName) {
            $specs = $this->requiredFields[$fieldName];
            
            try {
                $this->addField($fieldName, $specs);
                $this->info("✅ Added field: {$fieldName}");
            } catch (\Exception $e) {
                $this->error("❌ Failed to add field {$fieldName}: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info("🎉 Field fixing completed!");
    }

    /**
     * Add a specific field to the products table.
     */
    private function addField(string $fieldName, array $specs): void
    {
        Schema::table('products', function ($table) use ($fieldName, $specs) {
            switch ($specs['type']) {
                case 'varchar':
                    $column = $table->string($fieldName);
                    break;
                case 'text':
                    $column = $table->text($fieldName);
                    break;
                case 'decimal':
                    $precision = explode(',', $specs['precision']);
                    $column = $table->decimal($fieldName, (int)$precision[0], (int)$precision[1]);
                    break;
                case 'int':
                    $column = $table->integer($fieldName);
                    break;
                case 'bigint':
                    $column = $table->bigInteger($fieldName);
                    break;
                case 'boolean':
                    $column = $table->boolean($fieldName);
                    break;
                case 'json':
                    $column = $table->json($fieldName);
                    break;
                case 'enum':
                    $column = $table->enum($fieldName, $specs['values']);
                    break;
                case 'timestamp':
                    $column = $table->timestamp($fieldName);
                    break;
                default:
                    $column = $table->string($fieldName);
            }

            // Apply nullable
            if ($specs['nullable']) {
                $column->nullable();
            }

            // Apply default value
            if (isset($specs['default'])) {
                $column->default($specs['default']);
            }

            // Apply unique constraint
            if (isset($specs['unique']) && $specs['unique']) {
                $column->unique();
            }
        });
    }
}
