.app-container {
  min-height: 100vh;
  transition: opacity 0.3s ease;

  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .loading-text {
    margin-top: 16px;
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }
}

.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
}
