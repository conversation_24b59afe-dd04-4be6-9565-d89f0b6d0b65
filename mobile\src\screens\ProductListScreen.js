import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';

import ProductService from '../services/ProductService';
import ProductCard from '../components/ProductCard';
import FilterModal from '../components/FilterModal';
import SortModal from '../components/SortModal';
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';
import { colors, spacing, typography } from '../theme';

const ProductListScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  
  // Get category from route params if navigating from category
  const { categoryId, categoryName } = route.params || {};

  // State
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showSort, setShowSort] = useState(false);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [totalItems, setTotalItems] = useState(0);

  // Filters and sorting
  const [filters, setFilters] = useState({
    category_id: categoryId || null,
    search: '',
    price_min: null,
    price_max: null,
    status: 'published',
    stock_status: null,
    featured: null,
    on_sale: null,
    sort_by: 'created_at',
    sort_order: 'desc',
    per_page: 20
  });

  const [availableFilters, setAvailableFilters] = useState({
    categories: [],
    priceRange: { min: 0, max: 1000 },
    brands: []
  });

  // Sort options
  const sortOptions = [
    { key: 'created_at_desc', label: 'Newest First', sort_by: 'created_at', sort_order: 'desc' },
    { key: 'created_at_asc', label: 'Oldest First', sort_by: 'created_at', sort_order: 'asc' },
    { key: 'name_asc', label: 'Name A-Z', sort_by: 'name', sort_order: 'asc' },
    { key: 'name_desc', label: 'Name Z-A', sort_by: 'name', sort_order: 'desc' },
    { key: 'price_asc', label: 'Price Low to High', sort_by: 'price', sort_order: 'asc' },
    { key: 'price_desc', label: 'Price High to Low', sort_by: 'price', sort_order: 'desc' },
    { key: 'rating_desc', label: 'Highest Rated', sort_by: 'average_rating', sort_order: 'desc' },
    { key: 'popularity_desc', label: 'Most Popular', sort_by: 'total_sales', sort_order: 'desc' }
  ];

  useEffect(() => {
    loadProducts(true);
    loadFilters();
  }, [filters.category_id, filters.sort_by, filters.sort_order]);

  useEffect(() => {
    // Set navigation title
    navigation.setOptions({
      title: categoryName || 'Products',
      headerRight: () => (
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowSearch(!showSearch)}
          >
            <Icon name="search" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilters(true)}
          >
            <Icon name="filter-list" size={24} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      )
    });
  }, [navigation, categoryName, showSearch]);

  const loadProducts = async (reset = false) => {
    try {
      if (reset) {
        setLoading(true);
        setCurrentPage(1);
      } else {
        setLoadingMore(true);
      }

      const page = reset ? 1 : currentPage + 1;
      const searchFilters = {
        ...filters,
        page,
        search: searchQuery
      };

      const response = await ProductService.getProducts(searchFilters);

      if (response.success) {
        const newProducts = response.data;
        
        if (reset) {
          setProducts(newProducts);
        } else {
          setProducts(prev => [...prev, ...newProducts]);
        }

        setCurrentPage(page);
        setHasMorePages(response.meta.current_page < response.meta.last_page);
        setTotalItems(response.meta.total);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      Alert.alert('Error', 'Failed to load products. Please try again.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  const loadFilters = async () => {
    try {
      const response = await ProductService.getProductFilters(categoryId);
      if (response.success) {
        setAvailableFilters(response.data);
      }
    } catch (error) {
      console.error('Error loading filters:', error);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadProducts(true);
  }, [filters, searchQuery]);

  const onLoadMore = () => {
    if (!loadingMore && hasMorePages) {
      loadProducts(false);
    }
  };

  const onSearch = (query) => {
    setSearchQuery(query);
    if (query.length >= 2 || query.length === 0) {
      loadProducts(true);
    }
  };

  const onApplyFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setShowFilters(false);
    loadProducts(true);
  };

  const onApplySort = (sortOption) => {
    setFilters(prev => ({
      ...prev,
      sort_by: sortOption.sort_by,
      sort_order: sortOption.sort_order
    }));
    setShowSort(false);
  };

  const onProductPress = (product) => {
    navigation.navigate('ProductDetail', { productId: product.id, product });
  };

  const onAddToCart = async (product) => {
    try {
      await ProductService.addToCart(product.id, 1);
      Alert.alert('Success', `${product.name} added to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      Alert.alert('Error', 'Failed to add product to cart. Please try again.');
    }
  };

  const onToggleWishlist = async (product) => {
    try {
      if (product.is_in_wishlist) {
        await ProductService.removeFromWishlist(product.id);
      } else {
        await ProductService.addToWishlist(product.id);
      }
      
      // Update product in list
      setProducts(prev => prev.map(p => 
        p.id === product.id 
          ? { ...p, is_in_wishlist: !p.is_in_wishlist }
          : p
      ));
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      Alert.alert('Error', 'Failed to update wishlist. Please try again.');
    }
  };

  const renderProduct = ({ item }) => (
    <ProductCard
      product={item}
      onPress={() => onProductPress(item)}
      onAddToCart={() => onAddToCart(item)}
      onToggleWishlist={() => onToggleWishlist(item)}
      style={styles.productCard}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {showSearch && (
        <View style={styles.searchContainer}>
          <Icon name="search" size={20} color={colors.text.secondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search products..."
            value={searchQuery}
            onChangeText={onSearch}
            autoFocus
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => onSearch('')}
            >
              <Icon name="clear" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          )}
        </View>
      )}
      
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsText}>
          {totalItems} product{totalItems !== 1 ? 's' : ''} found
        </Text>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => setShowSort(true)}
        >
          <Icon name="sort" size={20} color={colors.text.secondary} />
          <Text style={styles.sortText}>Sort</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    
    return (
      <View style={styles.loadingMore}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.loadingMoreText}>Loading more products...</Text>
      </View>
    );
  };

  const renderEmpty = () => (
    <EmptyState
      icon="inventory"
      title="No products found"
      message={searchQuery ? 
        `No products match "${searchQuery}". Try adjusting your search or filters.` :
        "No products available in this category."
      }
      actionText="Clear Filters"
      onAction={() => {
        setSearchQuery('');
        setFilters(prev => ({
          ...prev,
          search: '',
          price_min: null,
          price_max: null,
          stock_status: null,
          featured: null,
          on_sale: null
        }));
        loadProducts(true);
      }}
    />
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={products}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        columnWrapperStyle={styles.row}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      {/* Filter Modal */}
      <FilterModal
        visible={showFilters}
        filters={filters}
        availableFilters={availableFilters}
        onApply={onApplyFilters}
        onClose={() => setShowFilters(false)}
      />

      {/* Sort Modal */}
      <SortModal
        visible={showSort}
        options={sortOptions}
        selectedSort={`${filters.sort_by}_${filters.sort_order}`}
        onApply={onApplySort}
        onClose={() => setShowSort(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: spacing.sm,
    marginLeft: spacing.xs,
  },
  header: {
    padding: spacing.md,
    backgroundColor: colors.background.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
  },
  clearButton: {
    padding: spacing.xs,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resultsText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
  },
  sortText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },
  listContent: {
    flexGrow: 1,
  },
  row: {
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
  },
  productCard: {
    width: '48%',
    marginBottom: spacing.md,
  },
  loadingMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  loadingMoreText: {
    marginLeft: spacing.sm,
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
  },
});

export default ProductListScreen;
