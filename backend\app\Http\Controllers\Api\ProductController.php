<?php

namespace App\Http\Controllers\Api;

use App\Models\Product;
use App\Models\Category;
use App\Models\ProductImage;
use App\Models\ProductVariation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ProductController extends BaseController
{
    /**
     * Display a listing of products.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $categoryId = $request->get('category_id');
        $status = $request->get('status');
        $featured = $request->get('featured');
        $inStock = $request->get('in_stock');
        $onSale = $request->get('on_sale');
        $priceMin = $request->get('price_min');
        $priceMax = $request->get('price_max');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Product::with(['category:id,name', 'images', 'creator:id,name'])
            ->withCount(['variations', 'orderItems']);

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($categoryId) {
            $query->inCategory($categoryId);
        }

        // Filter by status
        if ($status) {
            $query->where('status', $status);
        }

        // Filter featured products
        if ($featured !== null) {
            $query->where('featured', $featured);
        }

        // Filter by stock status
        if ($inStock !== null) {
            if ($inStock) {
                $query->inStock();
            } else {
                $query->where('stock_status', Product::STOCK_OUT_OF_STOCK);
            }
        }

        // Filter products on sale
        if ($onSale !== null && $onSale) {
            $query->onSale();
        }

        // Filter by price range
        if ($priceMin !== null || $priceMax !== null) {
            $query->when($priceMin, fn($q) => $q->where('price', '>=', $priceMin))
                  ->when($priceMax, fn($q) => $q->where('price', '<=', $priceMax));
        }

        // Sorting
        $allowedSortFields = ['name', 'price', 'stock_quantity', 'created_at', 'updated_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $products = $query->paginate($perPage);

        // Transform product data
        $products->getCollection()->transform(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'sku' => $product->sku,
                'category' => [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                ],
                'price' => $product->price,
                'sale_price' => $product->sale_price,
                'effective_price' => $product->effective_price,
                'is_on_sale' => $product->is_on_sale,
                'discount_percentage' => $product->discount_percentage,
                'stock_quantity' => $product->stock_quantity,
                'stock_status' => $product->stock_status,
                'is_in_stock' => $product->is_in_stock,
                'is_low_stock' => $product->is_low_stock,
                'main_image_url' => $product->main_image_url,
                'status' => $product->status,
                'featured' => $product->featured,
                'variations_count' => $product->variations_count,
                'order_items_count' => $product->order_items_count,
                'creator' => $product->creator ? $product->creator->name : null,
                'created_at' => $product->created_at,
                'updated_at' => $product->updated_at,
            ];
        });

        return $this->sendPaginatedResponse($products);
    }

    /**
     * Store a newly created product.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'sku' => 'nullable|string|max:100|unique:products',
            'category_id' => 'required|integer|exists:categories,id',
            'mrp' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0|lte:mrp',
            'price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:selling_price',
            'cost_price' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'stock_quantity' => 'nullable|integer|min:0',
            'manage_stock' => 'boolean',
            'stock_status' => 'in:in_stock,out_of_stock,on_backorder',
            'low_stock_threshold' => 'nullable|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'status' => 'in:draft,published,archived',
            'is_active' => 'boolean',
            'featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'purchase_note' => 'nullable|string|max:1000',
            'attributes' => 'nullable|array',
            'gallery' => 'nullable|array',
            'main_image' => 'nullable|string|max:255',
            'images' => 'nullable|array',
            'images.*.image_path' => 'required|string',
            'images.*.alt_text' => 'nullable|string|max:255',
            'images.*.is_primary' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        DB::beginTransaction();
        try {
            $productData = $request->only([
                'name', 'slug', 'description', 'short_description', 'sku', 'category_id',
                'mrp', 'selling_price', 'price', 'sale_price', 'cost_price', 'discount_percentage',
                'stock_quantity', 'manage_stock', 'stock_status', 'low_stock_threshold',
                'weight', 'length', 'width', 'height', 'status', 'is_active', 'featured',
                'meta_title', 'meta_description', 'meta_keywords', 'purchase_note',
                'attributes', 'gallery', 'main_image'
            ]);

            $productData['created_by'] = auth()->id();

            // Generate slug if not provided
            if (empty($productData['slug'])) {
                $productData['slug'] = Product::generateUniqueSlug($productData['name']);
            }

            // Generate SKU if not provided
            if (empty($productData['sku'])) {
                $productData['sku'] = Product::generateUniqueSku();
            }

            // Set default values
            $productData['manage_stock'] = $productData['manage_stock'] ?? true;
            $productData['stock_status'] = $productData['stock_status'] ?? Product::STOCK_IN_STOCK;
            $productData['status'] = $productData['status'] ?? Product::STATUS_DRAFT;
            $productData['featured'] = $productData['featured'] ?? false;
            $productData['low_stock_threshold'] = $productData['low_stock_threshold'] ?? 5;

            $product = Product::create($productData);

            // Add product images
            if ($request->has('images')) {
                foreach ($request->images as $index => $imageData) {
                    ProductImage::create([
                        'product_id' => $product->id,
                        'image_path' => $imageData['image_path'],
                        'alt_text' => $imageData['alt_text'] ?? null,
                        'is_primary' => $imageData['is_primary'] ?? ($index === 0),
                        'sort_order' => $index + 1,
                    ]);
                }
            }

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($product)
                ->log('Product created');

            return $this->sendCreated([
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'sku' => $product->sku,
                'price' => $product->price,
                'status' => $product->status,
                'category_id' => $product->category_id,
            ], 'Product created successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to create product: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified product.
     */
    public function show(string $id): JsonResponse
    {
        $product = Product::with([
            'category:id,name,slug',
            'images' => fn($q) => $q->orderBy('sort_order'),
            'variations' => fn($q) => $q->active(),
            'creator:id,name',
            'upsells:id,name,slug,price,main_image_url',
            'crossSells:id,name,slug,price,main_image_url'
        ])->find($id);

        if (!$product) {
            return $this->sendNotFound('Product not found');
        }

        return $this->sendResponse([
            'id' => $product->id,
            'name' => $product->name,
            'slug' => $product->slug,
            'description' => $product->description,
            'short_description' => $product->short_description,
            'sku' => $product->sku,
            'category' => [
                'id' => $product->category->id,
                'name' => $product->category->name,
                'slug' => $product->category->slug,
            ],
            'price' => $product->price,
            'sale_price' => $product->sale_price,
            'cost_price' => $product->cost_price,
            'effective_price' => $product->effective_price,
            'is_on_sale' => $product->is_on_sale,
            'discount_percentage' => $product->discount_percentage,
            'stock_quantity' => $product->stock_quantity,
            'manage_stock' => $product->manage_stock,
            'stock_status' => $product->stock_status,
            'low_stock_threshold' => $product->low_stock_threshold,
            'is_in_stock' => $product->is_in_stock,
            'is_low_stock' => $product->is_low_stock,
            'weight' => $product->weight,
            'dimensions' => $product->dimensions,
            'length' => $product->length,
            'width' => $product->width,
            'height' => $product->height,
            'status' => $product->status,
            'featured' => $product->featured,
            'meta_title' => $product->meta_title,
            'meta_description' => $product->meta_description,
            'meta_keywords' => $product->meta_keywords,
            'purchase_note' => $product->purchase_note,
            'attributes' => $product->attributes,
            'gallery' => $product->gallery,
            'main_image_url' => $product->main_image_url,
            'images' => $product->images->map(function ($image) {
                return [
                    'id' => $image->id,
                    'image_url' => $image->image_url,
                    'alt_text' => $image->alt_text,
                    'is_primary' => $image->is_primary,
                    'sort_order' => $image->sort_order,
                ];
            }),
            'variations' => $product->variations->map(function ($variation) {
                return [
                    'id' => $variation->id,
                    'sku' => $variation->sku,
                    'price' => $variation->price,
                    'sale_price' => $variation->sale_price,
                    'effective_price' => $variation->effective_price,
                    'stock_quantity' => $variation->stock_quantity,
                    'is_in_stock' => $variation->is_in_stock,
                    'attributes' => $variation->attributes,
                    'image_url' => $variation->image_url,
                    'display_name' => $variation->display_name,
                ];
            }),
            'upsells' => $product->upsells,
            'cross_sells' => $product->crossSells,
            'seo_data' => $product->seo_data,
            'creator' => $product->creator ? [
                'id' => $product->creator->id,
                'name' => $product->creator->name,
            ] : null,
            'created_at' => $product->created_at,
            'updated_at' => $product->updated_at,
        ]);
    }

    /**
     * Update the specified product.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $product = Product::find($id);

        if (!$product) {
            return $this->sendNotFound('Product not found');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'slug' => 'nullable|string|max:255|unique:products,slug,' . $id,
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'sku' => 'nullable|string|max:100|unique:products,sku,' . $id,
            'category_id' => 'integer|exists:categories,id',
            'mrp' => 'sometimes|numeric|min:0',
            'selling_price' => 'sometimes|numeric|min:0',
            'price' => 'numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'stock_quantity' => 'nullable|integer|min:0',
            'manage_stock' => 'boolean',
            'stock_status' => 'in:in_stock,out_of_stock,on_backorder',
            'low_stock_threshold' => 'nullable|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'status' => 'in:draft,published,archived',
            'is_active' => 'boolean',
            'featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'purchase_note' => 'nullable|string|max:1000',
            'attributes' => 'nullable|array',
            'gallery' => 'nullable|array',
            'main_image' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        // Validate sale price is less than regular price
        if ($request->has('sale_price') && $request->sale_price) {
            $price = $request->has('price') ? $request->price : $product->price;
            if ($request->sale_price >= $price) {
                return $this->sendError('Sale price must be less than regular price');
            }
        }

        try {
            $updateData = $request->only([
                'name', 'slug', 'description', 'short_description', 'sku', 'category_id',
                'price', 'sale_price', 'cost_price', 'stock_quantity', 'manage_stock',
                'stock_status', 'low_stock_threshold', 'weight', 'length', 'width', 'height',
                'status', 'featured', 'meta_title', 'meta_description', 'meta_keywords',
                'purchase_note', 'attributes', 'gallery'
            ]);

            // Generate slug if name changed and slug not provided
            if ($request->has('name') && !$request->has('slug')) {
                $updateData['slug'] = Product::generateUniqueSlug($request->name, $id);
            }

            $product->update($updateData);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($product)
                ->log('Product updated');

            return $this->sendUpdated([
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'sku' => $product->sku,
                'price' => $product->price,
                'status' => $product->status,
                'category_id' => $product->category_id,
            ], 'Product updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update product: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Remove the specified product.
     */
    public function destroy(string $id): JsonResponse
    {
        $product = Product::find($id);

        if (!$product) {
            return $this->sendNotFound('Product not found');
        }

        try {
            // Log activity before deletion
            activity()
                ->causedBy(auth()->user())
                ->performedOn($product)
                ->withProperties(['product_name' => $product->name, 'sku' => $product->sku])
                ->log('Product deleted');

            $product->delete();

            return $this->sendDeleted('Product deleted successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to delete product: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Duplicate a product.
     */
    public function duplicate(string $id): JsonResponse
    {
        $product = Product::find($id);

        if (!$product) {
            return $this->sendNotFound('Product not found');
        }

        try {
            $duplicatedProduct = $product->duplicate();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($duplicatedProduct)
                ->withProperties(['original_product_id' => $product->id])
                ->log('Product duplicated');

            return $this->sendCreated([
                'id' => $duplicatedProduct->id,
                'name' => $duplicatedProduct->name,
                'slug' => $duplicatedProduct->slug,
                'sku' => $duplicatedProduct->sku,
                'status' => $duplicatedProduct->status,
            ], 'Product duplicated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to duplicate product: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Auto-generate product variations from attributes.
     */
    public function createVariations(Request $request, string $id): JsonResponse
    {
        $product = Product::find($id);

        if (!$product) {
            return $this->sendNotFound('Product not found');
        }

        $validator = Validator::make($request->all(), [
            'attributes' => 'required|array|min:1',
            'attributes.*' => 'required|array|min:1',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $attributes = $request->attributes;
            $variations = $this->generateVariationCombinations($attributes);
            $createdVariations = [];

            foreach ($variations as $variation) {
                $variationData = [
                    'product_id' => $product->id,
                    'price' => $product->selling_price,
                    'sale_price' => $product->sale_price,
                    'stock_quantity' => 0,
                    'manage_stock' => true,
                    'stock_status' => 'out_of_stock',
                    'attributes' => $variation,
                    'is_active' => true,
                ];

                $productVariation = \App\Models\ProductVariation::create($variationData);
                $createdVariations[] = $productVariation;
            }

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($product)
                ->withProperties(['variations_count' => count($createdVariations)])
                ->log('Product variations auto-generated');

            return $this->sendResponse([
                'variations' => $createdVariations,
                'count' => count($createdVariations),
            ], 'Product variations created successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to create variations: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Generate all possible combinations of attributes.
     */
    private function generateVariationCombinations(array $attributes): array
    {
        $combinations = [[]];

        foreach ($attributes as $attributeName => $values) {
            $newCombinations = [];

            foreach ($combinations as $combination) {
                foreach ($values as $value) {
                    $newCombination = $combination;
                    $newCombination[$attributeName] = $value;
                    $newCombinations[] = $newCombination;
                }
            }

            $combinations = $newCombinations;
        }

        return $combinations;
    }

    /**
     * Bulk operations on products.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:update_status,update_category,update_featured,delete',
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:products,id',
            'data' => 'required|array',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        $action = $request->action;
        $productIds = $request->ids;
        $data = $request->data;

        try {
            $affectedCount = 0;

            switch ($action) {
                case 'update_status':
                    if (!isset($data['status']) || !in_array($data['status'], ['draft', 'published', 'archived'])) {
                        return $this->sendError('Invalid status provided');
                    }
                    $affectedCount = Product::whereIn('id', $productIds)->update(['status' => $data['status']]);
                    break;

                case 'update_category':
                    if (!isset($data['category_id']) || !Category::find($data['category_id'])) {
                        return $this->sendError('Invalid category provided');
                    }
                    $affectedCount = Product::whereIn('id', $productIds)->update(['category_id' => $data['category_id']]);
                    break;

                case 'update_featured':
                    if (!isset($data['featured']) || !is_bool($data['featured'])) {
                        return $this->sendError('Invalid featured value provided');
                    }
                    $affectedCount = Product::whereIn('id', $productIds)->update(['featured' => $data['featured']]);
                    break;

                case 'delete':
                    $products = Product::whereIn('id', $productIds)->get();
                    foreach ($products as $product) {
                        activity()
                            ->causedBy(auth()->user())
                            ->performedOn($product)
                            ->withProperties(['product_name' => $product->name])
                            ->log('Product bulk deleted');
                        $product->delete();
                    }
                    $affectedCount = $products->count();
                    break;
            }

            // Log bulk activity
            activity()
                ->causedBy(auth()->user())
                ->withProperties(['action' => $action, 'product_ids' => $productIds, 'data' => $data])
                ->log('Products bulk action performed');

            return $this->sendResponse([
                'affected_count' => $affectedCount,
            ], "Bulk action '{$action}' completed successfully");

        } catch (\Exception $e) {
            return $this->sendError('Failed to perform bulk action: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Update product stock.
     */
    public function updateStock(Request $request, string $id): JsonResponse
    {
        $product = Product::find($id);

        if (!$product) {
            return $this->sendNotFound('Product not found');
        }

        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:0',
            'operation' => 'in:set,increase,decrease',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $operation = $request->get('operation', 'set');
            $product->updateStock($request->quantity, $operation);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($product)
                ->withProperties([
                    'operation' => $operation,
                    'quantity' => $request->quantity,
                    'new_stock' => $product->fresh()->stock_quantity
                ])
                ->log('Product stock updated');

            return $this->sendResponse([
                'stock_quantity' => $product->fresh()->stock_quantity,
                'stock_status' => $product->fresh()->stock_status,
                'is_in_stock' => $product->fresh()->is_in_stock,
            ], 'Product stock updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update product stock: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get product statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = Product::getStatistics();

            return $this->sendResponse($stats);

        } catch (\Exception $e) {
            return $this->sendError('Failed to get product statistics: ' . $e->getMessage(), [], 500);
        }
    }
}
