<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛒 Admin Dashboard - E-Commerce Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-name {
            font-weight: 500;
        }
        
        .logout-btn {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(244, 67, 54, 0.4);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .welcome-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 0.5rem 0;
            color: #fff;
        }
        
        .stat-label {
            opacity: 0.8;
            font-size: 1rem;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .action-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .action-card h3 {
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        .btn.primary {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
        }
        
        .btn.primary:hover {
            background: rgba(76, 175, 80, 0.5);
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #ffcdd2;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }
        
        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4CAF50;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🛒 E-Commerce Admin</div>
        <div class="user-info">
            <span class="status-indicator"></span>
            <span class="user-name" id="userName">Loading...</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
        </div>
    </div>
    
    <div class="container">
        <div id="loadingSection" class="loading">
            <span class="spinner"></span>
            Verifying authentication and loading dashboard...
        </div>
        
        <div id="errorSection" class="error" style="display: none;">
            <h3>❌ Access Denied</h3>
            <p>You are not authenticated. Please login first.</p>
            <button class="btn primary" onclick="redirectToLogin()">Go to Login</button>
        </div>
        
        <div id="dashboardContent" style="display: none;">
            <div class="welcome-section">
                <h1>🎉 Welcome to Admin Dashboard!</h1>
                <p>You are successfully authenticated and can access all admin features.</p>
                <p><strong>Backend API:</strong> <span class="status-indicator"></span> Connected to http://localhost:8001</p>
            </div>
            
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">Loading...</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalProducts">Loading...</div>
                    <div class="stat-label">Total Products</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalOrders">Loading...</div>
                    <div class="stat-label">Total Orders</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRevenue">Loading...</div>
                    <div class="stat-label">Total Revenue</div>
                </div>
            </div>
            
            <div class="actions-grid">
                <div class="action-card">
                    <h3>📊 Analytics & Reports</h3>
                    <p>View detailed analytics and generate reports.</p>
                    <button class="btn primary" onclick="loadAnalytics()">View Analytics</button>
                    <button class="btn" onclick="testAPI()">Test API</button>
                </div>
                
                <div class="action-card">
                    <h3>👥 User Management</h3>
                    <p>Manage users, roles, and permissions.</p>
                    <button class="btn primary" onclick="loadUsers()">Manage Users</button>
                    <button class="btn" onclick="viewActivity()">View Activity</button>
                </div>
                
                <div class="action-card">
                    <h3>🛍️ Product Management</h3>
                    <p>Add, edit, and manage your product catalog.</p>
                    <button class="btn primary" onclick="loadProducts()">Manage Products</button>
                    <button class="btn" onclick="loadCategories()">Categories</button>
                </div>
                
                <div class="action-card">
                    <h3>📦 Order Management</h3>
                    <p>Process orders and manage fulfillment.</p>
                    <button class="btn primary" onclick="loadOrders()">View Orders</button>
                    <button class="btn" onclick="loadInvoices()">Invoices</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let authToken = null;
        let currentUser = null;
        
        // Check authentication on page load
        window.addEventListener('load', function() {
            checkAuthentication();
        });
        
        function checkAuthentication() {
            authToken = localStorage.getItem('authToken');
            const userStr = localStorage.getItem('user');
            
            if (!authToken) {
                showError();
                return;
            }
            
            // Verify token with backend
            fetch('http://localhost:8001/api/auth/check', {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.authenticated) {
                    currentUser = data.data;
                    showDashboard();
                    loadDashboardData();
                } else {
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('user');
                    showError();
                }
            })
            .catch(error => {
                console.error('Auth check failed:', error);
                showError();
            });
        }
        
        function showDashboard() {
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'block';
            
            if (currentUser) {
                document.getElementById('userName').textContent = currentUser.name;
            }
        }
        
        function showError() {
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'none';
            document.getElementById('errorSection').style.display = 'block';
        }
        
        function loadDashboardData() {
            fetch('http://localhost:8001/api/dashboard/stats', {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    const stats = data.data;
                    document.getElementById('totalUsers').textContent = stats.users.total;
                    document.getElementById('totalProducts').textContent = stats.products.total;
                    document.getElementById('totalOrders').textContent = stats.orders.total;
                    document.getElementById('totalRevenue').textContent = '$' + stats.orders.total_revenue.toLocaleString();
                }
            })
            .catch(error => {
                console.error('Error loading stats:', error);
                document.getElementById('totalUsers').textContent = 'Error';
                document.getElementById('totalProducts').textContent = 'Error';
                document.getElementById('totalOrders').textContent = 'Error';
                document.getElementById('totalRevenue').textContent = 'Error';
            });
        }
        
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('http://localhost:8001/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                })
                .then(() => {
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('user');
                    window.location.href = 'admin-login.html';
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('user');
                    window.location.href = 'admin-login.html';
                });
            }
        }
        
        function redirectToLogin() {
            window.location.href = 'admin-login.html';
        }
        
        // Action functions
        function loadAnalytics() {
            alert('📊 Analytics feature would load here.\nThis is a demo - in production, this would show detailed charts and reports.');
        }
        
        function testAPI() {
            fetch('http://localhost:8001/api/auth/user', {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                alert('✅ API Test Successful!\n\n' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                alert('❌ API Test Failed!\n\n' + error.message);
            });
        }
        
        function loadUsers() {
            alert('👥 User Management feature would load here.\nThis is a demo - in production, this would show user list and management tools.');
        }
        
        function viewActivity() {
            fetch('http://localhost:8001/api/activity-log', {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                alert('📋 Activity Log:\n\n' + JSON.stringify(data.data, null, 2));
            })
            .catch(error => {
                alert('❌ Failed to load activity log: ' + error.message);
            });
        }
        
        function loadProducts() {
            alert('🛍️ Product Management feature would load here.\nThis is a demo - in production, this would show product catalog management.');
        }
        
        function loadCategories() {
            alert('📂 Category Management feature would load here.\nThis is a demo - in production, this would show category management tools.');
        }
        
        function loadOrders() {
            alert('📦 Order Management feature would load here.\nThis is a demo - in production, this would show order processing interface.');
        }
        
        function loadInvoices() {
            alert('🧾 Invoice Management feature would load here.\nThis is a demo - in production, this would show invoice generation and management.');
        }
        
        console.log('🔐 Admin Dashboard Loaded!');
        console.log('🛡️ Authentication: Required');
        console.log('📊 Backend API: http://localhost:8001');
    </script>
</body>
</html>
