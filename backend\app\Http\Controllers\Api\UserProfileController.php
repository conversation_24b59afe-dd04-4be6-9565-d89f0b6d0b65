<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserProfile;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class UserProfileController extends Controller
{
    /**
     * Display the user's profile.
     */
    public function show(): JsonResponse
    {
        $user = Auth::user();
        $profile = $user->profile ?: new UserProfile(['user_id' => $user->id]);

        return response()->json([
            'success' => true,
            'message' => 'Profile retrieved successfully',
            'data' => [
                'user' => $user,
                'profile' => $profile,
                'addresses' => $user->addresses,
                'statistics' => [
                    'total_orders' => $user->orders()->count(),
                    'total_spent' => $user->orders()->where('status', 'completed')->sum('total_amount'),
                    'total_reviews' => $user->reviews()->count(),
                    'wishlist_items' => $user->wishlists()->withCount('items')->get()->sum('items_count'),
                ]
            ]
        ]);
    }

    /**
     * Update the user's profile.
     */
    public function update(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $validated = $request->validate([
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => ['nullable', Rule::in(['male', 'female', 'other'])],
            'bio' => 'nullable|string|max:1000',
            'website' => 'nullable|url|max:255',
            'facebook' => 'nullable|string|max:255',
            'twitter' => 'nullable|string|max:255',
            'instagram' => 'nullable|string|max:255',
            'linkedin' => 'nullable|string|max:255',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:5',
            'currency' => 'nullable|string|max:3',
            'preferences' => 'nullable|array',
            'settings' => 'nullable|array',
            // User basic info
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
        ]);

        // Update user basic info
        $userFields = array_intersect_key($validated, array_flip(['name', 'email', 'phone']));
        if (!empty($userFields)) {
            $user->update($userFields);
        }

        // Update or create profile
        $profileFields = array_diff_key($validated, array_flip(['name', 'email', 'phone']));
        if (!empty($profileFields)) {
            $profile = $user->profile ?: new UserProfile(['user_id' => $user->id]);
            $profile->fill($profileFields);
            $profile->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => [
                'user' => $user->fresh(),
                'profile' => $user->profile
            ]
        ]);
    }

    /**
     * Upload profile image.
     */
    public function uploadImage(Request $request): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $user = Auth::user();
        $profile = $user->profile ?: UserProfile::create(['user_id' => $user->id]);

        // Delete old image if exists
        if ($profile->profile_image) {
            Storage::disk('public')->delete($profile->profile_image);
        }

        // Store new image
        $path = $request->file('image')->store('profiles', 'public');
        
        $profile->update(['profile_image' => $path]);

        return response()->json([
            'success' => true,
            'message' => 'Profile image uploaded successfully',
            'data' => [
                'profile_image' => $profile->profile_image,
                'profile_image_url' => $profile->profile_image_url
            ]
        ]);
    }

    /**
     * Delete profile image.
     */
    public function deleteImage(): JsonResponse
    {
        $user = Auth::user();
        $profile = $user->profile;

        if (!$profile || !$profile->profile_image) {
            return response()->json([
                'success' => false,
                'message' => 'No profile image to delete'
            ], 404);
        }

        // Delete image file
        Storage::disk('public')->delete($profile->profile_image);
        
        // Update profile
        $profile->update(['profile_image' => null]);

        return response()->json([
            'success' => true,
            'message' => 'Profile image deleted successfully'
        ]);
    }

    /**
     * Update notification preferences.
     */
    public function updateNotificationPreferences(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'order_updates' => 'boolean',
            'promotions' => 'boolean',
            'newsletter' => 'boolean',
            'review_reminders' => 'boolean',
            'price_drop_alerts' => 'boolean',
            'back_in_stock_alerts' => 'boolean',
        ]);

        $user = Auth::user();
        $profile = $user->profile ?: UserProfile::create(['user_id' => $user->id]);
        
        $profile->updateNotificationPreferences($validated);

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences updated successfully',
            'data' => $profile->getNotificationPreferences()
        ]);
    }

    /**
     * Update app settings.
     */
    public function updateAppSettings(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'theme' => ['nullable', Rule::in(['light', 'dark', 'auto'])],
            'items_per_page' => 'nullable|integer|min:10|max:100',
            'default_view' => ['nullable', Rule::in(['grid', 'list'])],
            'currency_display' => ['nullable', Rule::in(['symbol', 'code'])],
            'date_format' => ['nullable', Rule::in(['Y-m-d', 'd/m/Y', 'm/d/Y'])],
            'time_format' => ['nullable', Rule::in(['24', '12'])],
        ]);

        $user = Auth::user();
        $profile = $user->profile ?: UserProfile::create(['user_id' => $user->id]);
        
        $profile->updateAppSettings($validated);

        return response()->json([
            'success' => true,
            'message' => 'App settings updated successfully',
            'data' => $profile->getAppSettings()
        ]);
    }

    /**
     * Get user activity summary.
     */
    public function getActivitySummary(): JsonResponse
    {
        $user = Auth::user();
        
        $summary = [
            'recent_orders' => $user->orders()
                ->with(['items.product'])
                ->latest()
                ->limit(5)
                ->get(),
            'recent_reviews' => $user->reviews()
                ->with('product')
                ->latest()
                ->limit(5)
                ->get(),
            'wishlist_items' => $user->wishlists()
                ->with(['items.product'])
                ->get()
                ->flatMap->items
                ->take(10),
            'statistics' => [
                'orders_this_month' => $user->orders()
                    ->whereMonth('created_at', now()->month)
                    ->count(),
                'spent_this_month' => $user->orders()
                    ->whereMonth('created_at', now()->month)
                    ->where('status', 'completed')
                    ->sum('total_amount'),
                'reviews_count' => $user->reviews()->count(),
                'average_rating_given' => $user->reviews()->avg('rating'),
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Activity summary retrieved successfully',
            'data' => $summary
        ]);
    }

    /**
     * Get user preferences.
     */
    public function getPreferences(): JsonResponse
    {
        $user = Auth::user();
        $profile = $user->profile;

        $preferences = [
            'notifications' => $profile ? $profile->getNotificationPreferences() : [],
            'app_settings' => $profile ? $profile->getAppSettings() : [],
            'privacy' => [
                'profile_visibility' => 'private', // Can be extended
                'show_activity' => false,
                'allow_messages' => true,
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Preferences retrieved successfully',
            'data' => $preferences
        ]);
    }

    /**
     * Export user data.
     */
    public function exportData(): JsonResponse
    {
        $user = Auth::user()->load([
            'profile',
            'addresses',
            'orders.items.product',
            'reviews.product',
            'wishlists.items.product'
        ]);

        $exportData = [
            'user_info' => [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'created_at' => $user->created_at,
            ],
            'profile' => $user->profile,
            'addresses' => $user->addresses,
            'orders' => $user->orders,
            'reviews' => $user->reviews,
            'wishlists' => $user->wishlists,
            'exported_at' => now(),
        ];

        return response()->json([
            'success' => true,
            'message' => 'User data exported successfully',
            'data' => $exportData
        ]);
    }

    /**
     * Delete user account.
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        $request->validate([
            'password' => 'required|string',
            'confirmation' => 'required|string|in:DELETE_MY_ACCOUNT'
        ]);

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid password'
            ], 422);
        }

        // Delete user data
        $user->addresses()->delete();
        $user->reviews()->delete();
        $user->wishlists()->delete();
        if ($user->profile) {
            // Delete profile image
            if ($user->profile->profile_image) {
                Storage::disk('public')->delete($user->profile->profile_image);
            }
            $user->profile->delete();
        }

        // Soft delete user
        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'Account deleted successfully'
        ]);
    }
}
