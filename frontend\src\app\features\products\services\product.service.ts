import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  sku: string;
  price: number;
  sale_price?: number;
  cost_price?: number;
  stock_quantity: number;
  manage_stock: boolean;
  stock_status: 'in_stock' | 'out_of_stock' | 'on_backorder';
  low_stock_threshold: number;
  weight?: number;
  length?: number;
  width?: number;
  height?: number;
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  meta_title?: string;
  meta_description?: string;
  attributes?: any;
  category_id: number;
  category?: Category;
  images?: ProductImage[];
  variations?: ProductVariation[];
  main_image_url?: string;
  effective_price: number;
  is_on_sale: boolean;
  sale_percentage?: number;
  total_sales: number;
  average_rating?: number;
  reviews_count: number;
  creator?: { id: number; name: string };
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parent_id?: number;
  is_active: boolean;
  featured: boolean;
  sort_order: number;
  children?: Category[];
  products_count?: number;
}

export interface ProductImage {
  id: number;
  product_id: number;
  image_url: string;
  alt_text?: string;
  sort_order: number;
  is_primary: boolean;
}

export interface ProductVariation {
  id: number;
  product_id: number;
  sku: string;
  price: number;
  sale_price?: number;
  stock_quantity: number;
  manage_stock: boolean;
  stock_status: string;
  attributes: any;
  image?: string;
  is_active: boolean;
  effective_price: number;
}

export interface ProductFilters {
  search?: string;
  category_id?: number;
  status?: string;
  stock_status?: string;
  featured?: boolean;
  price_min?: number;
  price_max?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  per_page?: number;
  page?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  links: {
    first: string;
    last: string;
    prev?: string;
    next?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private readonly API_URL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get paginated list of products
   */
  getProducts(filters: ProductFilters = {}): Observable<PaginatedResponse<Product>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof ProductFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<PaginatedResponse<Product>>(`${this.API_URL}/products`, { params });
  }

  /**
   * Get single product by ID
   */
  getProduct(id: number): Observable<ApiResponse<Product>> {
    return this.http.get<ApiResponse<Product>>(`${this.API_URL}/products/${id}`);
  }

  /**
   * Create new product
   */
  createProduct(productData: Partial<Product>): Observable<ApiResponse<Product>> {
    return this.http.post<ApiResponse<Product>>(`${this.API_URL}/products`, productData);
  }

  /**
   * Update existing product
   */
  updateProduct(id: number, productData: Partial<Product>): Observable<ApiResponse<Product>> {
    return this.http.put<ApiResponse<Product>>(`${this.API_URL}/products/${id}`, productData);
  }

  /**
   * Delete product
   */
  deleteProduct(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.API_URL}/products/${id}`);
  }

  /**
   * Bulk update product status
   */
  bulkUpdateStatus(productIds: number[], status: string): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/products/bulk-update-status`, {
      product_ids: productIds,
      status
    });
  }

  /**
   * Duplicate product
   */
  duplicateProduct(id: number): Observable<ApiResponse<Product>> {
    return this.http.post<ApiResponse<Product>>(`${this.API_URL}/products/${id}/duplicate`, {});
  }

  /**
   * Update product stock
   */
  updateStock(id: number, stockData: { stock_quantity: number; stock_status: string }): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/products/${id}/update-stock`, stockData);
  }

  /**
   * Get product statistics
   */
  getStatistics(): Observable<ApiResponse<any>> {
    return this.http.get<ApiResponse<any>>(`${this.API_URL}/products/statistics`);
  }

  /**
   * Get categories
   */
  getCategories(): Observable<ApiResponse<Category[]>> {
    return this.http.get<ApiResponse<Category[]>>(`${this.API_URL}/categories`);
  }

  /**
   * Get category tree
   */
  getCategoryTree(): Observable<ApiResponse<Category[]>> {
    return this.http.get<ApiResponse<Category[]>>(`${this.API_URL}/categories/tree`);
  }

  /**
   * Create new category
   */
  createCategory(categoryData: Partial<Category>): Observable<ApiResponse<Category>> {
    return this.http.post<ApiResponse<Category>>(`${this.API_URL}/categories`, categoryData);
  }

  /**
   * Update category
   */
  updateCategory(id: number, categoryData: Partial<Category>): Observable<ApiResponse<Category>> {
    return this.http.put<ApiResponse<Category>>(`${this.API_URL}/categories/${id}`, categoryData);
  }

  /**
   * Delete category
   */
  deleteCategory(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.API_URL}/categories/${id}`);
  }

  /**
   * Upload product image
   */
  uploadImage(productId: number, file: File, altText?: string): Observable<ApiResponse<ProductImage>> {
    const formData = new FormData();
    formData.append('image', file);
    if (altText) {
      formData.append('alt_text', altText);
    }

    return this.http.post<ApiResponse<ProductImage>>(`${this.API_URL}/products/${productId}/images`, formData);
  }

  /**
   * Delete product image
   */
  deleteImage(productId: number, imageId: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.API_URL}/products/${productId}/images/${imageId}`);
  }

  /**
   * Set primary image
   */
  setPrimaryImage(productId: number, imageId: number): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.API_URL}/products/${productId}/images/${imageId}/set-primary`, {});
  }

  /**
   * Get product variations
   */
  getVariations(productId: number): Observable<ApiResponse<ProductVariation[]>> {
    return this.http.get<ApiResponse<ProductVariation[]>>(`${this.API_URL}/products/${productId}/variations`);
  }

  /**
   * Create product variation
   */
  createVariation(productId: number, variationData: Partial<ProductVariation>): Observable<ApiResponse<ProductVariation>> {
    return this.http.post<ApiResponse<ProductVariation>>(`${this.API_URL}/products/${productId}/variations`, variationData);
  }

  /**
   * Update product variation
   */
  updateVariation(productId: number, variationId: number, variationData: Partial<ProductVariation>): Observable<ApiResponse<ProductVariation>> {
    return this.http.put<ApiResponse<ProductVariation>>(`${this.API_URL}/products/${productId}/variations/${variationId}`, variationData);
  }

  /**
   * Delete product variation
   */
  deleteVariation(productId: number, variationId: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.API_URL}/products/${productId}/variations/${variationId}`);
  }

  /**
   * Search products
   */
  searchProducts(query: string, limit: number = 10): Observable<ApiResponse<Product[]>> {
    const params = new HttpParams()
      .set('search', query)
      .set('per_page', limit.toString());

    return this.http.get<ApiResponse<Product[]>>(`${this.API_URL}/products/search`, { params });
  }

  /**
   * Get low stock products
   */
  getLowStockProducts(): Observable<ApiResponse<Product[]>> {
    return this.http.get<ApiResponse<Product[]>>(`${this.API_URL}/products/low-stock`);
  }

  /**
   * Get featured products
   */
  getFeaturedProducts(): Observable<ApiResponse<Product[]>> {
    return this.http.get<ApiResponse<Product[]>>(`${this.API_URL}/products/featured`);
  }

  /**
   * Export products
   */
  exportProducts(format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    return this.http.get(`${this.API_URL}/products/export`, {
      params: { format },
      responseType: 'blob'
    });
  }

  /**
   * Import products
   */
  importProducts(file: File): Observable<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<ApiResponse<any>>(`${this.API_URL}/products/import`, formData);
  }
}
