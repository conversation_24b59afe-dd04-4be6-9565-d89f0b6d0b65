// Test authentication system
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAuth() {
    console.log('🔐 Testing Authentication System...\n');
    
    try {
        // Test 1: Try accessing protected route without auth
        console.log('1. Testing protected route without authentication:');
        const response1 = await fetch('http://localhost:8001/api/dashboard/stats');
        const data1 = await response1.json();
        console.log('   Status:', response1.status);
        console.log('   Response:', data1);
        console.log('   ✅ Correctly blocked unauthorized access\n');
        
        // Test 2: Login with correct credentials
        console.log('2. Testing login with correct credentials:');
        const loginResponse = await fetch('http://localhost:8001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123'
            })
        });
        const loginData = await loginResponse.json();
        console.log('   Status:', loginResponse.status);
        console.log('   Response:', loginData);
        
        if (loginData.success) {
            const token = loginData.data.token;
            console.log('   ✅ Login successful! Token:', token.substring(0, 20) + '...\n');
            
            // Test 3: Access protected route with token
            console.log('3. Testing protected route with authentication:');
            const response3 = await fetch('http://localhost:8001/api/dashboard/stats', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            const data3 = await response3.json();
            console.log('   Status:', response3.status);
            console.log('   Response:', data3);
            console.log('   ✅ Successfully accessed protected data\n');
            
            // Test 4: Test logout
            console.log('4. Testing logout:');
            const logoutResponse = await fetch('http://localhost:8001/api/auth/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            const logoutData = await logoutResponse.json();
            console.log('   Status:', logoutResponse.status);
            console.log('   Response:', logoutData);
            console.log('   ✅ Logout successful\n');
            
            // Test 5: Try accessing with expired token
            console.log('5. Testing access with logged out token:');
            const response5 = await fetch('http://localhost:8001/api/dashboard/stats', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            const data5 = await response5.json();
            console.log('   Status:', response5.status);
            console.log('   Response:', data5);
            console.log('   ✅ Correctly blocked access with expired token\n');
            
        } else {
            console.log('   ❌ Login failed');
        }
        
        // Test 6: Login with wrong credentials
        console.log('6. Testing login with wrong credentials:');
        const wrongLoginResponse = await fetch('http://localhost:8001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'wrongpassword'
            })
        });
        const wrongLoginData = await wrongLoginResponse.json();
        console.log('   Status:', wrongLoginResponse.status);
        console.log('   Response:', wrongLoginData);
        console.log('   ✅ Correctly rejected wrong credentials\n');
        
        console.log('🎉 All authentication tests passed!');
        console.log('\n📋 Summary:');
        console.log('   ✅ Protected routes require authentication');
        console.log('   ✅ Login works with correct credentials');
        console.log('   ✅ Login fails with wrong credentials');
        console.log('   ✅ Authenticated users can access protected data');
        console.log('   ✅ Logout invalidates tokens');
        console.log('   ✅ Expired tokens are rejected');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testAuth();
