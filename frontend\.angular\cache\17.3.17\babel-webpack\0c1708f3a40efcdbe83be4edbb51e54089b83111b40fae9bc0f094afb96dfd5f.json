{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor(router) {\n    this.router = router;\n    this.title = 'E-Commerce Admin Dashboard';\n  }\n  ngOnInit() {\n    console.log('App component initialized');\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 38,\n      vars: 1,\n      consts: [[1, \"app-container\"], [1, \"dashboard\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-number\"], [1, \"welcome-section\"], [\"href\", \"http://localhost:8001\", \"target\", \"_blank\"], [\"href\", \"http://localhost:4200\", \"target\", \"_blank\"], [\"onclick\", \"testBackend()\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 2)(5, \"div\", 3)(6, \"h3\");\n          i0.ɵɵtext(7, \"Total Products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 4);\n          i0.ɵɵtext(9, \"1,234\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 3)(11, \"h3\");\n          i0.ɵɵtext(12, \"Total Orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4);\n          i0.ɵɵtext(14, \"567\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 3)(16, \"h3\");\n          i0.ɵɵtext(17, \"Total Users\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 4);\n          i0.ɵɵtext(19, \"890\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 3)(21, \"h3\");\n          i0.ɵɵtext(22, \"Revenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 4);\n          i0.ɵɵtext(24, \"$12,345\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 5)(26, \"h2\");\n          i0.ɵɵtext(27, \"\\uD83C\\uDF89 Your E-Commerce System is Live!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"p\");\n          i0.ɵɵtext(29, \"Backend API: \");\n          i0.ɵɵelementStart(30, \"a\", 6);\n          i0.ɵɵtext(31, \"http://localhost:8001\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"p\");\n          i0.ɵɵtext(33, \"Frontend Dashboard: \");\n          i0.ɵɵelementStart(34, \"a\", 7);\n          i0.ɵɵtext(35, \"http://localhost:4200\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"button\", 8);\n          i0.ɵɵtext(37, \"Test Backend Connection\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.title);\n        }\n      },\n      dependencies: [CommonModule, MatToolbarModule, MatButtonModule, MatIconModule, MatProgressSpinnerModule],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n  color: white;\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 2.5rem;\\n  margin-bottom: 2rem;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 2rem;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 15px;\\n  padding: 1.5rem;\\n  text-align: center;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: transform 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: bold;\\n  margin: 0.5rem 0;\\n  color: #fff;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  text-align: center;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  font-size: 2rem;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  font-size: 1.1rem;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #fff;\\n  text-decoration: underline;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 1rem;\\n  margin-top: 1rem;\\n  transition: all 0.3s ease;\\n}\\n\\nbutton[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "AppComponent", "constructor", "router", "title", "ngOnInit", "console", "log", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\amit\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\amit\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, RouterOutlet } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'E-Commerce Admin Dashboard';\n\n  constructor(private router: Router) {}\n\n  ngOnInit(): void {\n    console.log('App component initialized');\n  }\n}\n", "<div class=\"app-container\">\n  <h1>{{title}}</h1>\n  <div class=\"dashboard\">\n    <div class=\"stats-grid\">\n      <div class=\"stat-card\">\n        <h3>Total Products</h3>\n        <div class=\"stat-number\">1,234</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Total Orders</h3>\n        <div class=\"stat-number\">567</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Total Users</h3>\n        <div class=\"stat-number\">890</div>\n      </div>\n      <div class=\"stat-card\">\n        <h3>Revenue</h3>\n        <div class=\"stat-number\">$12,345</div>\n      </div>\n    </div>\n\n    <div class=\"welcome-section\">\n      <h2>🎉 Your E-Commerce System is Live!</h2>\n      <p>Backend API: <a href=\"http://localhost:8001\" target=\"_blank\">http://localhost:8001</a></p>\n      <p>Frontend Dashboard: <a href=\"http://localhost:4200\" target=\"_blank\">http://localhost:4200</a></p>\n      <button onclick=\"testBackend()\">Test Backend Connection</button>\n    </div>\n  </div>\n</div>\n\n<script>\nfunction testBackend() {\n  fetch('http://localhost:8001/api/dashboard/stats')\n    .then(response => response.json())\n    .then(data => {\n      alert('Backend connection successful! ' + JSON.stringify(data, null, 2));\n    })\n    .catch(error => {\n      alert('Backend connection failed: ' + error.message);\n    });\n}\n</script>\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;;;AAgB7E,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAF1B,KAAAC,KAAK,GAAG,4BAA4B;EAEC;EAErCC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAC1C;;;uBAPWN,YAAY,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZV,YAAY;MAAAW,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBvBb,EADF,CAAAe,cAAA,aAA2B,SACrB;UAAAf,EAAA,CAAAgB,MAAA,GAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAIZjB,EAHN,CAAAe,cAAA,aAAuB,aACG,aACC,SACjB;UAAAf,EAAA,CAAAgB,MAAA,qBAAc;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACvBjB,EAAA,CAAAe,cAAA,aAAyB;UAAAf,EAAA,CAAAgB,MAAA,YAAK;UAChChB,EADgC,CAAAiB,YAAA,EAAM,EAChC;UAEJjB,EADF,CAAAe,cAAA,cAAuB,UACjB;UAAAf,EAAA,CAAAgB,MAAA,oBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACrBjB,EAAA,CAAAe,cAAA,cAAyB;UAAAf,EAAA,CAAAgB,MAAA,WAAG;UAC9BhB,EAD8B,CAAAiB,YAAA,EAAM,EAC9B;UAEJjB,EADF,CAAAe,cAAA,cAAuB,UACjB;UAAAf,EAAA,CAAAgB,MAAA,mBAAW;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACpBjB,EAAA,CAAAe,cAAA,cAAyB;UAAAf,EAAA,CAAAgB,MAAA,WAAG;UAC9BhB,EAD8B,CAAAiB,YAAA,EAAM,EAC9B;UAEJjB,EADF,CAAAe,cAAA,cAAuB,UACjB;UAAAf,EAAA,CAAAgB,MAAA,eAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAChBjB,EAAA,CAAAe,cAAA,cAAyB;UAAAf,EAAA,CAAAgB,MAAA,eAAO;UAEpChB,EAFoC,CAAAiB,YAAA,EAAM,EAClC,EACF;UAGJjB,EADF,CAAAe,cAAA,cAA6B,UACvB;UAAAf,EAAA,CAAAgB,MAAA,oDAAkC;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC3CjB,EAAA,CAAAe,cAAA,SAAG;UAAAf,EAAA,CAAAgB,MAAA,qBAAa;UAAAhB,EAAA,CAAAe,cAAA,YAAgD;UAAAf,EAAA,CAAAgB,MAAA,6BAAqB;UAAIhB,EAAJ,CAAAiB,YAAA,EAAI,EAAI;UAC7FjB,EAAA,CAAAe,cAAA,SAAG;UAAAf,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAe,cAAA,YAAgD;UAAAf,EAAA,CAAAgB,MAAA,6BAAqB;UAAIhB,EAAJ,CAAAiB,YAAA,EAAI,EAAI;UACpGjB,EAAA,CAAAe,cAAA,iBAAgC;UAAAf,EAAA,CAAAgB,MAAA,+BAAuB;UAG7DhB,EAH6D,CAAAiB,YAAA,EAAS,EAC5D,EACF,EACF;;;UA5BAjB,EAAA,CAAAkB,SAAA,GAAS;UAATlB,EAAA,CAAAmB,iBAAA,CAAAL,GAAA,CAAAlB,KAAA,CAAS;;;qBDWXR,YAAY,EAEZC,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,wBAAwB;MAAA4B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}