<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'base_cost',
        'cost_per_kg',
        'cost_per_item',
        'free_shipping_threshold',
        'min_delivery_days',
        'max_delivery_days',
        'available_countries',
        'restricted_countries',
        'max_weight',
        'max_dimensions',
        'is_active',
        'requires_signature',
        'is_trackable',
        'tracking_url',
        'sort_order',
        'settings'
    ];

    protected $casts = [
        'base_cost' => 'decimal:2',
        'cost_per_kg' => 'decimal:2',
        'cost_per_item' => 'decimal:2',
        'free_shipping_threshold' => 'decimal:2',
        'max_weight' => 'decimal:2',
        'max_dimensions' => 'decimal:2',
        'available_countries' => 'array',
        'restricted_countries' => 'array',
        'is_active' => 'boolean',
        'requires_signature' => 'boolean',
        'is_trackable' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Calculate shipping cost for given parameters.
     */
    public function calculateCost(float $weight = 0, int $itemCount = 0, float $orderValue = 0, string $country = null): float
    {
        // Check if free shipping applies
        if ($this->free_shipping_threshold && $orderValue >= $this->free_shipping_threshold) {
            return 0;
        }

        // Check if method is available for country
        if ($country && !$this->isAvailableForCountry($country)) {
            return -1; // Not available
        }

        // Check weight limits
        if ($this->max_weight && $weight > $this->max_weight) {
            return -1; // Exceeds weight limit
        }

        $cost = $this->base_cost;

        switch ($this->type) {
            case 'weight_based':
                if ($this->cost_per_kg) {
                    $cost += $weight * $this->cost_per_kg;
                }
                break;

            case 'price_based':
                // Price-based calculation (percentage of order value)
                $percentage = $this->settings['percentage'] ?? 10;
                $cost = $orderValue * ($percentage / 100);
                break;

            case 'free':
                $cost = 0;
                break;

            case 'pickup':
                $cost = 0;
                break;

            case 'flat_rate':
            default:
                // Base cost only
                if ($this->cost_per_item) {
                    $cost += $itemCount * $this->cost_per_item;
                }
                break;
        }

        return max(0, $cost);
    }

    /**
     * Check if method is available for country.
     */
    public function isAvailableForCountry(string $country): bool
    {
        // If restricted countries list exists and country is in it, not available
        if ($this->restricted_countries && in_array($country, $this->restricted_countries)) {
            return false;
        }

        // If available countries list exists, country must be in it
        if ($this->available_countries && !in_array($country, $this->available_countries)) {
            return false;
        }

        return true;
    }

    /**
     * Get estimated delivery time.
     */
    public function getEstimatedDeliveryAttribute(): string
    {
        if (!$this->min_delivery_days && !$this->max_delivery_days) {
            return 'Contact for delivery time';
        }

        if ($this->min_delivery_days === $this->max_delivery_days) {
            return $this->min_delivery_days . ' day' . ($this->min_delivery_days > 1 ? 's' : '');
        }

        return $this->min_delivery_days . '-' . $this->max_delivery_days . ' days';
    }

    /**
     * Get delivery date range.
     */
    public function getDeliveryDateRange(): array
    {
        $minDate = $this->min_delivery_days ? now()->addDays($this->min_delivery_days) : null;
        $maxDate = $this->max_delivery_days ? now()->addDays($this->max_delivery_days) : null;

        return [
            'min_date' => $minDate?->format('Y-m-d'),
            'max_date' => $maxDate?->format('Y-m-d'),
            'formatted' => $this->estimated_delivery
        ];
    }

    /**
     * Generate tracking URL for order.
     */
    public function generateTrackingUrl(string $trackingNumber): ?string
    {
        if (!$this->is_trackable || !$this->tracking_url) {
            return null;
        }

        return str_replace('{tracking_number}', $trackingNumber, $this->tracking_url);
    }

    /**
     * Check if method supports given weight and dimensions.
     */
    public function supportsPackage(float $weight = 0, array $dimensions = []): bool
    {
        // Check weight limit
        if ($this->max_weight && $weight > $this->max_weight) {
            return false;
        }

        // Check dimensions if specified
        if ($this->max_dimensions && !empty($dimensions)) {
            $maxDimension = max($dimensions);
            if ($maxDimension > $this->max_dimensions) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get method features.
     */
    public function getFeaturesAttribute(): array
    {
        $features = [];

        if ($this->is_trackable) {
            $features[] = 'Trackable';
        }

        if ($this->requires_signature) {
            $features[] = 'Signature Required';
        }

        if ($this->free_shipping_threshold) {
            $features[] = 'Free shipping over $' . number_format($this->free_shipping_threshold, 2);
        }

        if ($this->type === 'free') {
            $features[] = 'Free Shipping';
        }

        if ($this->type === 'pickup') {
            $features[] = 'Store Pickup';
        }

        return $features;
    }

    /**
     * Scope for active methods.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for methods available to country.
     */
    public function scopeAvailableToCountry($query, string $country)
    {
        return $query->where(function ($q) use ($country) {
            $q->whereNull('available_countries')
              ->orWhereJsonContains('available_countries', $country);
        })->where(function ($q) use ($country) {
            $q->whereNull('restricted_countries')
              ->orWhereJsonDoesntContain('restricted_countries', $country);
        });
    }

    /**
     * Scope ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get available shipping methods for order.
     */
    public static function getAvailableForOrder(float $weight, int $itemCount, float $orderValue, string $country): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->availableToCountry($country)
            ->ordered()
            ->get()
            ->filter(function ($method) use ($weight, $itemCount, $orderValue, $country) {
                return $method->supportsPackage($weight) && 
                       $method->calculateCost($weight, $itemCount, $orderValue, $country) >= 0;
            })
            ->map(function ($method) use ($weight, $itemCount, $orderValue, $country) {
                $method->calculated_cost = $method->calculateCost($weight, $itemCount, $orderValue, $country);
                $method->delivery_dates = $method->getDeliveryDateRange();
                return $method;
            });
    }
}
