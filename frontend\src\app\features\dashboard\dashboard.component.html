<div class="dashboard-container">
  <!-- Header Section -->
  <div class="dashboard-header">
    <div class="welcome-section" *ngIf="currentUser$ | async as user">
      <h1>{{ getGreeting() }}, {{ user.name }}!</h1>
      <p>Welcome to your E-Commerce Management Dashboard</p>
    </div>
    
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="refreshDashboard()">
        <mat-icon>refresh</mat-icon>
        Refresh
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading dashboard data...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refreshDashboard()">
      Try Again
    </button>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading && !error && stats" class="dashboard-content">
    
    <!-- Statistics Cards -->
    <div class="stats-grid">
      <!-- Users Stats -->
      <mat-card class="stat-card users-card">
        <mat-card-header>
          <div mat-card-avatar class="stat-avatar users-avatar">
            <mat-icon>people</mat-icon>
          </div>
          <mat-card-title>Users</mat-card-title>
          <mat-card-subtitle>Total registered users</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ stats.users.total | number }}</div>
          <div class="stat-details">
            <span class="stat-item">
              <mat-icon>check_circle</mat-icon>
              {{ stats.users.active }} Active
            </span>
            <span class="stat-item">
              <mat-icon>today</mat-icon>
              {{ stats.users.new_today }} New Today
            </span>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button color="primary" (click)="navigateToModule('users')">
            View All
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Products Stats -->
      <mat-card class="stat-card products-card">
        <mat-card-header>
          <div mat-card-avatar class="stat-avatar products-avatar">
            <mat-icon>inventory</mat-icon>
          </div>
          <mat-card-title>Products</mat-card-title>
          <mat-card-subtitle>Product catalog</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ stats.products.total | number }}</div>
          <div class="stat-details">
            <span class="stat-item">
              <mat-icon>visibility</mat-icon>
              {{ stats.products.published }} Published
            </span>
            <span class="stat-item warning" *ngIf="stats.products.low_stock > 0">
              <mat-icon>warning</mat-icon>
              {{ stats.products.low_stock }} Low Stock
            </span>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button color="primary" (click)="navigateToModule('products')">
            View All
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Orders Stats -->
      <mat-card class="stat-card orders-card">
        <mat-card-header>
          <div mat-card-avatar class="stat-avatar orders-avatar">
            <mat-icon>shopping_cart</mat-icon>
          </div>
          <mat-card-title>Orders</mat-card-title>
          <mat-card-subtitle>Customer orders</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ stats.orders.total | number }}</div>
          <div class="stat-details">
            <span class="stat-item">
              <mat-icon>pending</mat-icon>
              {{ stats.orders.pending }} Pending
            </span>
            <span class="stat-item">
              <mat-icon>today</mat-icon>
              {{ stats.orders.orders_today }} Today
            </span>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button color="primary" (click)="navigateToModule('orders')">
            View All
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Revenue Stats -->
      <mat-card class="stat-card revenue-card">
        <mat-card-header>
          <div mat-card-avatar class="stat-avatar revenue-avatar">
            <mat-icon>attach_money</mat-icon>
          </div>
          <mat-card-title>Revenue</mat-card-title>
          <mat-card-subtitle>Total earnings</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ formatCurrency(stats.orders.total_revenue) }}</div>
          <div class="stat-details">
            <span class="stat-item">
              <mat-icon>today</mat-icon>
              {{ formatCurrency(stats.orders.revenue_today) }} Today
            </span>
            <span class="stat-item">
              <mat-icon>trending_up</mat-icon>
              {{ stats.orders.delivered }} Completed
            </span>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button color="primary" (click)="navigateToModule('analytics')">
            View Reports
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <div class="chart-container">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Sales Overview</mat-card-title>
            <mat-card-subtitle>Last 7 days</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-wrapper" *ngIf="salesChartData.length > 0">
              <!-- Chart component would go here -->
              <p class="chart-placeholder">Sales Chart (Chart.js integration needed)</p>
            </div>
            <div *ngIf="salesChartData.length === 0" class="no-data">
              <mat-icon>bar_chart</mat-icon>
              <p>No sales data available</p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="chart-container">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Orders Trend</mat-card-title>
            <mat-card-subtitle>Last 7 days</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-wrapper" *ngIf="ordersChartData.length > 0">
              <!-- Chart component would go here -->
              <p class="chart-placeholder">Orders Chart (Chart.js integration needed)</p>
            </div>
            <div *ngIf="ordersChartData.length === 0" class="no-data">
              <mat-icon>show_chart</mat-icon>
              <p>No orders data available</p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Recent Activities -->
    <div class="activities-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Recent Activities</mat-card-title>
          <mat-card-subtitle>Latest system activities</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="recentActivities.length > 0" class="activities-list">
            <div 
              *ngFor="let activity of recentActivities" 
              class="activity-item"
            >
              <div class="activity-icon">
                <mat-icon [color]="getActivityColor(activity.subject_type)">
                  {{ getActivityIcon(activity.subject_type) }}
                </mat-icon>
              </div>
              <div class="activity-content">
                <p class="activity-description">{{ activity.description }}</p>
                <div class="activity-meta">
                  <span class="activity-user">by {{ activity.causer.name }}</span>
                  <span class="activity-time">{{ formatDate(activity.created_at) }}</span>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="recentActivities.length === 0" class="no-activities">
            <mat-icon>history</mat-icon>
            <p>No recent activities</p>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button color="primary" (click)="navigateToModule('activity-log')">
            View All Activities
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

  </div>
</div>
