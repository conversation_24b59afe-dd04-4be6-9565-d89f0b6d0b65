import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Share,
  Dimensions,
  Animated
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';

import ProductService from '../services/ProductService';
import ImageCarousel from '../components/ImageCarousel';
import ProductVariations from '../components/ProductVariations';
import ProductReviews from '../components/ProductReviews';
import RelatedProducts from '../components/RelatedProducts';
import QuantitySelector from '../components/QuantitySelector';
import LoadingSpinner from '../components/LoadingSpinner';
import { colors, spacing, typography } from '../theme';
import { formatCurrency } from '../utils/helpers';

const { width } = Dimensions.get('window');

const ProductDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const scrollY = useRef(new Animated.Value(0)).current;
  
  const { productId, product: initialProduct } = route.params;

  // State
  const [product, setProduct] = useState(initialProduct || null);
  const [loading, setLoading] = useState(!initialProduct);
  const [selectedVariation, setSelectedVariation] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [activeTab, setActiveTab] = useState('description');

  useEffect(() => {
    loadProduct();
    loadRelatedProducts();
    trackProductView();
  }, [productId]);

  useEffect(() => {
    // Setup navigation header
    navigation.setOptions({
      title: product?.name || 'Product',
      headerRight: () => (
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleShare}
          >
            <Icon name="share" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleToggleWishlist}
          >
            <Icon 
              name={product?.is_in_wishlist ? "favorite" : "favorite-border"} 
              size={24} 
              color={product?.is_in_wishlist ? colors.error : colors.text.primary} 
            />
          </TouchableOpacity>
        </View>
      )
    });
  }, [navigation, product]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      const response = await ProductService.getProduct(productId);
      
      if (response.success) {
        setProduct(response.data);
        
        // Set default variation if available
        if (response.data.variations && response.data.variations.length > 0) {
          setSelectedVariation(response.data.variations[0]);
        }
      }
    } catch (error) {
      console.error('Error loading product:', error);
      Alert.alert('Error', 'Failed to load product details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadRelatedProducts = async () => {
    try {
      const response = await ProductService.getRelatedProducts(productId);
      if (response.success) {
        setRelatedProducts(response.data);
      }
    } catch (error) {
      console.error('Error loading related products:', error);
    }
  };

  const trackProductView = async () => {
    try {
      await ProductService.trackProductView(productId);
    } catch (error) {
      // Silently fail for tracking
    }
  };

  const handleAddToCart = async () => {
    try {
      setAddingToCart(true);
      
      // Check availability first
      const availabilityResponse = await ProductService.checkAvailability(
        productId, 
        quantity, 
        selectedVariation?.id
      );
      
      if (!availabilityResponse.success || !availabilityResponse.data.available) {
        Alert.alert('Out of Stock', 'This product is currently out of stock.');
        return;
      }

      const response = await ProductService.addToCart(
        productId, 
        quantity, 
        selectedVariation?.id
      );
      
      if (response.success) {
        Alert.alert(
          'Added to Cart',
          `${product.name} has been added to your cart.`,
          [
            { text: 'Continue Shopping', style: 'cancel' },
            { text: 'View Cart', onPress: () => navigation.navigate('Cart') }
          ]
        );
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      Alert.alert('Error', 'Failed to add product to cart. Please try again.');
    } finally {
      setAddingToCart(false);
    }
  };

  const handleToggleWishlist = async () => {
    try {
      if (product.is_in_wishlist) {
        await ProductService.removeFromWishlist(productId);
        setProduct(prev => ({ ...prev, is_in_wishlist: false }));
      } else {
        await ProductService.addToWishlist(productId);
        setProduct(prev => ({ ...prev, is_in_wishlist: true }));
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      Alert.alert('Error', 'Failed to update wishlist. Please try again.');
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out ${product.name} - ${product.short_description}`,
        url: product.share_url || `https://example.com/products/${product.slug}`,
        title: product.name
      });
    } catch (error) {
      console.error('Error sharing product:', error);
    }
  };

  const handleVariationChange = (variation) => {
    setSelectedVariation(variation);
    setQuantity(1); // Reset quantity when variation changes
  };

  const getCurrentPrice = () => {
    if (selectedVariation) {
      return selectedVariation.sale_price || selectedVariation.regular_price;
    }
    return product?.sale_price || product?.regular_price || 0;
  };

  const getRegularPrice = () => {
    if (selectedVariation) {
      return selectedVariation.regular_price;
    }
    return product?.regular_price || 0;
  };

  const isOnSale = () => {
    const currentPrice = getCurrentPrice();
    const regularPrice = getRegularPrice();
    return currentPrice < regularPrice;
  };

  const getStockStatus = () => {
    if (selectedVariation) {
      return selectedVariation.stock_status;
    }
    return product?.stock_status || 'out_of_stock';
  };

  const getStockQuantity = () => {
    if (selectedVariation) {
      return selectedVariation.stock_quantity;
    }
    return product?.stock_quantity || 0;
  };

  const isInStock = () => {
    return getStockStatus() === 'in_stock' && getStockQuantity() > 0;
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'description':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.description}>{product.description}</Text>
            
            {product.attributes && product.attributes.length > 0 && (
              <View style={styles.attributesSection}>
                <Text style={styles.sectionTitle}>Specifications</Text>
                {product.attributes.map((attr, index) => (
                  <View key={index} style={styles.attributeRow}>
                    <Text style={styles.attributeName}>{attr.name}:</Text>
                    <Text style={styles.attributeValue}>{attr.value}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        );
      
      case 'reviews':
        return (
          <ProductReviews
            productId={productId}
            reviews={reviews}
            onReviewsLoad={setReviews}
          />
        );
      
      case 'shipping':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.sectionTitle}>Shipping Information</Text>
            <Text style={styles.shippingText}>
              • Free shipping on orders over $50{'\n'}
              • Standard delivery: 3-5 business days{'\n'}
              • Express delivery: 1-2 business days{'\n'}
              • International shipping available
            </Text>
          </View>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!product) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color={colors.text.secondary} />
          <Text style={styles.errorText}>Product not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Animated.ScrollView
        style={styles.scrollView}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {/* Product Images */}
        <ImageCarousel
          images={product.images}
          style={styles.imageCarousel}
        />

        {/* Product Info */}
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.productBrand}>{product.brand}</Text>
          
          {/* Price */}
          <View style={styles.priceContainer}>
            <Text style={styles.currentPrice}>
              {formatCurrency(getCurrentPrice())}
            </Text>
            {isOnSale() && (
              <Text style={styles.originalPrice}>
                {formatCurrency(getRegularPrice())}
              </Text>
            )}
            {isOnSale() && (
              <View style={styles.saleTag}>
                <Text style={styles.saleText}>SALE</Text>
              </View>
            )}
          </View>

          {/* Rating */}
          {product.average_rating > 0 && (
            <View style={styles.ratingContainer}>
              <View style={styles.stars}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <Icon
                    key={star}
                    name="star"
                    size={16}
                    color={star <= product.average_rating ? colors.warning : colors.border}
                  />
                ))}
              </View>
              <Text style={styles.ratingText}>
                ({product.review_count} review{product.review_count !== 1 ? 's' : ''})
              </Text>
            </View>
          )}

          {/* Stock Status */}
          <View style={styles.stockContainer}>
            <Icon
              name={isInStock() ? "check-circle" : "cancel"}
              size={16}
              color={isInStock() ? colors.success : colors.error}
            />
            <Text style={[
              styles.stockText,
              { color: isInStock() ? colors.success : colors.error }
            ]}>
              {isInStock() ? 'In Stock' : 'Out of Stock'}
            </Text>
            {isInStock() && getStockQuantity() <= 10 && (
              <Text style={styles.lowStockText}>
                Only {getStockQuantity()} left!
              </Text>
            )}
          </View>

          {/* Short Description */}
          <Text style={styles.shortDescription}>{product.short_description}</Text>

          {/* Variations */}
          {product.variations && product.variations.length > 0 && (
            <ProductVariations
              variations={product.variations}
              selectedVariation={selectedVariation}
              onVariationChange={handleVariationChange}
            />
          )}

          {/* Quantity Selector */}
          {isInStock() && (
            <View style={styles.quantitySection}>
              <Text style={styles.quantityLabel}>Quantity:</Text>
              <QuantitySelector
                quantity={quantity}
                onQuantityChange={setQuantity}
                maxQuantity={getStockQuantity()}
                style={styles.quantitySelector}
              />
            </View>
          )}

          {/* Add to Cart Button */}
          <TouchableOpacity
            style={[
              styles.addToCartButton,
              !isInStock() && styles.disabledButton
            ]}
            onPress={handleAddToCart}
            disabled={!isInStock() || addingToCart}
          >
            <Text style={styles.addToCartText}>
              {addingToCart ? 'Adding...' : isInStock() ? 'Add to Cart' : 'Out of Stock'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <View style={styles.tabsHeader}>
            {['description', 'reviews', 'shipping'].map((tab) => (
              <TouchableOpacity
                key={tab}
                style={[
                  styles.tab,
                  activeTab === tab && styles.activeTab
                ]}
                onPress={() => setActiveTab(tab)}
              >
                <Text style={[
                  styles.tabText,
                  activeTab === tab && styles.activeTabText
                ]}>
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          
          {renderTabContent()}
        </View>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <RelatedProducts
            products={relatedProducts}
            onProductPress={(relatedProduct) => 
              navigation.push('ProductDetail', { 
                productId: relatedProduct.id, 
                product: relatedProduct 
              })
            }
          />
        )}
      </Animated.ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: spacing.sm,
    marginLeft: spacing.xs,
  },
  scrollView: {
    flex: 1,
  },
  imageCarousel: {
    height: width,
  },
  productInfo: {
    padding: spacing.lg,
    backgroundColor: colors.background.primary,
  },
  productName: {
    fontSize: typography.h2.fontSize,
    fontWeight: typography.h2.fontWeight,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  productBrand: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
    marginBottom: spacing.md,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  currentPrice: {
    fontSize: typography.h3.fontSize,
    fontWeight: 'bold',
    color: colors.primary,
    marginRight: spacing.sm,
  },
  originalPrice: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
    textDecorationLine: 'line-through',
    marginRight: spacing.sm,
  },
  saleTag: {
    backgroundColor: colors.error,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 4,
  },
  saleText: {
    fontSize: typography.caption.fontSize,
    fontWeight: 'bold',
    color: colors.background.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  stars: {
    flexDirection: 'row',
    marginRight: spacing.sm,
  },
  ratingText: {
    fontSize: typography.caption.fontSize,
    color: colors.text.secondary,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  stockText: {
    fontSize: typography.body.fontSize,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },
  lowStockText: {
    fontSize: typography.caption.fontSize,
    color: colors.warning,
    marginLeft: spacing.sm,
    fontStyle: 'italic',
  },
  shortDescription: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
    lineHeight: 22,
    marginBottom: spacing.lg,
  },
  quantitySection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  quantityLabel: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    marginRight: spacing.md,
  },
  quantitySelector: {
    flex: 1,
  },
  addToCartButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: colors.text.disabled,
  },
  addToCartText: {
    fontSize: typography.button.fontSize,
    fontWeight: typography.button.fontWeight,
    color: colors.background.primary,
  },
  tabsContainer: {
    backgroundColor: colors.background.primary,
    marginTop: spacing.md,
  },
  tabsHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '500',
  },
  tabContent: {
    padding: spacing.lg,
  },
  description: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    lineHeight: 22,
  },
  attributesSection: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.h4.fontSize,
    fontWeight: typography.h4.fontWeight,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  attributeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  attributeName: {
    fontSize: typography.body.fontSize,
    color: colors.text.secondary,
    flex: 1,
  },
  attributeValue: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    flex: 1,
    textAlign: 'right',
  },
  shippingText: {
    fontSize: typography.body.fontSize,
    color: colors.text.primary,
    lineHeight: 22,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  errorText: {
    fontSize: typography.h3.fontSize,
    color: colors.text.secondary,
    marginTop: spacing.md,
  },
});

export default ProductDetailScreen;
