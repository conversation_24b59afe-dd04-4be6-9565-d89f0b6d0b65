import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { store } from '../store';
import { logout } from '../store/slices/authSlice';

// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:8000/api' : 'https://your-api-domain.com/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  REFRESH_TOKEN: 'refresh_token',
  SETTINGS: 'app_settings',
  CART: 'shopping_cart',
  FAVORITES: 'favorites',
  OFFLINE_DATA: 'offline_data',
};

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  errors?: any;
}

// Paginated response interface
export interface PaginatedResponse<T = any> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
    has_more_pages: boolean;
  };
}

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    async (config) => {
      // Add auth token to requests
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Add request timestamp for debugging
      config.metadata = { startTime: new Date() };
      
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log response time
      const endTime = new Date();
      const startTime = response.config.metadata?.startTime;
      const duration = startTime ? endTime.getTime() - startTime.getTime() : 0;
      
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
      
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      console.error(`❌ API Error: ${error.response?.status} ${error.config?.url}`);

      // Handle 401 Unauthorized
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          // Try to refresh token
          const refreshToken = await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
          if (refreshToken) {
            const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/refresh`, {
              refresh_token: refreshToken,
            });

            const { access_token } = response.data.data;
            await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, access_token);

            // Retry original request
            originalRequest.headers.Authorization = `Bearer ${access_token}`;
            return instance(originalRequest);
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
        }

        // If refresh fails, logout user
        store.dispatch(logout());
        await AsyncStorage.multiRemove([
          STORAGE_KEYS.AUTH_TOKEN,
          STORAGE_KEYS.REFRESH_TOKEN,
          STORAGE_KEYS.USER_DATA,
        ]);
      }

      // Handle network errors
      if (!error.response) {
        error.message = 'Network error. Please check your internet connection.';
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create API instance
export const api = createApiInstance();

// API Service class
export class ApiService {
  // Generic GET request
  static async get<T>(endpoint: string, params?: any): Promise<T> {
    try {
      const response = await api.get<ApiResponse<T>>(endpoint, { params });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Generic POST request
  static async post<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response = await api.post<ApiResponse<T>>(endpoint, data);
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Generic PUT request
  static async put<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response = await api.put<ApiResponse<T>>(endpoint, data);
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Generic DELETE request
  static async delete<T>(endpoint: string): Promise<T> {
    try {
      const response = await api.delete<ApiResponse<T>>(endpoint);
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Paginated GET request
  static async getPaginated<T>(endpoint: string, params?: any): Promise<PaginatedResponse<T>> {
    try {
      const response = await api.get<PaginatedResponse<T>>(endpoint, { params });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // File upload
  static async upload<T>(endpoint: string, file: any, additionalData?: any): Promise<T> {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: file.uri,
        type: file.type,
        name: file.name || 'upload.jpg',
      } as any);

      if (additionalData) {
        Object.keys(additionalData).forEach(key => {
          formData.append(key, additionalData[key]);
        });
      }

      const response = await api.post<ApiResponse<T>>(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Download file
  static async download(endpoint: string, filename?: string): Promise<Blob> {
    try {
      const response = await api.get(endpoint, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handler
  private static handleError(error: any): Error {
    let message = 'An unknown error occurred';

    if (error.response) {
      // Server responded with error status
      const { data, status } = error.response;
      
      if (data?.message) {
        message = data.message;
      } else if (data?.errors) {
        message = Object.values(data.errors).flat().join(', ');
      } else {
        switch (status) {
          case 400:
            message = 'Bad Request';
            break;
          case 401:
            message = 'Unauthorized';
            break;
          case 403:
            message = 'Forbidden';
            break;
          case 404:
            message = 'Not Found';
            break;
          case 422:
            message = 'Validation Error';
            break;
          case 500:
            message = 'Internal Server Error';
            break;
          default:
            message = `Error ${status}`;
        }
      }
    } else if (error.request) {
      // Network error
      message = 'Network error. Please check your internet connection.';
    } else {
      // Other error
      message = error.message || 'An unexpected error occurred';
    }

    return new Error(message);
  }
}

export default ApiService;
