{"name": "ecommerce-admin-frontend", "version": "1.0.0", "description": "E-Commerce Management System - Admin Dashboard", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "test:coverage": "ng test --code-coverage", "lint": "ng lint", "e2e": "ng e2e", "analyze": "ng build --stats-json && npx webpack-bundle-analyzer dist/stats.json"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/material": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/service-worker": "^17.0.0", "@ngrx/store": "^17.0.0", "@ngrx/effects": "^17.0.0", "@ngrx/store-devtools": "^17.0.0", "@ngrx/router-store": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "chart.js": "^4.4.0", "ng2-charts": "^5.0.0", "ngx-file-drop": "^16.0.0", "ngx-image-cropper": "^7.0.0", "ngx-quill": "^24.0.0", "quill": "^1.3.7", "ngx-pagination": "^6.0.3", "ngx-toastr": "^18.0.0", "ngx-loading": "^16.0.0", "ngx-spinner": "^17.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "file-saver": "^2.0.5"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/lodash": "^4.14.202", "@types/file-saver": "^2.0.7", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-headless": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "@angular-eslint/builder": "^17.0.0", "@angular-eslint/eslint-plugin": "^17.0.0", "@angular-eslint/eslint-plugin-template": "^17.0.0", "@angular-eslint/schematics": "^17.0.0", "@angular-eslint/template-parser": "^17.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}