.order-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    h1 {
      margin: 0 0 8px 0;
      color: #333;
      font-weight: 600;
      font-size: 28px;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 16px;
    }
  }
  
  .header-actions {
    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

.filters-card {
  margin-bottom: 24px;
  
  .filters-container {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr auto;
    gap: 16px;
    align-items: center;
    
    .search-field {
      min-width: 300px;
    }
    
    .clear-filters-btn {
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

.bulk-actions {
  margin-bottom: 24px;
  
  .bulk-actions-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .selection-count {
      font-weight: 500;
      color: #333;
    }
    
    .bulk-action-buttons {
      display: flex;
      gap: 12px;
      
      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

.table-card {
  .table-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .table-info {
      font-size: 14px;
      color: #666;
    }
    
    .table-action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 24px;
    
    mat-spinner {
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 16px;
    }
  }
  
  .table-container {
    .orders-table {
      width: 100%;
      
      .mat-column-select {
        width: 60px;
      }
      
      .mat-column-order_number {
        min-width: 180px;
      }
      
      .mat-column-customer {
        min-width: 200px;
      }
      
      .mat-column-status {
        width: 120px;
      }
      
      .mat-column-payment_status {
        width: 130px;
      }
      
      .mat-column-payment_method {
        width: 140px;
      }
      
      .mat-column-total_amount {
        width: 120px;
      }
      
      .mat-column-items_count {
        width: 100px;
      }
      
      .mat-column-created_at {
        width: 140px;
      }
      
      .mat-column-actions {
        width: 200px;
      }
      
      .order-number {
        .number {
          display: block;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }
        
        .date {
          display: block;
          font-size: 12px;
          color: #999;
        }
      }
      
      .customer-info {
        .name {
          display: block;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }
        
        .email {
          display: block;
          font-size: 12px;
          color: #666;
        }
      }
      
      .amount-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        .total {
          font-weight: 600;
          color: #333;
        }
        
        .discount {
          font-size: 12px;
          color: #4caf50;
          font-weight: 500;
        }
      }
      
      .items-count {
        font-size: 14px;
        color: #666;
      }
      
      .action-buttons {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
      }
    }
    
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 64px 24px;
      text-align: center;
      
      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #ccc;
        margin-bottom: 16px;
      }
      
      h3 {
        margin: 0 0 8px 0;
        color: #333;
        font-weight: 500;
      }
      
      p {
        margin: 0 0 24px 0;
        color: #666;
        font-size: 14px;
      }
      
      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 1400px) {
  .filters-container {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 12px;
    
    .search-field {
      grid-column: 1 / -1;
      min-width: auto;
    }
    
    .clear-filters-btn {
      grid-column: 1 / -1;
      justify-self: start;
    }
  }
}

@media (max-width: 1200px) {
  .filters-container {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .order-list-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filters-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .bulk-actions-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    
    .bulk-action-buttons {
      flex-wrap: wrap;
    }
  }
  
  .table-actions {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  // Hide some columns on mobile
  .orders-table {
    .mat-column-payment_method,
    .mat-column-items_count,
    .mat-column-created_at {
      display: none;
    }
  }
}

@media (max-width: 480px) {
  .orders-table {
    .mat-column-payment_status {
      display: none;
    }
    
    .action-buttons {
      flex-direction: column;
      gap: 2px;
    }
  }
}

// Status chip colors
::ng-deep {
  .mat-chip {
    font-size: 12px;
    min-height: 24px;
    line-height: 24px;
    
    &.mat-primary {
      background-color: #2196f3 !important;
      color: white !important;
    }
    
    &.mat-accent {
      background-color: #ff9800 !important;
      color: white !important;
    }
    
    &.mat-warn {
      background-color: #f44336 !important;
      color: white !important;
    }
  }
  
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }
  
  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
  
  .warning-snackbar {
    background-color: #ff9800 !important;
    color: white !important;
  }
  
  .info-snackbar {
    background-color: #2196f3 !important;
    color: white !important;
  }
}
