<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Product;
use Carbon\Carbon;

class OrdersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🛒 Seeding orders...');

        // Get customers and products
        $customers = User::role('customer')->get();
        $products = Product::where('is_active', true)->get();

        if ($customers->isEmpty() || $products->isEmpty()) {
            $this->command->warn('No customers or products found. Skipping order seeding.');
            return;
        }

        $statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
        $paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
        $paymentMethods = ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'cash_on_delivery'];

        // Create orders for the last 90 days
        for ($i = 0; $i < 50; $i++) {
            $customer = $customers->random();
            $orderDate = Carbon::now()->subDays(rand(0, 90));
            
            $status = $statuses[array_rand($statuses)];
            $paymentStatus = $paymentStatuses[array_rand($paymentStatuses)];
            $paymentMethod = $paymentMethods[array_rand($paymentMethods)];

            // Calculate order amounts
            $subtotal = 0;
            $orderProducts = $products->random(rand(1, 5));
            
            foreach ($orderProducts as $product) {
                $quantity = rand(1, 3);
                $subtotal += $product->price * $quantity;
            }

            $taxRate = 0.10; // 10% tax
            $taxAmount = $subtotal * $taxRate;
            $shippingAmount = $subtotal > 100 ? 0 : 15; // Free shipping over $100
            $discountAmount = rand(0, 1) ? rand(5, 20) : 0; // Random discount
            $totalAmount = $subtotal + $taxAmount + $shippingAmount - $discountAmount;

            $order = Order::create([
                'order_number' => 'ORD-' . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                'user_id' => $customer->id,
                'status' => $status,
                'payment_status' => $paymentStatus,
                'payment_method' => $paymentMethod,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'currency' => 'USD',
                'notes' => $this->getRandomOrderNote(),
                'shipping_address' => $this->getRandomAddress(),
                'billing_address' => $this->getRandomAddress(),
                'coupon_code' => $discountAmount > 0 ? 'SAVE' . $discountAmount : null,
                'coupon_discount' => $discountAmount,
                'created_at' => $orderDate,
                'updated_at' => $orderDate,
                'created_by' => $customer->id,
            ]);

            // Set status-specific timestamps
            if (in_array($status, ['processing', 'shipped', 'delivered'])) {
                $order->processed_at = $orderDate->copy()->addHours(rand(1, 24));
            }
            if (in_array($status, ['shipped', 'delivered'])) {
                $order->shipped_at = $orderDate->copy()->addDays(rand(1, 3));
            }
            if ($status === 'delivered') {
                $order->delivered_at = $orderDate->copy()->addDays(rand(3, 7));
            }
            if ($status === 'cancelled') {
                $order->cancelled_at = $orderDate->copy()->addHours(rand(1, 48));
            }
            $order->save();

            // Create order items
            foreach ($orderProducts as $product) {
                $quantity = rand(1, 3);
                $unitPrice = $product->price;
                $totalPrice = $unitPrice * $quantity;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'product_options' => $this->getRandomProductOptions(),
                ]);
            }
        }

        $this->command->info('✅ Orders seeded successfully!');
        $this->command->info('📊 Created ' . Order::count() . ' orders with ' . OrderItem::count() . ' order items');
    }

    /**
     * Get random order note.
     */
    private function getRandomOrderNote(): ?string
    {
        $notes = [
            'Please deliver between 9 AM - 5 PM',
            'Leave package at front door',
            'Call before delivery',
            'Gift wrapping requested',
            'Fragile items - handle with care',
            'Rush order - expedite shipping',
            null, // Some orders have no notes
            null,
            null,
        ];

        return $notes[array_rand($notes)];
    }

    /**
     * Get random address.
     */
    private function getRandomAddress(): array
    {
        $addresses = [
            [
                'name' => 'John Doe',
                'company' => '',
                'address_line_1' => '123 Main Street',
                'address_line_2' => 'Apt 4B',
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10001',
                'country' => 'US',
                'phone' => '******-123-4567',
            ],
            [
                'name' => 'Jane Smith',
                'company' => 'Tech Corp',
                'address_line_1' => '456 Business Ave',
                'address_line_2' => 'Suite 200',
                'city' => 'San Francisco',
                'state' => 'CA',
                'postal_code' => '94102',
                'country' => 'US',
                'phone' => '******-987-6543',
            ],
            [
                'name' => 'Bob Johnson',
                'company' => '',
                'address_line_1' => '789 Oak Street',
                'address_line_2' => '',
                'city' => 'Chicago',
                'state' => 'IL',
                'postal_code' => '60601',
                'country' => 'US',
                'phone' => '******-456-7890',
            ],
            [
                'name' => 'Alice Brown',
                'company' => '',
                'address_line_1' => '321 Pine Road',
                'address_line_2' => 'Unit 15',
                'city' => 'Miami',
                'state' => 'FL',
                'postal_code' => '33101',
                'country' => 'US',
                'phone' => '******-321-6547',
            ],
        ];

        return $addresses[array_rand($addresses)];
    }

    /**
     * Get random product options.
     */
    private function getRandomProductOptions(): ?array
    {
        $options = [
            ['size' => 'Medium', 'color' => 'Blue'],
            ['size' => 'Large', 'color' => 'Red'],
            ['color' => 'Black'],
            ['size' => 'Small'],
            null, // Some products have no options
            null,
        ];

        return $options[array_rand($options)];
    }
}
