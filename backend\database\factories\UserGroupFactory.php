<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserGroup>
 */
class UserGroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $colors = [
            '#FF5722', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
            '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
            '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800',
        ];

        return [
            'name' => fake()->words(2, true) . ' Group',
            'description' => fake()->sentence(),
            'color' => fake()->randomElement($colors),
            'discount_percentage' => fake()->randomFloat(2, 0, 30),
            'is_active' => true,
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the group is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a group with specific discount percentage.
     */
    public function withDiscount(float $percentage): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_percentage' => $percentage,
        ]);
    }

    /**
     * Create a VIP group.
     */
    public function vip(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'VIP Customers',
            'description' => 'High-value customers with special privileges',
            'color' => '#FFD700',
            'discount_percentage' => 20.00,
        ]);
    }

    /**
     * Create a premium group.
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Premium Members',
            'description' => 'Premium membership customers',
            'color' => '#9C27B0',
            'discount_percentage' => 15.00,
        ]);
    }

    /**
     * Create a regular group.
     */
    public function regular(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Regular Customers',
            'description' => 'Standard customers',
            'color' => '#2196F3',
            'discount_percentage' => 5.00,
        ]);
    }
}
