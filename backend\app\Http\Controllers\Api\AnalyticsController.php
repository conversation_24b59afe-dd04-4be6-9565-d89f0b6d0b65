<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Transaction;

class AnalyticsController extends BaseController
{
    /**
     * Get dashboard analytics data.
     */
    public function dashboard(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '7d'); // 7d, 30d, 90d, 1y
            $startDate = $this->getStartDate($period);
            $endDate = now();

            $data = [
                'overview' => $this->getOverviewStats($startDate, $endDate),
                'sales_chart' => $this->getSalesChart($startDate, $endDate),
                'orders_by_status' => $this->getOrdersByStatus($startDate, $endDate),
                'top_products' => $this->getTopProducts($startDate, $endDate),
                'recent_orders' => $this->getRecentOrders(),
                'customer_growth' => $this->getCustomerGrowth($startDate, $endDate),
            ];

            return $this->sendResponse($data, 'Dashboard analytics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to get dashboard analytics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get user analytics.
     */
    public function userAnalytics(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30d');
            $startDate = $this->getStartDate($period);
            $endDate = now();

            $data = [
                'total_users' => User::count(),
                'new_users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
                'active_users' => User::where('is_active', true)->count(),
                'user_growth' => $this->getUserGrowthChart($startDate, $endDate),
                'user_roles' => $this->getUsersByRole(),
                'top_customers' => $this->getTopCustomers($startDate, $endDate),
            ];

            return $this->sendResponse($data, 'User analytics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to get user analytics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get product analytics.
     */
    public function productAnalytics(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30d');
            $startDate = $this->getStartDate($period);
            $endDate = now();

            $data = [
                'total_products' => Product::count(),
                'active_products' => Product::where('is_active', true)->count(),
                'out_of_stock' => Product::where('stock_quantity', '<=', 0)->count(),
                'low_stock' => Product::where('stock_quantity', '>', 0)->where('stock_quantity', '<=', 10)->count(),
                'top_selling' => $this->getTopSellingProducts($startDate, $endDate),
                'category_performance' => $this->getCategoryPerformance($startDate, $endDate),
                'inventory_value' => $this->getInventoryValue(),
            ];

            return $this->sendResponse($data, 'Product analytics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to get product analytics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get order analytics.
     */
    public function orderAnalytics(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30d');
            $startDate = $this->getStartDate($period);
            $endDate = now();

            $data = [
                'total_orders' => Order::whereBetween('created_at', [$startDate, $endDate])->count(),
                'completed_orders' => Order::where('status', 'completed')->whereBetween('created_at', [$startDate, $endDate])->count(),
                'pending_orders' => Order::where('status', 'pending')->count(),
                'cancelled_orders' => Order::where('status', 'cancelled')->whereBetween('created_at', [$startDate, $endDate])->count(),
                'average_order_value' => $this->getAverageOrderValue($startDate, $endDate),
                'orders_by_day' => $this->getOrdersByDay($startDate, $endDate),
                'orders_by_status' => $this->getOrdersByStatus($startDate, $endDate),
            ];

            return $this->sendResponse($data, 'Order analytics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to get order analytics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get revenue analytics.
     */
    public function revenueAnalytics(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30d');
            $startDate = $this->getStartDate($period);
            $endDate = now();

            $data = [
                'total_revenue' => $this->getTotalRevenue($startDate, $endDate),
                'revenue_growth' => $this->getRevenueGrowth($startDate, $endDate),
                'revenue_by_day' => $this->getRevenueByDay($startDate, $endDate),
                'revenue_by_month' => $this->getRevenueByMonth(),
                'payment_methods' => $this->getRevenueByPaymentMethod($startDate, $endDate),
            ];

            return $this->sendResponse($data, 'Revenue analytics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to get revenue analytics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get start date based on period.
     */
    private function getStartDate(string $period): Carbon
    {
        return match ($period) {
            '7d' => now()->subDays(7),
            '30d' => now()->subDays(30),
            '90d' => now()->subDays(90),
            '1y' => now()->subYear(),
            default => now()->subDays(7),
        };
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'total_revenue' => $this->getTotalRevenue($startDate, $endDate),
            'total_orders' => Order::whereBetween('created_at', [$startDate, $endDate])->count(),
            'total_customers' => User::role('customer')->whereBetween('created_at', [$startDate, $endDate])->count(),
            'total_products' => Product::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'low_stock_products' => Product::where('stock_quantity', '>', 0)->where('stock_quantity', '<=', 10)->count(),
        ];
    }

    /**
     * Get sales chart data.
     */
    private function getSalesChart(Carbon $startDate, Carbon $endDate): array
    {
        $sales = Order::selectRaw('DATE(created_at) as date, SUM(total_amount) as total')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $labels = [];
        $values = [];
        
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $labels[] = $currentDate->format('M d');
            
            $dayTotal = $sales->where('date', $dateStr)->first();
            $values[] = $dayTotal ? (float) $dayTotal->total : 0;
            
            $currentDate->addDay();
        }

        return [
            'labels' => $labels,
            'values' => $values,
        ];
    }

    /**
     * Get orders by status.
     */
    private function getOrdersByStatus(Carbon $startDate, Carbon $endDate): array
    {
        return Order::selectRaw('status, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('status')
            ->get()
            ->map(function ($item) {
                return [
                    'status' => ucfirst($item->status),
                    'count' => $item->count,
                ];
            })
            ->toArray();
    }

    /**
     * Get top products.
     */
    private function getTopProducts(Carbon $startDate, Carbon $endDate): array
    {
        // This would need order_items table to be properly implemented
        return Product::select('id', 'name', 'price', 'stock_quantity')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    /**
     * Get recent orders.
     */
    private function getRecentOrders(): array
    {
        return Order::with(['user:id,name,email'])
            ->select('id', 'user_id', 'total_amount', 'status', 'created_at')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get customer growth.
     */
    private function getCustomerGrowth(Carbon $startDate, Carbon $endDate): array
    {
        $customers = User::role('customer')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $labels = [];
        $values = [];
        
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $labels[] = $currentDate->format('M d');
            
            $dayCount = $customers->where('date', $dateStr)->first();
            $values[] = $dayCount ? (int) $dayCount->count : 0;
            
            $currentDate->addDay();
        }

        return [
            'labels' => $labels,
            'values' => $values,
        ];
    }

    /**
     * Get total revenue.
     */
    private function getTotalRevenue(Carbon $startDate, Carbon $endDate): float
    {
        return (float) Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');
    }

    /**
     * Get user growth chart.
     */
    private function getUserGrowthChart(Carbon $startDate, Carbon $endDate): array
    {
        return $this->getCustomerGrowth($startDate, $endDate);
    }

    /**
     * Get users by role.
     */
    private function getUsersByRole(): array
    {
        return User::selectRaw('roles.name as role, COUNT(*) as count')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->groupBy('roles.name')
            ->get()
            ->map(function ($item) {
                return [
                    'role' => ucfirst($item->role),
                    'count' => $item->count,
                ];
            })
            ->toArray();
    }

    /**
     * Get top customers.
     */
    private function getTopCustomers(Carbon $startDate, Carbon $endDate): array
    {
        return User::role('customer')
            ->select('id', 'name', 'email')
            ->withCount(['orders' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->withSum(['orders' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->where('status', '!=', 'cancelled');
            }], 'total_amount')
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get top selling products.
     */
    private function getTopSellingProducts(Carbon $startDate, Carbon $endDate): array
    {
        // This would need proper order_items implementation
        return Product::select('id', 'name', 'price', 'stock_quantity')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get category performance.
     */
    private function getCategoryPerformance(Carbon $startDate, Carbon $endDate): array
    {
        return DB::table('categories')
            ->select('categories.name', DB::raw('COUNT(products.id) as product_count'))
            ->leftJoin('products', 'categories.id', '=', 'products.category_id')
            ->where('categories.is_active', true)
            ->groupBy('categories.id', 'categories.name')
            ->orderBy('product_count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get inventory value.
     */
    private function getInventoryValue(): float
    {
        return (float) Product::where('is_active', true)
            ->selectRaw('SUM(price * stock_quantity) as total_value')
            ->value('total_value') ?? 0;
    }

    /**
     * Get average order value.
     */
    private function getAverageOrderValue(Carbon $startDate, Carbon $endDate): float
    {
        return (float) Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->avg('total_amount') ?? 0;
    }

    /**
     * Get orders by day.
     */
    private function getOrdersByDay(Carbon $startDate, Carbon $endDate): array
    {
        $orders = Order::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $labels = [];
        $values = [];

        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $labels[] = $currentDate->format('M d');

            $dayCount = $orders->where('date', $dateStr)->first();
            $values[] = $dayCount ? (int) $dayCount->count : 0;

            $currentDate->addDay();
        }

        return [
            'labels' => $labels,
            'values' => $values,
        ];
    }

    /**
     * Get revenue growth.
     */
    private function getRevenueGrowth(Carbon $startDate, Carbon $endDate): array
    {
        $currentPeriodRevenue = $this->getTotalRevenue($startDate, $endDate);

        $previousStartDate = $startDate->copy()->sub($endDate->diffInDays($startDate), 'days');
        $previousEndDate = $startDate->copy()->subDay();
        $previousPeriodRevenue = $this->getTotalRevenue($previousStartDate, $previousEndDate);

        $growthPercentage = $previousPeriodRevenue > 0
            ? (($currentPeriodRevenue - $previousPeriodRevenue) / $previousPeriodRevenue) * 100
            : 0;

        return [
            'current_period' => $currentPeriodRevenue,
            'previous_period' => $previousPeriodRevenue,
            'growth_percentage' => round($growthPercentage, 2),
        ];
    }

    /**
     * Get revenue by day.
     */
    private function getRevenueByDay(Carbon $startDate, Carbon $endDate): array
    {
        return $this->getSalesChart($startDate, $endDate);
    }

    /**
     * Get revenue by month.
     */
    private function getRevenueByMonth(): array
    {
        $revenue = Order::selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(total_amount) as total')
            ->where('status', '!=', 'cancelled')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        $labels = [];
        $values = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $year = $date->year;
            $month = $date->month;

            $labels[] = $date->format('M Y');

            $monthRevenue = $revenue->where('year', $year)->where('month', $month)->first();
            $values[] = $monthRevenue ? (float) $monthRevenue->total : 0;
        }

        return [
            'labels' => $labels,
            'values' => $values,
        ];
    }

    /**
     * Get revenue by payment method.
     */
    private function getRevenueByPaymentMethod(Carbon $startDate, Carbon $endDate): array
    {
        return Order::selectRaw('payment_method, SUM(total_amount) as total')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->groupBy('payment_method')
            ->get()
            ->map(function ($item) {
                return [
                    'method' => ucfirst($item->payment_method ?? 'Unknown'),
                    'total' => (float) $item->total,
                ];
            })
            ->toArray();
    }
}
