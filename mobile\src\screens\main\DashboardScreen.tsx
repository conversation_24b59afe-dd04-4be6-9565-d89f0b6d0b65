import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { apiService } from '../../services/api';
import { theme } from '../../theme/theme';

interface DashboardStats {
  orders: {
    total: number;
    pending: number;
    processing: number;
    delivered: number;
    total_revenue: number;
    orders_today: number;
    revenue_today: number;
  };
  products: {
    total: number;
    published: number;
    out_of_stock: number;
    low_stock: number;
  };
  users?: {
    total: number;
    active: number;
    new_today: number;
  };
}

interface QuickAction {
  id: string;
  title: string;
  icon: string;
  color: string;
  route: string;
  adminOnly?: boolean;
}

const { width } = Dimensions.get('window');

export const DashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user, logout, isAdmin } = useAuth();
  
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const quickActions: QuickAction[] = [
    {
      id: 'products',
      title: 'Products',
      icon: 'cube-outline',
      color: '#667eea',
      route: 'Products',
    },
    {
      id: 'orders',
      title: 'Orders',
      icon: 'receipt-outline',
      color: '#764ba2',
      route: 'Orders',
    },
    {
      id: 'customers',
      title: 'Customers',
      icon: 'people-outline',
      color: '#f093fb',
      route: 'Customers',
      adminOnly: true,
    },
    {
      id: 'analytics',
      title: 'Analytics',
      icon: 'analytics-outline',
      color: '#4facfe',
      route: 'Analytics',
      adminOnly: true,
    },
    {
      id: 'inventory',
      title: 'Inventory',
      icon: 'library-outline',
      color: '#43e97b',
      route: 'Inventory',
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings-outline',
      color: '#fa709a',
      route: 'Settings',
    },
  ];

  const loadDashboardData = useCallback(async () => {
    try {
      const promises = [
        apiService.get('/orders/statistics'),
        apiService.get('/products/statistics'),
      ];

      // Add user statistics for admin users
      if (isAdmin()) {
        promises.push(apiService.get('/users/statistics'));
      }

      const responses = await Promise.all(promises);
      
      const dashboardStats: DashboardStats = {
        orders: responses[0].data,
        products: responses[1].data,
      };

      if (isAdmin() && responses[2]) {
        dashboardStats.users = responses[2].data;
      }

      setStats(dashboardStats);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert(
        'Error',
        'Failed to load dashboard data. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [isAdmin]);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadDashboardData();
  }, [loadDashboardData]);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              console.error('Logout error:', error);
            }
          },
        },
      ]
    );
  };

  const navigateToAction = (route: string) => {
    navigation.navigate(route as never);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const filteredQuickActions = quickActions.filter(action => 
    !action.adminOnly || isAdmin()
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>{getGreeting()}</Text>
          <Text style={styles.userName}>{user?.name}</Text>
        </View>
        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <Ionicons name="log-out-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Statistics Cards */}
        {stats && (
          <View style={styles.statsContainer}>
            {/* Orders Stats */}
            <View style={[styles.statCard, styles.ordersCard]}>
              <View style={styles.statHeader}>
                <Ionicons name="receipt" size={24} color="white" />
                <Text style={styles.statTitle}>Orders</Text>
              </View>
              <Text style={styles.statNumber}>{stats.orders.total}</Text>
              <View style={styles.statDetails}>
                <Text style={styles.statDetail}>
                  {stats.orders.pending} Pending
                </Text>
                <Text style={styles.statDetail}>
                  {stats.orders.orders_today} Today
                </Text>
              </View>
            </View>

            {/* Revenue Stats */}
            <View style={[styles.statCard, styles.revenueCard]}>
              <View style={styles.statHeader}>
                <Ionicons name="cash" size={24} color="white" />
                <Text style={styles.statTitle}>Revenue</Text>
              </View>
              <Text style={styles.statNumber}>
                {formatCurrency(stats.orders.total_revenue)}
              </Text>
              <View style={styles.statDetails}>
                <Text style={styles.statDetail}>
                  {formatCurrency(stats.orders.revenue_today)} Today
                </Text>
                <Text style={styles.statDetail}>
                  {stats.orders.delivered} Delivered
                </Text>
              </View>
            </View>

            {/* Products Stats */}
            <View style={[styles.statCard, styles.productsCard]}>
              <View style={styles.statHeader}>
                <Ionicons name="cube" size={24} color="white" />
                <Text style={styles.statTitle}>Products</Text>
              </View>
              <Text style={styles.statNumber}>{stats.products.total}</Text>
              <View style={styles.statDetails}>
                <Text style={styles.statDetail}>
                  {stats.products.published} Published
                </Text>
                {stats.products.low_stock > 0 && (
                  <Text style={[styles.statDetail, styles.warningText]}>
                    {stats.products.low_stock} Low Stock
                  </Text>
                )}
              </View>
            </View>

            {/* Users Stats (Admin only) */}
            {stats.users && (
              <View style={[styles.statCard, styles.usersCard]}>
                <View style={styles.statHeader}>
                  <Ionicons name="people" size={24} color="white" />
                  <Text style={styles.statTitle}>Users</Text>
                </View>
                <Text style={styles.statNumber}>{stats.users.total}</Text>
                <View style={styles.statDetails}>
                  <Text style={styles.statDetail}>
                    {stats.users.active} Active
                  </Text>
                  <Text style={styles.statDetail}>
                    {stats.users.new_today} New Today
                  </Text>
                </View>
              </View>
            )}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {filteredQuickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={[styles.quickActionCard, { backgroundColor: action.color }]}
                onPress={() => navigateToAction(action.route)}
              >
                <Ionicons name={action.icon as any} size={32} color="white" />
                <Text style={styles.quickActionTitle}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Activity Placeholder */}
        <View style={styles.recentActivityContainer}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.activityPlaceholder}>
            <Ionicons name="time-outline" size={48} color={theme.colors.gray} />
            <Text style={styles.placeholderText}>
              Activity tracking will be available in the next update
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 2,
  },
  logoutButton: {
    padding: theme.spacing.sm,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
  },
  statsContainer: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  statCard: {
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
  },
  ordersCard: {
    backgroundColor: '#667eea',
  },
  revenueCard: {
    backgroundColor: '#764ba2',
  },
  productsCard: {
    backgroundColor: '#f093fb',
  },
  usersCard: {
    backgroundColor: '#4facfe',
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  statTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: theme.spacing.sm,
  },
  statNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: theme.spacing.sm,
  },
  statDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statDetail: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  warningText: {
    color: '#ffeb3b',
    fontWeight: '600',
  },
  quickActionsContainer: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: (width - theme.spacing.lg * 2 - theme.spacing.md) / 2,
    aspectRatio: 1,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.md,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  recentActivityContainer: {
    marginBottom: theme.spacing.xl,
  },
  activityPlaceholder: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderStyle: 'dashed',
  },
  placeholderText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
});
