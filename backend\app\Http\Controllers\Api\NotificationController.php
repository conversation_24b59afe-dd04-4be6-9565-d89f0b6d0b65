<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Notifications\DatabaseNotification;

class NotificationController extends Controller
{
    /**
     * Display a listing of user notifications.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Auth::user()->notifications();

        // Filter by read status
        if ($request->has('unread_only') && $request->boolean('unread_only')) {
            $query->whereNull('read_at');
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Sort by newest first
        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'message' => 'Notifications retrieved successfully',
            'data' => $notifications->items(),
            'meta' => [
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'per_page' => $notifications->perPage(),
                'total' => $notifications->total(),
                'unread_count' => Auth::user()->unreadNotifications()->count(),
            ]
        ]);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(string $id): JsonResponse
    {
        $notification = Auth::user()->notifications()->findOrFail($id);
        
        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read',
            'data' => $notification
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(): JsonResponse
    {
        $count = Auth::user()->unreadNotifications()->count();
        
        Auth::user()->unreadNotifications()->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => "{$count} notifications marked as read"
        ]);
    }

    /**
     * Delete notification.
     */
    public function destroy(string $id): JsonResponse
    {
        $notification = Auth::user()->notifications()->findOrFail($id);
        
        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully'
        ]);
    }

    /**
     * Delete all read notifications.
     */
    public function deleteRead(): JsonResponse
    {
        $count = Auth::user()->readNotifications()->count();
        
        Auth::user()->readNotifications()->delete();

        return response()->json([
            'success' => true,
            'message' => "{$count} read notifications deleted"
        ]);
    }

    /**
     * Get notification statistics.
     */
    public function getStatistics(): JsonResponse
    {
        $user = Auth::user();
        
        $stats = [
            'total_notifications' => $user->notifications()->count(),
            'unread_count' => $user->unreadNotifications()->count(),
            'read_count' => $user->readNotifications()->count(),
            'types' => $user->notifications()
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'recent_activity' => $user->notifications()
                ->whereDate('created_at', '>=', now()->subDays(7))
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'message' => 'Notification statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Get notification preferences.
     */
    public function getPreferences(): JsonResponse
    {
        $user = Auth::user();
        $profile = $user->profile;
        
        $preferences = $profile ? $profile->getNotificationPreferences() : [
            'email_notifications' => true,
            'push_notifications' => true,
            'order_updates' => true,
            'promotions' => false,
            'newsletter' => false,
            'review_reminders' => true,
            'price_drop_alerts' => true,
            'back_in_stock_alerts' => true,
        ];

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences retrieved successfully',
            'data' => $preferences
        ]);
    }

    /**
     * Update notification preferences.
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'order_updates' => 'boolean',
            'promotions' => 'boolean',
            'newsletter' => 'boolean',
            'review_reminders' => 'boolean',
            'price_drop_alerts' => 'boolean',
            'back_in_stock_alerts' => 'boolean',
        ]);

        $user = Auth::user();
        $profile = $user->profile ?: $user->profile()->create([]);
        
        $profile->updateNotificationPreferences($validated);

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences updated successfully',
            'data' => $profile->getNotificationPreferences()
        ]);
    }

    /**
     * Test notification.
     */
    public function testNotification(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => 'required|string|in:email,push,database',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000'
        ]);

        $user = Auth::user();

        try {
            switch ($validated['type']) {
                case 'database':
                    $user->notify(new \App\Notifications\TestNotification(
                        $validated['title'],
                        $validated['message']
                    ));
                    break;
                    
                case 'email':
                    // Send email notification
                    $user->notify(new \App\Notifications\TestEmailNotification(
                        $validated['title'],
                        $validated['message']
                    ));
                    break;
                    
                case 'push':
                    // Send push notification
                    $user->notify(new \App\Notifications\TestPushNotification(
                        $validated['title'],
                        $validated['message']
                    ));
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification templates.
     */
    public function getTemplates(): JsonResponse
    {
        $templates = [
            'order_placed' => [
                'title' => 'Order Placed',
                'description' => 'Sent when a new order is placed',
                'variables' => ['order_number', 'total_amount', 'customer_name']
            ],
            'order_shipped' => [
                'title' => 'Order Shipped',
                'description' => 'Sent when an order is shipped',
                'variables' => ['order_number', 'tracking_number', 'shipping_method']
            ],
            'order_delivered' => [
                'title' => 'Order Delivered',
                'description' => 'Sent when an order is delivered',
                'variables' => ['order_number', 'delivery_date']
            ],
            'price_drop' => [
                'title' => 'Price Drop Alert',
                'description' => 'Sent when a wishlist item price drops',
                'variables' => ['product_name', 'old_price', 'new_price', 'discount_percentage']
            ],
            'back_in_stock' => [
                'title' => 'Back in Stock',
                'description' => 'Sent when a wishlist item is back in stock',
                'variables' => ['product_name', 'stock_quantity']
            ],
            'review_reminder' => [
                'title' => 'Review Reminder',
                'description' => 'Sent to remind customers to review purchased products',
                'variables' => ['product_name', 'order_number', 'purchase_date']
            ],
            'promotion' => [
                'title' => 'Promotion',
                'description' => 'Sent for promotional campaigns',
                'variables' => ['promotion_title', 'discount_amount', 'expiry_date']
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Notification templates retrieved successfully',
            'data' => $templates
        ]);
    }

    /**
     * Bulk actions on notifications.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|string|in:mark_read,delete',
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'string|exists:notifications,id'
        ]);

        $user = Auth::user();
        $notifications = $user->notifications()->whereIn('id', $validated['notification_ids']);

        $count = $notifications->count();

        switch ($validated['action']) {
            case 'mark_read':
                $notifications->update(['read_at' => now()]);
                $message = "{$count} notifications marked as read";
                break;
                
            case 'delete':
                $notifications->delete();
                $message = "{$count} notifications deleted";
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }
}
