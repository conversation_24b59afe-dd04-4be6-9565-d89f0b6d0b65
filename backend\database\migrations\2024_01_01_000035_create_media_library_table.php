<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('media_library', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Original filename
            $table->string('file_name'); // Stored filename
            $table->string('mime_type');
            $table->string('disk')->default('public');
            $table->string('collection_name')->default('default');
            $table->unsignedBigInteger('size'); // File size in bytes
            $table->json('manipulations')->nullable();
            $table->json('custom_properties')->nullable();
            $table->json('responsive_images')->nullable();
            $table->unsignedInteger('order_column')->nullable();
            $table->string('alt_text')->nullable();
            $table->text('description')->nullable();
            $table->string('title')->nullable();
            $table->string('caption')->nullable();
            $table->enum('type', ['image', 'video', 'audio', 'document', 'other'])->default('other');
            $table->integer('width')->nullable(); // For images/videos
            $table->integer('height')->nullable(); // For images/videos
            $table->integer('duration')->nullable(); // For videos/audio in seconds
            $table->string('folder_path')->nullable(); // Folder organization
            $table->json('tags')->nullable(); // Media tags for organization
            $table->boolean('is_public')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->foreignId('uploaded_by')->constrained('users');
            $table->timestamp('last_accessed_at')->nullable();
            $table->integer('download_count')->default(0);
            $table->timestamps();
            
            // Indexes
            $table->index(['collection_name', 'type']);
            $table->index(['uploaded_by', 'created_at']);
            $table->index(['is_public', 'type']);
            $table->index(['folder_path', 'name']);
            $table->index('mime_type');
            $table->index('size');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('media_library');
    }
};
