<?php

namespace App\Http\Controllers\Api;

use App\Models\Invoice;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class InvoiceController extends BaseController
{
    /**
     * Display a listing of invoices.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $status = $request->get('status');
        $type = $request->get('type');
        $userId = $request->get('user_id');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Invoice::with(['user:id,name,email', 'order:id,order_number', 'creator:id,name'])
            ->withCount('items');

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('order', function ($orderQuery) use ($search) {
                      $orderQuery->where('order_number', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($status) {
            $query->status($status);
        }

        // Filter by type
        if ($type) {
            $query->type($type);
        }

        // Filter by user
        if ($userId) {
            $query->where('user_id', $userId);
        }

        // Filter by date range
        if ($dateFrom && $dateTo) {
            $query->dateRange($dateFrom, $dateTo);
        } elseif ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        } elseif ($dateTo) {
            $query->where('created_at', '<=', $dateTo);
        }

        // Sorting
        $allowedSortFields = ['invoice_number', 'status', 'type', 'total_amount', 'due_date', 'created_at', 'paid_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $invoices = $query->paginate($perPage);

        // Transform invoice data
        $invoices->getCollection()->transform(function ($invoice) {
            return [
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'order' => $invoice->order ? [
                    'id' => $invoice->order->id,
                    'order_number' => $invoice->order->order_number,
                ] : null,
                'user' => [
                    'id' => $invoice->user->id,
                    'name' => $invoice->user->name,
                    'email' => $invoice->user->email,
                ],
                'status' => $invoice->status,
                'status_color' => $invoice->status_color,
                'type' => $invoice->type,
                'type_color' => $invoice->type_color,
                'subtotal' => $invoice->subtotal,
                'tax_amount' => $invoice->tax_amount,
                'discount_amount' => $invoice->discount_amount,
                'total_amount' => $invoice->total_amount,
                'currency' => $invoice->currency,
                'formatted_total' => $invoice->formatted_total,
                'due_date' => $invoice->due_date,
                'paid_at' => $invoice->paid_at,
                'is_paid' => $invoice->is_paid,
                'is_overdue' => $invoice->is_overdue,
                'can_be_paid' => $invoice->can_be_paid,
                'can_be_cancelled' => $invoice->can_be_cancelled,
                'days_until_due' => $invoice->days_until_due,
                'days_overdue' => $invoice->days_overdue,
                'items_count' => $invoice->items_count,
                'creator' => $invoice->creator ? $invoice->creator->name : null,
                'created_at' => $invoice->created_at,
                'updated_at' => $invoice->updated_at,
            ];
        });

        return $this->sendPaginatedResponse($invoices);
    }

    /**
     * Store a newly created invoice.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'nullable|integer|exists:orders,id',
            'user_id' => 'required|integer|exists:users,id',
            'type' => 'required|in:invoice,credit_note,proforma',
            'subtotal' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'total_amount' => 'required|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'due_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
            'billing_address' => 'required|array',
            'shipping_address' => 'nullable|array',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.product_sku' => 'nullable|string|max:100',
            'items.*.description' => 'nullable|string|max:500',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        DB::beginTransaction();
        try {
            $invoice = Invoice::create([
                'order_id' => $request->order_id,
                'user_id' => $request->user_id,
                'status' => Invoice::STATUS_DRAFT,
                'type' => $request->type,
                'subtotal' => $request->subtotal,
                'tax_amount' => $request->tax_amount ?? 0,
                'discount_amount' => $request->discount_amount ?? 0,
                'total_amount' => $request->total_amount,
                'currency' => $request->currency ?? config('app.currency', 'USD'),
                'due_date' => $request->due_date,
                'notes' => $request->notes,
                'terms_conditions' => $request->terms_conditions,
                'billing_address' => $request->billing_address,
                'shipping_address' => $request->shipping_address,
                'created_by' => auth()->id(),
            ]);

            // Create invoice items
            foreach ($request->items as $itemData) {
                $invoice->items()->create([
                    'product_name' => $itemData['product_name'],
                    'product_sku' => $itemData['product_sku'] ?? null,
                    'description' => $itemData['description'] ?? null,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'total_price' => $itemData['quantity'] * $itemData['unit_price'],
                ]);
            }

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($invoice)
                ->log('Invoice created');

            return $this->sendCreated([
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'type' => $invoice->type,
                'status' => $invoice->status,
                'total_amount' => $invoice->total_amount,
            ], 'Invoice created successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to create invoice: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified invoice.
     */
    public function show(string $id): JsonResponse
    {
        $invoice = Invoice::with([
            'user:id,name,email,phone',
            'order.items.product:id,name,slug',
            'items',
            'creator:id,name'
        ])->find($id);

        if (!$invoice) {
            return $this->sendNotFound('Invoice not found');
        }

        return $this->sendResponse([
            'id' => $invoice->id,
            'invoice_number' => $invoice->invoice_number,
            'order' => $invoice->order ? [
                'id' => $invoice->order->id,
                'order_number' => $invoice->order->order_number,
                'status' => $invoice->order->status,
                'items_count' => $invoice->order->items->count(),
            ] : null,
            'user' => [
                'id' => $invoice->user->id,
                'name' => $invoice->user->name,
                'email' => $invoice->user->email,
                'phone' => $invoice->user->phone,
            ],
            'status' => $invoice->status,
            'status_color' => $invoice->status_color,
            'type' => $invoice->type,
            'type_color' => $invoice->type_color,
            'subtotal' => $invoice->subtotal,
            'tax_amount' => $invoice->tax_amount,
            'discount_amount' => $invoice->discount_amount,
            'total_amount' => $invoice->total_amount,
            'currency' => $invoice->currency,
            'formatted_subtotal' => $invoice->formatted_subtotal,
            'formatted_tax' => $invoice->formatted_tax,
            'formatted_discount' => $invoice->formatted_discount,
            'formatted_total' => $invoice->formatted_total,
            'due_date' => $invoice->due_date,
            'paid_at' => $invoice->paid_at,
            'notes' => $invoice->notes,
            'terms_conditions' => $invoice->terms_conditions,
            'billing_address' => $invoice->billing_address,
            'shipping_address' => $invoice->shipping_address,
            'is_paid' => $invoice->is_paid,
            'is_overdue' => $invoice->is_overdue,
            'can_be_paid' => $invoice->can_be_paid,
            'can_be_cancelled' => $invoice->can_be_cancelled,
            'days_until_due' => $invoice->days_until_due,
            'days_overdue' => $invoice->days_overdue,
            'items' => $invoice->items->map(function ($item) {
                return [
                    'id' => $item->id,
                    'product_name' => $item->product_name,
                    'product_sku' => $item->product_sku,
                    'description' => $item->description,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                    'formatted_unit_price' => $item->formatted_unit_price,
                    'formatted_total_price' => $item->formatted_total_price,
                ];
            }),
            'creator' => $invoice->creator ? [
                'id' => $invoice->creator->id,
                'name' => $invoice->creator->name,
            ] : null,
            'created_at' => $invoice->created_at,
            'updated_at' => $invoice->updated_at,
        ]);
    }

    /**
     * Update the specified invoice.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $invoice = Invoice::find($id);

        if (!$invoice) {
            return $this->sendNotFound('Invoice not found');
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:draft,sent,paid,overdue,cancelled,refunded',
            'due_date' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $invoice->update([
                'status' => $request->status,
                'due_date' => $request->due_date ?? $invoice->due_date,
                'notes' => $request->notes,
                'terms_conditions' => $request->terms_conditions,
                'paid_at' => $request->status === Invoice::STATUS_PAID ? now() : $invoice->paid_at,
            ]);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($invoice)
                ->log('Invoice updated');

            return $this->sendUpdated([
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'status' => $invoice->status,
                'total_amount' => $invoice->total_amount,
            ], 'Invoice updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to update invoice: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Remove the specified invoice.
     */
    public function destroy(string $id): JsonResponse
    {
        $invoice = Invoice::find($id);

        if (!$invoice) {
            return $this->sendNotFound('Invoice not found');
        }

        if ($invoice->is_paid) {
            return $this->sendError('Cannot delete paid invoice');
        }

        try {
            $invoice->delete();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($invoice)
                ->log('Invoice deleted');

            return $this->sendDeleted('Invoice deleted successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to delete invoice: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Create invoice from order.
     */
    public function createFromOrder(Request $request, string $orderId): JsonResponse
    {
        $order = Order::with('items.product')->find($orderId);

        if (!$order) {
            return $this->sendNotFound('Order not found');
        }

        // Check if invoice already exists for this order
        $existingInvoice = Invoice::where('order_id', $orderId)->first();
        if ($existingInvoice) {
            return $this->sendError('Invoice already exists for this order');
        }

        try {
            $invoice = Invoice::createFromOrder($order);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($invoice)
                ->withProperties(['order_id' => $orderId])
                ->log('Invoice created from order');

            return $this->sendCreated([
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'order_id' => $order->id,
                'total_amount' => $invoice->total_amount,
            ], 'Invoice created from order successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to create invoice from order: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Mark invoice as paid.
     */
    public function markAsPaid(string $id): JsonResponse
    {
        $invoice = Invoice::find($id);

        if (!$invoice) {
            return $this->sendNotFound('Invoice not found');
        }

        if (!$invoice->can_be_paid) {
            return $this->sendError('Invoice cannot be marked as paid');
        }

        try {
            $invoice->markAsPaid();

            return $this->sendResponse([
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'status' => $invoice->status,
                'paid_at' => $invoice->paid_at,
            ], 'Invoice marked as paid successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to mark invoice as paid: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Mark invoice as sent.
     */
    public function markAsSent(string $id): JsonResponse
    {
        $invoice = Invoice::find($id);

        if (!$invoice) {
            return $this->sendNotFound('Invoice not found');
        }

        try {
            $invoice->markAsSent();

            return $this->sendResponse([
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'status' => $invoice->status,
            ], 'Invoice marked as sent successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to mark invoice as sent: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Cancel invoice.
     */
    public function cancel(Request $request, string $id): JsonResponse
    {
        $invoice = Invoice::find($id);

        if (!$invoice) {
            return $this->sendNotFound('Invoice not found');
        }

        if (!$invoice->can_be_cancelled) {
            return $this->sendError('Invoice cannot be cancelled');
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $invoice->cancel($request->reason);

            return $this->sendResponse([
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'status' => $invoice->status,
            ], 'Invoice cancelled successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to cancel invoice: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Download invoice PDF.
     */
    public function downloadPdf(string $id)
    {
        $invoice = Invoice::with(['user', 'items', 'order'])->find($id);

        if (!$invoice) {
            return $this->sendNotFound('Invoice not found');
        }

        try {
            $pdf = Pdf::loadView('invoices.pdf', compact('invoice'));

            return $pdf->download("invoice-{$invoice->invoice_number}.pdf");

        } catch (\Exception $e) {
            return $this->sendError('Failed to generate PDF: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get invoice statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = Invoice::getStatistics();

            return $this->sendResponse($stats);

        } catch (\Exception $e) {
            return $this->sendError('Failed to get invoice statistics: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get overdue invoices.
     */
    public function overdue(): JsonResponse
    {
        try {
            $overdueInvoices = Invoice::overdue()
                ->with(['user:id,name,email'])
                ->orderBy('due_date', 'asc')
                ->get()
                ->map(function ($invoice) {
                    return [
                        'id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'user' => [
                            'id' => $invoice->user->id,
                            'name' => $invoice->user->name,
                            'email' => $invoice->user->email,
                        ],
                        'total_amount' => $invoice->total_amount,
                        'formatted_total' => $invoice->formatted_total,
                        'due_date' => $invoice->due_date,
                        'days_overdue' => $invoice->days_overdue,
                    ];
                });

            return $this->sendResponse($overdueInvoices);

        } catch (\Exception $e) {
            return $this->sendError('Failed to get overdue invoices: ' . $e->getMessage(), [], 500);
        }
    }
}
