import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface DashboardStats {
  total_revenue: number;
  total_orders: number;
  total_customers: number;
  total_products: number;
  revenue_growth: number;
  orders_growth: number;
  customers_growth: number;
  products_growth: number;
  average_order_value: number;
  conversion_rate: number;
  top_selling_products: TopSellingProduct[];
  recent_orders: RecentOrder[];
  revenue_by_month: RevenueByMonth[];
  orders_by_status: OrdersByStatus[];
}

export interface TopSellingProduct {
  id: number;
  name: string;
  image_url: string;
  total_sales: number;
  revenue: number;
  stock_quantity: number;
}

export interface RecentOrder {
  id: number;
  order_number: string;
  customer_name: string;
  total_amount: number;
  status: string;
  created_at: string;
}

export interface RevenueByMonth {
  month: string;
  revenue: number;
  orders: number;
}

export interface OrdersByStatus {
  status: string;
  count: number;
  percentage: number;
}

export interface SalesAnalytics {
  total_revenue: number;
  total_orders: number;
  average_order_value: number;
  revenue_by_day: ChartData[];
  revenue_by_month: ChartData[];
  revenue_by_category: ChartData[];
  top_products: TopSellingProduct[];
  sales_by_payment_method: ChartData[];
  refunds_and_returns: {
    total_refunds: number;
    total_returns: number;
    refund_rate: number;
    return_rate: number;
  };
}

export interface CustomerAnalytics {
  total_customers: number;
  new_customers: number;
  returning_customers: number;
  customer_lifetime_value: number;
  customer_acquisition_cost: number;
  churn_rate: number;
  customers_by_location: ChartData[];
  customer_segments: ChartData[];
  customer_activity: ChartData[];
}

export interface ProductAnalytics {
  total_products: number;
  published_products: number;
  out_of_stock_products: number;
  low_stock_products: number;
  top_selling_products: TopSellingProduct[];
  product_performance: ChartData[];
  category_performance: ChartData[];
  inventory_turnover: number;
  stock_value: number;
}

export interface TrafficAnalytics {
  total_visitors: number;
  unique_visitors: number;
  page_views: number;
  bounce_rate: number;
  average_session_duration: number;
  conversion_rate: number;
  traffic_sources: ChartData[];
  popular_pages: ChartData[];
  device_breakdown: ChartData[];
  geographic_data: ChartData[];
}

export interface ChartData {
  label: string;
  value: number;
  color?: string;
  percentage?: number;
}

export interface DateRange {
  start_date: string;
  end_date: string;
}

export interface AnalyticsFilters extends DateRange {
  period?: 'today' | 'yesterday' | 'last_7_days' | 'last_30_days' | 'last_90_days' | 'this_month' | 'last_month' | 'this_year' | 'last_year' | 'custom';
  compare_period?: boolean;
  category_id?: number;
  product_id?: number;
  customer_segment?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

@Injectable({
  providedIn: 'root'
})
export class AnalyticsService {
  private readonly API_URL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get dashboard overview statistics
   */
  getDashboardStats(filters: AnalyticsFilters = {}): Observable<ApiResponse<DashboardStats>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<DashboardStats>>(`${this.API_URL}/analytics/dashboard`, { params });
  }

  /**
   * Get sales analytics
   */
  getSalesAnalytics(filters: AnalyticsFilters = {}): Observable<ApiResponse<SalesAnalytics>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<SalesAnalytics>>(`${this.API_URL}/analytics/sales`, { params });
  }

  /**
   * Get customer analytics
   */
  getCustomerAnalytics(filters: AnalyticsFilters = {}): Observable<ApiResponse<CustomerAnalytics>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<CustomerAnalytics>>(`${this.API_URL}/analytics/customers`, { params });
  }

  /**
   * Get product analytics
   */
  getProductAnalytics(filters: AnalyticsFilters = {}): Observable<ApiResponse<ProductAnalytics>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<ProductAnalytics>>(`${this.API_URL}/analytics/products`, { params });
  }

  /**
   * Get traffic analytics
   */
  getTrafficAnalytics(filters: AnalyticsFilters = {}): Observable<ApiResponse<TrafficAnalytics>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<TrafficAnalytics>>(`${this.API_URL}/analytics/traffic`, { params });
  }

  /**
   * Export analytics report
   */
  exportReport(type: 'sales' | 'customers' | 'products' | 'traffic', format: 'csv' | 'excel' | 'pdf', filters: AnalyticsFilters = {}): Observable<Blob> {
    let params = new HttpParams()
      .set('type', type)
      .set('format', format);
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get(`${this.API_URL}/analytics/export`, {
      params,
      responseType: 'blob'
    });
  }

  /**
   * Get real-time analytics
   */
  getRealTimeAnalytics(): Observable<ApiResponse<any>> {
    return this.http.get<ApiResponse<any>>(`${this.API_URL}/analytics/realtime`);
  }

  /**
   * Get cohort analysis
   */
  getCohortAnalysis(filters: AnalyticsFilters = {}): Observable<ApiResponse<any>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<any>>(`${this.API_URL}/analytics/cohort`, { params });
  }

  /**
   * Get funnel analysis
   */
  getFunnelAnalysis(filters: AnalyticsFilters = {}): Observable<ApiResponse<any>> {
    let params = new HttpParams();
    
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof AnalyticsFilters];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<any>>(`${this.API_URL}/analytics/funnel`, { params });
  }

  /**
   * Get period options for filters
   */
  getPeriodOptions(): { value: string; label: string }[] {
    return [
      { value: 'today', label: 'Today' },
      { value: 'yesterday', label: 'Yesterday' },
      { value: 'last_7_days', label: 'Last 7 Days' },
      { value: 'last_30_days', label: 'Last 30 Days' },
      { value: 'last_90_days', label: 'Last 90 Days' },
      { value: 'this_month', label: 'This Month' },
      { value: 'last_month', label: 'Last Month' },
      { value: 'this_year', label: 'This Year' },
      { value: 'last_year', label: 'Last Year' },
      { value: 'custom', label: 'Custom Range' }
    ];
  }

  /**
   * Get date range for period
   */
  getDateRangeForPeriod(period: string): DateRange {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    switch (period) {
      case 'today':
        return {
          start_date: today.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        };
      
      case 'yesterday':
        return {
          start_date: yesterday.toISOString().split('T')[0],
          end_date: yesterday.toISOString().split('T')[0]
        };
      
      case 'last_7_days':
        const last7Days = new Date(today);
        last7Days.setDate(last7Days.getDate() - 7);
        return {
          start_date: last7Days.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        };
      
      case 'last_30_days':
        const last30Days = new Date(today);
        last30Days.setDate(last30Days.getDate() - 30);
        return {
          start_date: last30Days.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        };
      
      case 'last_90_days':
        const last90Days = new Date(today);
        last90Days.setDate(last90Days.getDate() - 90);
        return {
          start_date: last90Days.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        };
      
      case 'this_month':
        const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        return {
          start_date: thisMonthStart.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        };
      
      case 'last_month':
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
        return {
          start_date: lastMonthStart.toISOString().split('T')[0],
          end_date: lastMonthEnd.toISOString().split('T')[0]
        };
      
      case 'this_year':
        const thisYearStart = new Date(today.getFullYear(), 0, 1);
        return {
          start_date: thisYearStart.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        };
      
      case 'last_year':
        const lastYearStart = new Date(today.getFullYear() - 1, 0, 1);
        const lastYearEnd = new Date(today.getFullYear() - 1, 11, 31);
        return {
          start_date: lastYearStart.toISOString().split('T')[0],
          end_date: lastYearEnd.toISOString().split('T')[0]
        };
      
      default:
        return {
          start_date: last30Days.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        };
    }
  }

  /**
   * Format currency
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  /**
   * Format percentage
   */
  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  /**
   * Format number with commas
   */
  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-US').format(value);
  }
}
