<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\ProductImage;
use App\Models\ProductVariation;
use App\Models\Category;
use App\Models\User;

class ProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the super admin user to set as creator
        $superAdmin = User::where('email', '<EMAIL>')->first();

        // Get categories for products
        $categories = Category::whereNotNull('parent_id')->get(); // Only leaf categories

        if ($categories->isEmpty()) {
            $this->command->warn('No categories found. Please run CategoriesSeeder first.');
            return;
        }

        // Sample products data
        $products = [
            // Electronics - Smartphones
            [
                'name' => 'iPhone 15 Pro',
                'description' => 'The latest iPhone with advanced camera system, A17 Pro chip, and titanium design. Features include 48MP main camera, 5x telephoto zoom, and USB-C connectivity.',
                'short_description' => 'Latest iPhone with A17 Pro chip and titanium design',
                'category_name' => 'iPhone',
                'price' => 999.00,
                'sale_price' => 949.00,
                'cost_price' => 750.00,
                'stock_quantity' => 50,
                'featured' => true,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
                    'Storage' => ['128GB', '256GB', '512GB', '1TB'],
                    'Screen Size' => '6.1 inches',
                    'Operating System' => 'iOS 17'
                ],
                'meta_title' => 'iPhone 15 Pro - Latest Apple Smartphone',
                'meta_description' => 'Buy the new iPhone 15 Pro with A17 Pro chip, titanium design, and advanced camera system.',
                'weight' => 0.187,
                'length' => 14.67,
                'width' => 7.09,
                'height' => 0.83,
            ],
            [
                'name' => 'Samsung Galaxy S24 Ultra',
                'description' => 'Premium Android smartphone with S Pen, 200MP camera, and AI-powered features. Includes 6.8-inch Dynamic AMOLED display and up to 1TB storage.',
                'short_description' => 'Premium Galaxy with S Pen and 200MP camera',
                'category_name' => 'Samsung Galaxy',
                'price' => 1199.00,
                'sale_price' => null,
                'cost_price' => 850.00,
                'stock_quantity' => 30,
                'featured' => true,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Titanium Gray', 'Titanium Black', 'Titanium Violet', 'Titanium Yellow'],
                    'Storage' => ['256GB', '512GB', '1TB'],
                    'Screen Size' => '6.8 inches',
                    'Operating System' => 'Android 14'
                ],
                'weight' => 0.232,
            ],

            // Electronics - Laptops
            [
                'name' => 'MacBook Pro 16-inch M3',
                'description' => 'Professional laptop with M3 chip, 16-inch Liquid Retina XDR display, and up to 22 hours of battery life. Perfect for creative professionals and developers.',
                'short_description' => 'Professional MacBook with M3 chip and 16-inch display',
                'category_name' => 'Business Laptops',
                'price' => 2499.00,
                'sale_price' => 2299.00,
                'cost_price' => 1800.00,
                'stock_quantity' => 15,
                'featured' => true,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Space Gray', 'Silver'],
                    'Memory' => ['18GB', '36GB', '128GB'],
                    'Storage' => ['512GB SSD', '1TB SSD', '2TB SSD', '4TB SSD'],
                    'Screen Size' => '16.2 inches',
                    'Processor' => 'Apple M3'
                ],
                'weight' => 2.16,
            ],
            [
                'name' => 'ASUS ROG Strix G16',
                'description' => 'High-performance gaming laptop with Intel Core i7, NVIDIA RTX 4070, and 16GB RAM. Features 165Hz display and advanced cooling system.',
                'short_description' => 'Gaming laptop with RTX 4070 and 165Hz display',
                'category_name' => 'Gaming Laptops',
                'price' => 1599.00,
                'sale_price' => 1399.00,
                'cost_price' => 1100.00,
                'stock_quantity' => 25,
                'featured' => false,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Eclipse Gray', 'Volt Green'],
                    'Memory' => ['16GB', '32GB'],
                    'Storage' => ['512GB SSD', '1TB SSD'],
                    'Graphics' => 'NVIDIA RTX 4070',
                    'Processor' => 'Intel Core i7-13650HX'
                ],
                'weight' => 2.5,
            ],

            // Electronics - Audio
            [
                'name' => 'Sony WH-1000XM5',
                'description' => 'Industry-leading noise canceling wireless headphones with 30-hour battery life, crystal clear hands-free calling, and premium comfort.',
                'short_description' => 'Premium noise-canceling wireless headphones',
                'category_name' => 'Headphones',
                'price' => 399.99,
                'sale_price' => 349.99,
                'cost_price' => 250.00,
                'stock_quantity' => 40,
                'featured' => true,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Black', 'Silver'],
                    'Battery Life' => '30 hours',
                    'Connectivity' => 'Bluetooth 5.2',
                    'Noise Canceling' => 'Yes'
                ],
                'weight' => 0.25,
            ],
            [
                'name' => 'Apple AirPods Pro (2nd Gen)',
                'description' => 'Wireless earbuds with active noise cancellation, transparency mode, and spatial audio. Includes MagSafe charging case.',
                'short_description' => 'Wireless earbuds with active noise cancellation',
                'category_name' => 'Earbuds',
                'price' => 249.00,
                'sale_price' => null,
                'cost_price' => 180.00,
                'stock_quantity' => 60,
                'featured' => true,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['White'],
                    'Battery Life' => '6 hours (30 hours with case)',
                    'Connectivity' => 'Bluetooth 5.3',
                    'Noise Canceling' => 'Yes'
                ],
                'weight' => 0.056,
            ],

            // Clothing - Men's
            [
                'name' => 'Classic Cotton T-Shirt',
                'description' => 'Comfortable 100% cotton t-shirt perfect for everyday wear. Pre-shrunk fabric with reinforced seams for durability.',
                'short_description' => '100% cotton t-shirt for everyday comfort',
                'category_name' => 'Mens Shirts',
                'price' => 24.99,
                'sale_price' => 19.99,
                'cost_price' => 12.00,
                'stock_quantity' => 100,
                'featured' => false,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['White', 'Black', 'Navy', 'Gray', 'Red'],
                    'Size' => ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
                    'Material' => '100% Cotton',
                    'Fit' => 'Regular'
                ],
                'weight' => 0.2,
            ],
            [
                'name' => 'Slim Fit Jeans',
                'description' => 'Modern slim-fit jeans made from premium denim with stretch for comfort and mobility. Features classic five-pocket styling.',
                'short_description' => 'Slim-fit jeans with stretch comfort',
                'category_name' => 'Mens Pants',
                'price' => 79.99,
                'sale_price' => null,
                'cost_price' => 40.00,
                'stock_quantity' => 75,
                'featured' => false,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Dark Blue', 'Light Blue', 'Black', 'Gray'],
                    'Size' => ['28', '30', '32', '34', '36', '38', '40'],
                    'Material' => '98% Cotton, 2% Elastane',
                    'Fit' => 'Slim'
                ],
                'weight' => 0.6,
            ],

            // Home & Garden
            [
                'name' => 'Ergonomic Office Chair',
                'description' => 'Professional office chair with lumbar support, adjustable height, and breathable mesh back. Perfect for long work sessions.',
                'short_description' => 'Ergonomic office chair with lumbar support',
                'category_name' => 'Office Furniture',
                'price' => 299.99,
                'sale_price' => 249.99,
                'cost_price' => 150.00,
                'stock_quantity' => 20,
                'featured' => false,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Black', 'Gray', 'White'],
                    'Material' => 'Mesh and Fabric',
                    'Weight Capacity' => '300 lbs',
                    'Adjustable Height' => 'Yes'
                ],
                'weight' => 18.0,
                'length' => 66.0,
                'width' => 66.0,
                'height' => 120.0,
            ],

            // Sports & Outdoors
            [
                'name' => 'Yoga Mat Premium',
                'description' => 'High-quality yoga mat with superior grip and cushioning. Made from eco-friendly materials with alignment guides.',
                'short_description' => 'Premium yoga mat with superior grip',
                'category_name' => 'Yoga & Pilates',
                'price' => 49.99,
                'sale_price' => 39.99,
                'cost_price' => 25.00,
                'stock_quantity' => 50,
                'featured' => false,
                'status' => 'published',
                'attributes' => [
                    'Color' => ['Purple', 'Blue', 'Green', 'Pink', 'Black'],
                    'Thickness' => '6mm',
                    'Material' => 'TPE (Eco-friendly)',
                    'Size' => '183cm x 61cm'
                ],
                'weight' => 1.2,
                'length' => 183.0,
                'width' => 61.0,
                'height' => 0.6,
            ],
        ];

        foreach ($products as $productData) {
            $category = Category::where('name', $productData['category_name'])->first();
            
            if (!$category) {
                $this->command->warn("Category '{$productData['category_name']}' not found. Skipping product '{$productData['name']}'");
                continue;
            }

            $product = Product::create([
                'name' => $productData['name'],
                'description' => $productData['description'],
                'short_description' => $productData['short_description'],
                'category_id' => $category->id,
                'price' => $productData['price'],
                'sale_price' => $productData['sale_price'],
                'cost_price' => $productData['cost_price'] ?? null,
                'stock_quantity' => $productData['stock_quantity'],
                'manage_stock' => true,
                'stock_status' => $productData['stock_quantity'] > 0 ? 'in_stock' : 'out_of_stock',
                'low_stock_threshold' => 5,
                'weight' => $productData['weight'] ?? null,
                'length' => $productData['length'] ?? null,
                'width' => $productData['width'] ?? null,
                'height' => $productData['height'] ?? null,
                'status' => $productData['status'],
                'featured' => $productData['featured'],
                'meta_title' => $productData['meta_title'] ?? null,
                'meta_description' => $productData['meta_description'] ?? null,
                'attributes' => $productData['attributes'] ?? null,
                'created_by' => $superAdmin->id,
            ]);

            // Create product variations for products with multiple options
            if (isset($productData['attributes'])) {
                $this->createProductVariations($product, $productData['attributes']);
            }

            $this->command->info("Created product: {$product->name} (SKU: {$product->sku})");
        }

        $this->command->info('Products seeded successfully!');
        $this->command->info('Created ' . count($products) . ' sample products with variations');
    }

    /**
     * Create product variations based on attributes.
     */
    private function createProductVariations(Product $product, array $attributes): void
    {
        // Create variations for products with Color and Size attributes
        if (isset($attributes['Color']) && isset($attributes['Size'])) {
            $colors = $attributes['Color'];
            $sizes = $attributes['Size'];
            
            foreach ($colors as $color) {
                foreach ($sizes as $size) {
                    ProductVariation::create([
                        'product_id' => $product->id,
                        'price' => $product->price,
                        'sale_price' => $product->sale_price,
                        'stock_quantity' => rand(5, 20),
                        'manage_stock' => true,
                        'stock_status' => 'in_stock',
                        'attributes' => [
                            'Color' => $color,
                            'Size' => $size,
                        ],
                        'is_active' => true,
                    ]);
                }
            }
        }
        // Create variations for products with Color and Storage attributes
        elseif (isset($attributes['Color']) && isset($attributes['Storage'])) {
            $colors = $attributes['Color'];
            $storages = $attributes['Storage'];
            
            foreach ($colors as $color) {
                foreach ($storages as $storage) {
                    // Adjust price based on storage
                    $priceAdjustment = $this->getStoragePriceAdjustment($storage);
                    
                    ProductVariation::create([
                        'product_id' => $product->id,
                        'price' => $product->price + $priceAdjustment,
                        'sale_price' => $product->sale_price ? $product->sale_price + $priceAdjustment : null,
                        'stock_quantity' => rand(3, 15),
                        'manage_stock' => true,
                        'stock_status' => 'in_stock',
                        'attributes' => [
                            'Color' => $color,
                            'Storage' => $storage,
                        ],
                        'is_active' => true,
                    ]);
                }
            }
        }
    }

    /**
     * Get price adjustment based on storage capacity.
     */
    private function getStoragePriceAdjustment(string $storage): float
    {
        $adjustments = [
            '128GB' => 0,
            '256GB' => 100,
            '512GB' => 200,
            '1TB' => 400,
            '2TB' => 800,
            '4TB' => 1600,
        ];

        return $adjustments[$storage] ?? 0;
    }
}
