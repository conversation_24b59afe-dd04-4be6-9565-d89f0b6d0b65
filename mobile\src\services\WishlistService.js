import ApiService from './ApiService';

class WishlistService {
  /**
   * Get all user wishlists
   */
  async getWishlists() {
    try {
      const response = await ApiService.get('/wishlists');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get wishlist by ID
   */
  async getWishlist(id) {
    try {
      const response = await ApiService.get(`/wishlists/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Create new wishlist
   */
  async createWishlist(wishlistData) {
    try {
      const response = await ApiService.post('/wishlists', wishlistData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Update wishlist
   */
  async updateWishlist(id, wishlistData) {
    try {
      const response = await ApiService.put(`/wishlists/${id}`, wishlistData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Delete wishlist
   */
  async deleteWishlist(id) {
    try {
      const response = await ApiService.delete(`/wishlists/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Add item to wishlist
   */
  async addItem(wishlistId, productId, variationId = null, notes = null) {
    try {
      const response = await ApiService.post(`/wishlists/${wishlistId}/items`, {
        product_id: productId,
        product_variation_id: variationId,
        notes: notes
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Remove item from wishlist
   */
  async removeItem(wishlistId, itemId) {
    try {
      const response = await ApiService.delete(`/wishlists/${wishlistId}/items/${itemId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Move item to cart
   */
  async moveToCart(wishlistId, itemId, quantity = 1) {
    try {
      const response = await ApiService.post(`/wishlists/${wishlistId}/items/${itemId}/move-to-cart`, {
        quantity: quantity
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Move all items to cart
   */
  async moveAllToCart(wishlistId) {
    try {
      const response = await ApiService.post(`/wishlists/${wishlistId}/move-all-to-cart`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Clear all items from wishlist
   */
  async clearItems(wishlistId) {
    try {
      const response = await ApiService.delete(`/wishlists/${wishlistId}/clear`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Set wishlist as default
   */
  async setAsDefault(id) {
    try {
      const response = await ApiService.post(`/wishlists/${id}/set-default`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get default wishlist
   */
  async getDefaultWishlist() {
    try {
      const response = await ApiService.get('/wishlists/default');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Share wishlist
   */
  async shareWishlist(id) {
    try {
      const response = await ApiService.post(`/wishlists/${id}/share`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get wishlist statistics
   */
  async getStatistics() {
    try {
      const response = await ApiService.get('/wishlists/statistics');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Check if product is in any wishlist
   */
  async isProductInWishlist(productId, variationId = null) {
    try {
      const wishlists = await this.getWishlists();
      if (!wishlists.success) return false;

      for (const wishlist of wishlists.data) {
        const wishlistDetails = await this.getWishlist(wishlist.id);
        if (wishlistDetails.success) {
          const hasProduct = wishlistDetails.data.items.some(item => 
            item.product_id === productId && 
            (variationId ? item.product_variation_id === variationId : !item.product_variation_id)
          );
          if (hasProduct) return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Add product to default wishlist
   */
  async addToDefaultWishlist(productId, variationId = null, notes = null) {
    try {
      const defaultWishlist = await this.getDefaultWishlist();
      if (defaultWishlist.success) {
        return await this.addItem(defaultWishlist.data.id, productId, variationId, notes);
      } else {
        // Create default wishlist if it doesn't exist
        const newWishlist = await this.createWishlist({
          name: 'My Wishlist',
          is_default: true
        });
        if (newWishlist.success) {
          return await this.addItem(newWishlist.data.id, productId, variationId, notes);
        }
        throw new Error('Failed to create default wishlist');
      }
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Remove product from all wishlists
   */
  async removeFromAllWishlists(productId, variationId = null) {
    try {
      const wishlists = await this.getWishlists();
      if (!wishlists.success) return { success: false, message: 'Failed to get wishlists' };

      const results = [];
      for (const wishlist of wishlists.data) {
        const wishlistDetails = await this.getWishlist(wishlist.id);
        if (wishlistDetails.success) {
          const item = wishlistDetails.data.items.find(item => 
            item.product_id === productId && 
            (variationId ? item.product_variation_id === variationId : !item.product_variation_id)
          );
          if (item) {
            const result = await this.removeItem(wishlist.id, item.id);
            results.push(result);
          }
        }
      }

      return {
        success: true,
        message: `Removed from ${results.length} wishlist(s)`,
        results: results
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get product price change information
   */
  getPriceChangeInfo(item) {
    const currentPrice = item.product.current_price;
    const originalPrice = item.price_when_added;
    const priceChange = currentPrice - originalPrice;
    const priceChangePercentage = originalPrice > 0 ? 
      ((currentPrice - originalPrice) / originalPrice) * 100 : 0;

    return {
      currentPrice,
      originalPrice,
      priceChange,
      priceChangePercentage,
      isPriceDrop: priceChange < 0,
      isPriceIncrease: priceChange > 0,
      hasPriceChanged: Math.abs(priceChange) > 0.01
    };
  }

  /**
   * Format price change for display
   */
  formatPriceChange(priceChangeInfo) {
    if (!priceChangeInfo.hasPriceChanged) {
      return 'No change';
    }

    const sign = priceChangeInfo.priceChange > 0 ? '+' : '';
    const percentage = Math.abs(priceChangeInfo.priceChangePercentage).toFixed(1);
    
    return `${sign}$${Math.abs(priceChangeInfo.priceChange).toFixed(2)} (${sign}${percentage}%)`;
  }

  /**
   * Validate wishlist data
   */
  validateWishlist(wishlist) {
    const errors = [];

    if (!wishlist.name?.trim()) {
      errors.push('Wishlist name is required');
    }

    if (wishlist.name && wishlist.name.length > 255) {
      errors.push('Wishlist name cannot exceed 255 characters');
    }

    if (wishlist.description && wishlist.description.length > 1000) {
      errors.push('Description cannot exceed 1000 characters');
    }

    return errors;
  }

  /**
   * Handle API errors
   */
  handleError(error) {
    if (error.response) {
      return new Error(error.response.data.message || 'An error occurred');
    } else if (error.request) {
      return new Error('Network error. Please check your connection.');
    } else {
      return new Error('An unexpected error occurred');
    }
  }
}

export default new WishlistService();
