// Test new fields in API
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testNewFields() {
    console.log('🔍 Testing New Fields in API...\n');
    
    try {
        // Login first
        console.log('1. Logging in...');
        const loginResponse = await fetch('http://localhost:8001/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: '<EMAIL>', password: 'admin123' })
        });
        const loginData = await loginResponse.json();
        
        if (!loginData.success) {
            console.error('❌ Login failed');
            return;
        }
        
        const token = loginData.data.token;
        console.log('✅ Login successful\n');
        
        // Test dashboard stats with new fields
        console.log('2. Testing Dashboard Stats API...');
        const statsResponse = await fetch('http://localhost:8001/api/dashboard/stats', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        const statsData = await statsResponse.json();
        
        if (statsData.success) {
            console.log('✅ Dashboard Stats loaded successfully');
            console.log('📊 Sample of new fields:');
            console.log('   Users - Total:', statsData.data.users.total);
            console.log('   Users - Active:', statsData.data.users.active);
            console.log('   Users - Premium:', statsData.data.users.premium);
            console.log('   Users - Bounce Rate:', statsData.data.users.bounce_rate);
            console.log('   Sales - Growth Rate:', statsData.data.sales.growth_rate);
            console.log('   Sales - Best Selling:', statsData.data.sales.best_selling_product);
            console.log('   Inventory - Total Value:', statsData.data.inventory.total_value);
            console.log('   Marketing - ROAS:', statsData.data.marketing.roas);
            console.log('   Support - Satisfaction:', statsData.data.support.satisfaction_rating);
        }
        
        // Test new individual endpoints
        console.log('\n3. Testing New Individual Endpoints...');
        
        const endpoints = [
            { name: 'Sales Statistics', url: '/api/sales/statistics' },
            { name: 'Marketing Statistics', url: '/api/marketing/statistics' },
            { name: 'Inventory Statistics', url: '/api/inventory/statistics' },
            { name: 'Support Statistics', url: '/api/support/statistics' },
            { name: 'Detailed Users', url: '/api/users/detailed' }
        ];
        
        for (const endpoint of endpoints) {
            try {
                const response = await fetch(`http://localhost:8001${endpoint.url}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const data = await response.json();
                
                if (data.success) {
                    console.log(`✅ ${endpoint.name}: Working`);
                    if (endpoint.name === 'Detailed Users') {
                        console.log(`   - Found ${data.data.length} users with detailed info`);
                        console.log(`   - Sample user: ${data.data[0].name} (${data.data[0].email})`);
                    }
                } else {
                    console.log(`❌ ${endpoint.name}: Failed`);
                }
            } catch (error) {
                console.log(`❌ ${endpoint.name}: Error - ${error.message}`);
            }
        }
        
        console.log('\n🎉 API Testing Complete!');
        console.log('\n📋 Summary of New Fields Available:');
        console.log('   👥 Users: total, active, premium, verified, bounce_rate, countries, etc.');
        console.log('   🛍️ Products: published, draft, featured, avg_rating, total_reviews, etc.');
        console.log('   📦 Orders: shipped, cancelled, refunded, conversion_rate, repeat_customers, etc.');
        console.log('   💰 Sales: growth_rate, best_selling_product, peak_hour, etc.');
        console.log('   📊 Marketing: email_subscribers, open_rate, social_followers, roas, etc.');
        console.log('   🏪 Inventory: total_value, suppliers, warehouses, avg_stock_level, etc.');
        console.log('   🎧 Support: open_tickets, satisfaction_rating, avg_response_time, etc.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testNewFields();
