<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('media', function (Blueprint $table) {
            $table->id();
            $table->string('filename');
            $table->string('original_filename');
            $table->string('path');
            $table->string('url')->nullable();
            $table->string('mime_type');
            $table->bigInteger('size'); // File size in bytes
            $table->string('alt_text')->nullable();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->string('folder', 100)->nullable();
            $table->string('disk', 50)->default('public');
            $table->foreignId('uploaded_by')->constrained('users');
            $table->boolean('is_public')->default(true);
            $table->json('metadata')->nullable(); // Additional file metadata
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['mime_type', 'folder']);
            $table->index(['uploaded_by', 'created_at']);
            $table->index(['folder', 'is_public']);
            $table->index('filename');
            $table->index('is_public');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('media');
    }
};
