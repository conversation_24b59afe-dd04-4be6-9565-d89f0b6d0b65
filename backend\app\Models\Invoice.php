<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Invoice extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'invoice_number',
        'order_id',
        'user_id',
        'status',
        'type',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'due_date',
        'paid_at',
        'notes',
        'terms_conditions',
        'billing_address',
        'shipping_address',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'due_date' => 'date',
        'paid_at' => 'datetime',
        'billing_address' => 'array',
        'shipping_address' => 'array',
    ];

    /**
     * Invoice status constants.
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_SENT = 'sent';
    const STATUS_PAID = 'paid';
    const STATUS_OVERDUE = 'overdue';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    /**
     * Invoice type constants.
     */
    const TYPE_INVOICE = 'invoice';
    const TYPE_CREDIT_NOTE = 'credit_note';
    const TYPE_PROFORMA = 'proforma';

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['invoice_number', 'status', 'total_amount', 'paid_at'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            if (empty($invoice->invoice_number)) {
                $invoice->invoice_number = static::generateInvoiceNumber();
            }
            
            if (empty($invoice->currency)) {
                $invoice->currency = config('app.currency', 'USD');
            }

            if (empty($invoice->due_date)) {
                $invoice->due_date = now()->addDays(30); // Default 30 days
            }
        });
    }

    /**
     * Get the order associated with this invoice.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user associated with this invoice.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created this invoice.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the invoice items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Get invoice status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_DRAFT => 'secondary',
            self::STATUS_SENT => 'info',
            self::STATUS_PAID => 'success',
            self::STATUS_OVERDUE => 'danger',
            self::STATUS_CANCELLED => 'secondary',
            self::STATUS_REFUNDED => 'warning',
            default => 'secondary',
        };
    }

    /**
     * Get invoice type badge color.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            self::TYPE_INVOICE => 'primary',
            self::TYPE_CREDIT_NOTE => 'warning',
            self::TYPE_PROFORMA => 'info',
            default => 'primary',
        };
    }

    /**
     * Check if invoice is paid.
     */
    public function getIsPaidAttribute(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * Check if invoice is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->status !== self::STATUS_PAID && 
               $this->status !== self::STATUS_CANCELLED &&
               $this->due_date && 
               now()->gt($this->due_date);
    }

    /**
     * Check if invoice can be paid.
     */
    public function getCanBePaidAttribute(): bool
    {
        return in_array($this->status, [self::STATUS_SENT, self::STATUS_OVERDUE]);
    }

    /**
     * Check if invoice can be cancelled.
     */
    public function getCanBeCancelledAttribute(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_SENT]);
    }

    /**
     * Get formatted total amount.
     */
    public function getFormattedTotalAttribute(): string
    {
        return number_format($this->total_amount, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted subtotal.
     */
    public function getFormattedSubtotalAttribute(): string
    {
        return number_format($this->subtotal, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted tax amount.
     */
    public function getFormattedTaxAttribute(): string
    {
        return number_format($this->tax_amount, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted discount amount.
     */
    public function getFormattedDiscountAttribute(): string
    {
        return number_format($this->discount_amount, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get days until due.
     */
    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->due_date || $this->is_paid) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get days overdue.
     */
    public function getDaysOverdueAttribute(): ?int
    {
        if (!$this->is_overdue) {
            return null;
        }

        return now()->diffInDays($this->due_date);
    }

    /**
     * Mark invoice as paid.
     */
    public function markAsPaid(): void
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'paid_at' => now(),
        ]);

        // Log activity
        activity()
            ->causedBy(auth()->user())
            ->performedOn($this)
            ->log('Invoice marked as paid');
    }

    /**
     * Mark invoice as sent.
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
        ]);

        // Log activity
        activity()
            ->causedBy(auth()->user())
            ->performedOn($this)
            ->log('Invoice marked as sent');
    }

    /**
     * Cancel invoice.
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'notes' => $this->notes . "\n\nCancelled: " . ($reason ?? 'No reason provided'),
        ]);

        // Log activity
        activity()
            ->causedBy(auth()->user())
            ->performedOn($this)
            ->withProperties(['reason' => $reason])
            ->log('Invoice cancelled');
    }

    /**
     * Create invoice from order.
     */
    public static function createFromOrder(Order $order): Invoice
    {
        $invoice = static::create([
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'status' => self::STATUS_DRAFT,
            'type' => self::TYPE_INVOICE,
            'subtotal' => $order->subtotal,
            'tax_amount' => $order->tax_amount,
            'discount_amount' => $order->discount_amount,
            'total_amount' => $order->total_amount,
            'currency' => $order->currency,
            'billing_address' => $order->billing_address,
            'shipping_address' => $order->shipping_address,
            'created_by' => auth()->id(),
        ]);

        // Create invoice items from order items
        foreach ($order->items as $orderItem) {
            $invoice->items()->create([
                'product_name' => $orderItem->product_name,
                'product_sku' => $orderItem->product_sku,
                'description' => $orderItem->product->description ?? '',
                'quantity' => $orderItem->quantity,
                'unit_price' => $orderItem->price,
                'total_price' => $orderItem->total,
            ]);
        }

        return $invoice;
    }

    /**
     * Scope for invoices by status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for invoices by type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for paid invoices.
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }

    /**
     * Scope for unpaid invoices.
     */
    public function scopeUnpaid($query)
    {
        return $query->whereNotIn('status', [self::STATUS_PAID, self::STATUS_CANCELLED]);
    }

    /**
     * Scope for overdue invoices.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', '!=', self::STATUS_PAID)
                    ->where('status', '!=', self::STATUS_CANCELLED)
                    ->where('due_date', '<', now());
    }

    /**
     * Scope for invoices within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Generate unique invoice number.
     */
    public static function generateInvoiceNumber(): string
    {
        $year = date('Y');
        $month = date('m');
        
        // Get the last invoice number for this month
        $lastInvoice = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastInvoice) {
            // Extract sequence number from last invoice
            $lastNumber = $lastInvoice->invoice_number;
            if (preg_match('/INV-' . $year . $month . '-(\d+)/', $lastNumber, $matches)) {
                $sequence = intval($matches[1]) + 1;
            }
        }

        return sprintf('INV-%s%s-%04d', $year, $month, $sequence);
    }

    /**
     * Get invoice statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_invoices' => static::count(),
            'paid_invoices' => static::paid()->count(),
            'unpaid_invoices' => static::unpaid()->count(),
            'overdue_invoices' => static::overdue()->count(),
            'total_amount' => static::sum('total_amount'),
            'paid_amount' => static::paid()->sum('total_amount'),
            'unpaid_amount' => static::unpaid()->sum('total_amount'),
            'overdue_amount' => static::overdue()->sum('total_amount'),
            'invoices_this_month' => static::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'revenue_this_month' => static::paid()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),
        ];
    }
}
