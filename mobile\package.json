{"name": "ecommerce-mobile-app", "version": "1.0.0", "description": "E-Commerce Management System - Mobile Application", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "expo-secure-store": "~12.3.1", "expo-image-picker": "~14.3.2", "expo-camera": "~13.4.4", "expo-barcode-scanner": "~12.5.3", "expo-notifications": "~0.20.1", "expo-location": "~16.1.0", "expo-linking": "~5.0.2", "expo-web-browser": "~12.3.2", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "react-native-vector-icons": "^10.0.0", "react-native-elements": "^3.4.3", "react-native-paper": "^5.10.4", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-svg": "13.9.0", "react-native-image-crop-picker": "^0.40.2", "react-native-fast-image": "^8.6.3", "react-native-skeleton-placeholder": "^5.2.4", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-async-storage": "^1.19.3", "react-native-keychain": "^8.1.3", "react-native-netinfo": "^9.4.1", "axios": "^1.5.0", "moment": "^2.29.4", "lodash": "^4.17.21", "react-hook-form": "^7.45.4", "yup": "^1.3.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@types/lodash": "^4.14.197", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.44.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.2.1", "jest-expo": "~49.0.0", "@testing-library/react-native": "^12.2.2", "@testing-library/jest-native": "^5.4.2", "typescript": "^5.1.3"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}