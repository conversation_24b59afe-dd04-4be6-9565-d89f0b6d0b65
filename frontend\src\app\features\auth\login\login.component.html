<div class="login-container">
  <div class="login-card">
    <mat-card>
      <mat-card-header>
        <div mat-card-avatar class="login-header-image">
          <mat-icon>admin_panel_settings</mat-icon>
        </div>
        <mat-card-title>Admin <PERSON>gin</mat-card-title>
        <mat-card-subtitle>E-Commerce Management System</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <!-- Email Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Email Address</mat-label>
            <input
              matInput
              type="email"
              formControlName="email"
              placeholder="Enter your email"
              autocomplete="email"
            >
            <mat-icon matSuffix>email</mat-icon>
            <mat-error *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
              {{ getErrorMessage('email') }}
            </mat-error>
          </mat-form-field>

          <!-- Password Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Password</mat-label>
            <input
              matInput
              [type]="hidePassword ? 'password' : 'text'"
              formControlName="password"
              placeholder="Enter your password"
              autocomplete="current-password"
            >
            <button
              mat-icon-button
              matSuffix
              type="button"
              (click)="togglePasswordVisibility()"
              [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hidePassword"
            >
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
              {{ getErrorMessage('password') }}
            </mat-error>
          </mat-form-field>

          <!-- Remember Me Checkbox -->
          <div class="form-options">
            <mat-checkbox formControlName="rememberMe">
              Remember me
            </mat-checkbox>
            
            <button
              type="button"
              mat-button
              color="primary"
              class="forgot-password-link"
              (click)="navigateToForgotPassword()"
            >
              Forgot Password?
            </button>
          </div>

          <!-- Submit Button -->
          <button
            mat-raised-button
            color="primary"
            type="submit"
            class="full-width login-button"
            [disabled]="loginForm.invalid"
          >
            <mat-icon>login</mat-icon>
            Sign In
          </button>
        </form>
      </mat-card-content>

      <mat-card-actions align="center">
        <p class="register-link">
          Don't have an account?
          <button
            mat-button
            color="accent"
            (click)="navigateToRegister()"
          >
            Register here
          </button>
        </p>
      </mat-card-actions>
    </mat-card>

    <!-- Demo Credentials -->
    <mat-card class="demo-credentials">
      <mat-card-header>
        <mat-card-title>Demo Credentials</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="credential-item">
          <strong>Super Admin:</strong>
          <span><EMAIL> / admin123</span>
        </div>
        <div class="credential-item">
          <strong>Admin:</strong>
          <span><EMAIL> / password123</span>
        </div>
        <div class="credential-item">
          <strong>Editor:</strong>
          <span><EMAIL> / password123</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
