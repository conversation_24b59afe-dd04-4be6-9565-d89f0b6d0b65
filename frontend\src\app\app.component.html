<div class="app-container">
  <h1>{{title}}</h1>
  <div class="dashboard">
    <div class="stats-grid">
      <div class="stat-card">
        <h3>Total Products</h3>
        <div class="stat-number">1,234</div>
      </div>
      <div class="stat-card">
        <h3>Total Orders</h3>
        <div class="stat-number">567</div>
      </div>
      <div class="stat-card">
        <h3>Total Users</h3>
        <div class="stat-number">890</div>
      </div>
      <div class="stat-card">
        <h3>Revenue</h3>
        <div class="stat-number">$12,345</div>
      </div>
    </div>

    <div class="welcome-section">
      <h2>🎉 Your E-Commerce System is Live!</h2>
      <p>Backend API: <a href="http://localhost:8001" target="_blank">http://localhost:8001</a></p>
      <p>Frontend Dashboard: <a href="http://localhost:4200" target="_blank">http://localhost:4200</a></p>
      <button onclick="testBackend()">Test Backend Connection</button>
    </div>
  </div>
</div>

<script>
function testBackend() {
  fetch('http://localhost:8001/api/dashboard/stats')
    .then(response => response.json())
    .then(data => {
      alert('Backend connection successful! ' + JSON.stringify(data, null, 2));
    })
    .catch(error => {
      alert('Backend connection failed: ' + error.message);
    });
}
</script>
