<!-- Loading Spinner -->
<div *ngIf="isLoading$ | async" class="loading-overlay">
  <mat-spinner diameter="50"></mat-spinner>
  <p class="loading-text">Loading...</p>
</div>

<!-- Main Application -->
<div class="app-container" [class.loading]="isLoading$ | async">
  <router-outlet></router-outlet>
</div>

<!-- Toast Container -->
<div class="toast-container">
  <!-- Toastr will inject toasts here -->
</div>
