<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('design_settings', function (Blueprint $table) {
            $table->id();
            
            // Site Identity
            $table->string('site_title')->default('E-Commerce Management System');
            $table->string('site_tagline')->nullable();
            $table->string('admin_email')->nullable();
            
            // Logos
            $table->string('logo_header')->nullable();
            $table->string('logo_footer')->nullable();
            $table->string('logo_mobile')->nullable();
            $table->string('favicon')->nullable();
            
            // Typography
            $table->string('primary_font_family')->default('Roboto');
            $table->string('secondary_font_family')->default('Open Sans');
            $table->integer('base_font_size')->default(16);
            $table->string('heading_font_weight')->default('600');
            $table->string('body_font_weight')->default('400');
            
            // Colors
            $table->string('primary_color', 7)->default('#3f51b5');
            $table->string('secondary_color', 7)->default('#ff4081');
            $table->string('background_color', 7)->default('#ffffff');
            $table->string('text_color', 7)->default('#333333');
            $table->string('text_secondary_color', 7)->default('#666666');
            $table->string('button_primary_color', 7)->default('#3f51b5');
            $table->string('button_secondary_color', 7)->default('#6c757d');
            $table->string('button_success_color', 7)->default('#28a745');
            $table->string('button_danger_color', 7)->default('#dc3545');
            $table->string('button_warning_color', 7)->default('#ffc107');
            $table->string('button_info_color', 7)->default('#17a2b8');
            $table->string('link_color', 7)->default('#007bff');
            $table->string('link_hover_color', 7)->default('#0056b3');
            $table->string('border_color', 7)->default('#dee2e6');
            $table->string('success_color', 7)->default('#28a745');
            $table->string('error_color', 7)->default('#dc3545');
            $table->string('warning_color', 7)->default('#ffc107');
            $table->string('info_color', 7)->default('#17a2b8');
            
            // Contact Information
            $table->string('company_name')->nullable();
            $table->string('company_phone')->nullable();
            $table->string('company_whatsapp')->nullable();
            $table->string('company_email')->nullable();
            $table->text('company_address')->nullable();
            $table->string('company_map_url')->nullable();
            
            // Payment Settings
            $table->string('gpay_upi_id')->nullable();
            $table->string('razorpay_key_id')->nullable();
            $table->string('razorpay_key_secret')->nullable();
            $table->boolean('cod_enabled')->default(true);
            $table->decimal('min_order_amount', 10, 2)->default(0);
            
            // Social Media Links
            $table->string('facebook_url')->nullable();
            $table->string('twitter_url')->nullable();
            $table->string('instagram_url')->nullable();
            $table->string('youtube_url')->nullable();
            $table->string('linkedin_url')->nullable();
            $table->string('pinterest_url')->nullable();
            $table->string('tiktok_url')->nullable();
            $table->string('snapchat_url')->nullable();
            
            // Analytics & Tracking
            $table->string('google_analytics_id')->nullable();
            $table->string('google_tag_manager_id')->nullable();
            $table->string('facebook_pixel_id')->nullable();
            $table->text('custom_head_code')->nullable();
            $table->text('custom_body_code')->nullable();
            $table->text('custom_footer_code')->nullable();
            
            // Copyright & Legal
            $table->text('copyright_text')->default('© 2024 E-Commerce Management System. All rights reserved.');
            $table->text('terms_of_service_url')->nullable();
            $table->text('privacy_policy_url')->nullable();
            $table->text('refund_policy_url')->nullable();
            
            // Layout Settings
            $table->string('layout_type')->default('boxed'); // boxed, full-width
            $table->integer('container_max_width')->default(1200);
            $table->string('sidebar_position')->default('right'); // left, right, none
            $table->boolean('sticky_header')->default(true);
            $table->boolean('sticky_footer')->default(false);
            
            // Theme Settings
            $table->string('theme_mode')->default('light'); // light, dark, auto
            $table->boolean('enable_animations')->default(true);
            $table->string('border_radius')->default('4px');
            $table->string('box_shadow')->default('0 2px 4px rgba(0,0,0,0.1)');
            
            // Custom CSS
            $table->text('custom_css')->nullable();
            
            // Maintenance Mode
            $table->boolean('maintenance_mode')->default(false);
            $table->text('maintenance_message')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index('site_title');
            $table->index('maintenance_mode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('design_settings');
    }
};
