# E-Commerce Management System - Deployment Guide

## Overview

This guide covers the deployment of all three components of the E-Commerce Management System:
- Laravel Backend API
- Angular Admin Dashboard
- React Native Mobile Application

## Prerequisites

### Server Requirements

**Backend (Laravel)**
- PHP 8.1 or higher
- MySQL 8.0 or higher
- Redis (recommended)
- Nginx or Apache
- SSL certificate
- Minimum 2GB RAM
- 20GB storage

**Frontend (Angular)**
- Node.js 18.x or higher
- Nginx or Apache for serving static files
- SSL certificate

**Mobile App**
- Apple Developer Account (for iOS)
- Google Play Console Account (for Android)
- Expo EAS Build service

## Backend Deployment (<PERSON><PERSON>)

### 1. Server Setup

#### Install Dependencies
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-redis php8.1-gd php8.1-curl php8.1-mbstring php8.1-xml php8.1-zip
sudo apt install mysql-server redis-server nginx composer

# CentOS/RHEL
sudo yum install php81 php81-fpm php81-mysql php81-redis php81-gd php81-curl php81-mbstring php81-xml php81-zip
sudo yum install mysql-server redis nginx composer
```

#### Configure MySQL
```bash
sudo mysql_secure_installation
sudo mysql -u root -p

CREATE DATABASE ecommerce_db;
CREATE USER 'ecommerce_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON ecommerce_db.* TO 'ecommerce_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. Application Deployment

#### Clone and Setup
```bash
cd /var/www
sudo git clone <repository-url> ecommerce
sudo chown -R www-data:www-data ecommerce
cd ecommerce/backend

# Install dependencies
composer install --optimize-autoloader --no-dev

# Environment configuration
cp .env.example .env
sudo nano .env
```

#### Environment Configuration (.env)
```env
APP_NAME="E-Commerce Management System"
APP_ENV=production
APP_KEY=base64:your_generated_key
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ecommerce_db
DB_USERNAME=ecommerce_user
DB_PASSWORD=secure_password

CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

JWT_SECRET=your_jwt_secret

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
```

#### Application Setup
```bash
# Generate keys
php artisan key:generate
php artisan jwt:secret

# Run migrations
php artisan migrate --force

# Seed database
php artisan db:seed --force

# Optimize application
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 755 storage bootstrap/cache
```

### 3. Nginx Configuration

Create `/etc/nginx/sites-available/ecommerce-api`:

```nginx
server {
    listen 80;
    server_name api.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.your-domain.com;
    root /var/www/ecommerce/backend/public;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/ecommerce-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. Process Management

#### Setup Supervisor for Queue Workers
Create `/etc/supervisor/conf.d/ecommerce-worker.conf`:

```ini
[program:ecommerce-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/ecommerce/backend/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/ecommerce/backend/storage/logs/worker.log
stopwaitsecs=3600
```

Start supervisor:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start ecommerce-worker:*
```

#### Setup Cron Jobs
```bash
sudo crontab -e

# Add this line
* * * * * cd /var/www/ecommerce/backend && php artisan schedule:run >> /dev/null 2>&1
```

## Frontend Deployment (Angular)

### 1. Build Application

```bash
cd frontend
npm install
npm run build:prod
```

### 2. Nginx Configuration

Create `/etc/nginx/sites-available/ecommerce-admin`:

```nginx
server {
    listen 80;
    server_name admin.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.your-domain.com;
    root /var/www/ecommerce/frontend/dist/ecommerce-admin;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Angular routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;
}
```

### 3. Deploy Script

Create `deploy-frontend.sh`:

```bash
#!/bin/bash

# Build application
cd /var/www/ecommerce/frontend
npm install
npm run build:prod

# Copy built files
sudo rm -rf /var/www/html/admin/*
sudo cp -r dist/ecommerce-admin/* /var/www/html/admin/

# Set permissions
sudo chown -R www-data:www-data /var/www/html/admin
sudo chmod -R 755 /var/www/html/admin

echo "Frontend deployment completed!"
```

## Mobile App Deployment

### 1. Prepare for Build

Update `mobile/app.json`:

```json
{
  "expo": {
    "name": "E-Commerce Mobile",
    "slug": "ecommerce-mobile-app",
    "version": "1.0.0",
    "ios": {
      "bundleIdentifier": "com.yourcompany.ecommerce.mobile",
      "buildNumber": "1.0.0"
    },
    "android": {
      "package": "com.yourcompany.ecommerce.mobile",
      "versionCode": 1
    }
  }
}
```

### 2. Build with EAS

```bash
cd mobile

# Install EAS CLI
npm install -g @expo/eas-cli

# Login to Expo
eas login

# Configure build
eas build:configure

# Build for both platforms
eas build --platform all

# Or build individually
eas build --platform android
eas build --platform ios
```

### 3. App Store Deployment

#### iOS App Store
1. Download the `.ipa` file from EAS Build
2. Upload to App Store Connect using Xcode or Transporter
3. Fill out app information and submit for review

#### Google Play Store
1. Download the `.aab` file from EAS Build
2. Upload to Google Play Console
3. Fill out store listing and submit for review

## SSL Certificate Setup

### Using Let's Encrypt (Certbot)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Generate certificates
sudo certbot --nginx -d api.your-domain.com -d admin.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### 1. Application Monitoring

#### Setup Laravel Telescope (Development)
```bash
cd backend
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate
```

#### Setup Error Tracking (Production)
```bash
composer require sentry/sentry-laravel
php artisan sentry:publish --dsn=your-sentry-dsn
```

### 2. Server Monitoring

#### Install monitoring tools
```bash
# Install htop, iotop, and other monitoring tools
sudo apt install htop iotop nethogs

# Setup log rotation
sudo nano /etc/logrotate.d/ecommerce
```

### 3. Backup Strategy

#### Database Backup Script
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/ecommerce"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u ecommerce_user -p ecommerce_db > $BACKUP_DIR/db_backup_$DATE.sql

# Application backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /var/www/ecommerce

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

## Performance Optimization

### 1. Backend Optimization

```bash
# Enable OPcache
sudo nano /etc/php/8.1/fpm/php.ini

# Add/modify:
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### 2. Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
```

### 3. Redis Configuration

```bash
sudo nano /etc/redis/redis.conf

# Optimize for your use case
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## Security Checklist

- [ ] SSL certificates installed and configured
- [ ] Firewall configured (UFW or iptables)
- [ ] Database access restricted to localhost
- [ ] Regular security updates applied
- [ ] Strong passwords for all accounts
- [ ] File permissions properly set
- [ ] Error reporting disabled in production
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers implemented

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   sudo chown -R www-data:www-data /var/www/ecommerce
   sudo chmod -R 755 storage bootstrap/cache
   ```

2. **Queue Not Processing**
   ```bash
   sudo supervisorctl restart ecommerce-worker:*
   ```

3. **Cache Issues**
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   ```

4. **Database Connection Issues**
   - Check MySQL service status
   - Verify credentials in .env
   - Check firewall settings

For additional support, refer to the troubleshooting section in the main documentation.
