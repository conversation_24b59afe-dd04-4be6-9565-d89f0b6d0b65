<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DesignSettings;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class DesignSettingsController extends Controller
{
    /**
     * Display design settings.
     */
    public function index(): JsonResponse
    {
        $settings = DesignSettings::getInstance();

        return response()->json([
            'success' => true,
            'message' => 'Design settings retrieved successfully',
            'data' => [
                'settings' => $settings,
                'css_variables' => $settings->generateCssVariables(),
                'available_fonts' => $this->getAvailableFonts(),
                'color_presets' => $this->getColorPresets(),
            ]
        ]);
    }

    /**
     * Update design settings.
     */
    public function update(Request $request): JsonResponse
    {
        $validated = $request->validate([
            // Site Identity
            'site_title' => 'sometimes|string|max:255',
            'site_tagline' => 'nullable|string|max:255',
            'admin_email' => 'nullable|email|max:255',
            
            // Typography
            'primary_font_family' => 'sometimes|string|max:100',
            'secondary_font_family' => 'sometimes|string|max:100',
            'base_font_size' => 'sometimes|integer|min:10|max:24',
            'heading_font_weight' => 'sometimes|string|max:10',
            'body_font_weight' => 'sometimes|string|max:10',
            
            // Colors (hex validation)
            'primary_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'secondary_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'background_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'text_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'text_secondary_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'button_primary_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'button_secondary_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'button_success_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'button_danger_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'button_warning_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'button_info_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'link_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'link_hover_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'border_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'success_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'error_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'warning_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            'info_color' => 'sometimes|regex:/^#[a-f0-9]{6}$/i',
            
            // Contact Information
            'company_name' => 'nullable|string|max:255',
            'company_phone' => 'nullable|string|max:20',
            'company_whatsapp' => 'nullable|string|max:20',
            'company_email' => 'nullable|email|max:255',
            'company_address' => 'nullable|string|max:1000',
            'company_map_url' => 'nullable|url|max:500',
            
            // Payment Settings
            'gpay_upi_id' => 'nullable|string|max:100',
            'razorpay_key_id' => 'nullable|string|max:100',
            'razorpay_key_secret' => 'nullable|string|max:100',
            'cod_enabled' => 'boolean',
            'min_order_amount' => 'nullable|numeric|min:0',
            
            // Social Media
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'linkedin_url' => 'nullable|url|max:255',
            'pinterest_url' => 'nullable|url|max:255',
            'tiktok_url' => 'nullable|url|max:255',
            'snapchat_url' => 'nullable|url|max:255',
            
            // Analytics
            'google_analytics_id' => 'nullable|string|max:50',
            'google_tag_manager_id' => 'nullable|string|max:50',
            'facebook_pixel_id' => 'nullable|string|max:50',
            'custom_head_code' => 'nullable|string|max:5000',
            'custom_body_code' => 'nullable|string|max:5000',
            'custom_footer_code' => 'nullable|string|max:5000',
            
            // Copyright & Legal
            'copyright_text' => 'nullable|string|max:500',
            'terms_of_service_url' => 'nullable|url|max:255',
            'privacy_policy_url' => 'nullable|url|max:255',
            'refund_policy_url' => 'nullable|url|max:255',
            
            // Layout
            'layout_type' => ['sometimes', Rule::in(['boxed', 'full-width'])],
            'container_max_width' => 'sometimes|integer|min:800|max:2000',
            'sidebar_position' => ['sometimes', Rule::in(['left', 'right', 'none'])],
            'sticky_header' => 'boolean',
            'sticky_footer' => 'boolean',
            
            // Theme
            'theme_mode' => ['sometimes', Rule::in(['light', 'dark', 'auto'])],
            'enable_animations' => 'boolean',
            'border_radius' => 'sometimes|string|max:20',
            'box_shadow' => 'sometimes|string|max:100',
            'custom_css' => 'nullable|string|max:10000',
            
            // Maintenance
            'maintenance_mode' => 'boolean',
            'maintenance_message' => 'nullable|string|max:1000',
        ]);

        $settings = DesignSettings::getInstance();
        $settings->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Design settings updated successfully',
            'data' => [
                'settings' => $settings->fresh(),
                'css_variables' => $settings->generateCssVariables(),
            ]
        ]);
    }

    /**
     * Upload logo files.
     */
    public function uploadLogo(Request $request): JsonResponse
    {
        $request->validate([
            'type' => ['required', Rule::in(['header', 'footer', 'mobile', 'favicon'])],
            'file' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048'
        ]);

        $type = $request->type;
        $file = $request->file('file');
        
        // Delete old logo if exists
        $settings = DesignSettings::getInstance();
        $oldLogoField = "logo_{$type}";
        if ($settings->$oldLogoField) {
            Storage::disk('public')->delete($settings->$oldLogoField);
        }
        
        // Store new logo
        $path = $file->store("logos/{$type}", 'public');
        
        $settings->update([$oldLogoField => $path]);

        return response()->json([
            'success' => true,
            'message' => ucfirst($type) . ' logo uploaded successfully',
            'data' => [
                'path' => $path,
                'url' => Storage::url($path),
                'field' => $oldLogoField
            ]
        ]);
    }

    /**
     * Delete logo.
     */
    public function deleteLogo(Request $request): JsonResponse
    {
        $request->validate([
            'type' => ['required', Rule::in(['header', 'footer', 'mobile', 'favicon'])]
        ]);

        $type = $request->type;
        $settings = DesignSettings::getInstance();
        $logoField = "logo_{$type}";
        
        if ($settings->$logoField) {
            Storage::disk('public')->delete($settings->$logoField);
            $settings->update([$logoField => null]);
        }

        return response()->json([
            'success' => true,
            'message' => ucfirst($type) . ' logo deleted successfully'
        ]);
    }

    /**
     * Get available fonts.
     */
    private function getAvailableFonts(): array
    {
        return [
            'Roboto' => 'Roboto',
            'Open Sans' => 'Open Sans',
            'Lato' => 'Lato',
            'Montserrat' => 'Montserrat',
            'Source Sans Pro' => 'Source Sans Pro',
            'Raleway' => 'Raleway',
            'Ubuntu' => 'Ubuntu',
            'Nunito' => 'Nunito',
            'Poppins' => 'Poppins',
            'Inter' => 'Inter',
            'Playfair Display' => 'Playfair Display',
            'Merriweather' => 'Merriweather',
            'Georgia' => 'Georgia',
            'Times New Roman' => 'Times New Roman',
            'Arial' => 'Arial',
            'Helvetica' => 'Helvetica',
        ];
    }

    /**
     * Get color presets.
     */
    private function getColorPresets(): array
    {
        return [
            'default' => [
                'name' => 'Default Blue',
                'primary' => '#3f51b5',
                'secondary' => '#ff4081',
                'success' => '#4caf50',
                'error' => '#f44336',
                'warning' => '#ff9800',
                'info' => '#2196f3',
            ],
            'green' => [
                'name' => 'Nature Green',
                'primary' => '#4caf50',
                'secondary' => '#8bc34a',
                'success' => '#66bb6a',
                'error' => '#f44336',
                'warning' => '#ff9800',
                'info' => '#2196f3',
            ],
            'purple' => [
                'name' => 'Royal Purple',
                'primary' => '#9c27b0',
                'secondary' => '#e91e63',
                'success' => '#4caf50',
                'error' => '#f44336',
                'warning' => '#ff9800',
                'info' => '#2196f3',
            ],
            'orange' => [
                'name' => 'Sunset Orange',
                'primary' => '#ff5722',
                'secondary' => '#ff9800',
                'success' => '#4caf50',
                'error' => '#f44336',
                'warning' => '#ffc107',
                'info' => '#2196f3',
            ],
            'dark' => [
                'name' => 'Dark Theme',
                'primary' => '#212121',
                'secondary' => '#424242',
                'success' => '#4caf50',
                'error' => '#f44336',
                'warning' => '#ff9800',
                'info' => '#2196f3',
            ],
        ];
    }

    /**
     * Apply color preset.
     */
    public function applyColorPreset(Request $request): JsonResponse
    {
        $request->validate([
            'preset' => ['required', Rule::in(['default', 'green', 'purple', 'orange', 'dark'])]
        ]);

        $presets = $this->getColorPresets();
        $preset = $presets[$request->preset];
        
        $settings = DesignSettings::getInstance();
        $settings->update([
            'primary_color' => $preset['primary'],
            'secondary_color' => $preset['secondary'],
            'success_color' => $preset['success'],
            'error_color' => $preset['error'],
            'warning_color' => $preset['warning'],
            'info_color' => $preset['info'],
            'button_primary_color' => $preset['primary'],
            'button_success_color' => $preset['success'],
            'button_danger_color' => $preset['error'],
            'button_warning_color' => $preset['warning'],
            'button_info_color' => $preset['info'],
            'link_color' => $preset['primary'],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Color preset applied successfully',
            'data' => [
                'preset' => $preset,
                'settings' => $settings->fresh(),
            ]
        ]);
    }

    /**
     * Reset to default settings.
     */
    public function resetToDefaults(): JsonResponse
    {
        $settings = DesignSettings::getInstance();
        
        // Delete uploaded logos
        if ($settings->logo_header) Storage::disk('public')->delete($settings->logo_header);
        if ($settings->logo_footer) Storage::disk('public')->delete($settings->logo_footer);
        if ($settings->logo_mobile) Storage::disk('public')->delete($settings->logo_mobile);
        if ($settings->favicon) Storage::disk('public')->delete($settings->favicon);
        
        // Reset to defaults
        $settings->delete();
        $newSettings = DesignSettings::getInstance();

        return response()->json([
            'success' => true,
            'message' => 'Settings reset to defaults successfully',
            'data' => $newSettings
        ]);
    }
}
