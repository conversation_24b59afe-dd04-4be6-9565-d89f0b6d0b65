<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Wishlist extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'is_public',
        'is_default'
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the wishlist.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the wishlist items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(WishlistItem::class);
    }

    /**
     * Get the products in this wishlist.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'wishlist_items')
            ->withPivot(['product_variation_id', 'notes', 'price_when_added'])
            ->withTimestamps();
    }

    /**
     * Get the total items count.
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items()->count();
    }

    /**
     * Get the total value of items in wishlist.
     */
    public function getTotalValueAttribute(): float
    {
        return $this->items()->with('product')->get()->sum(function ($item) {
            return $item->product->current_price;
        });
    }

    /**
     * Check if product is in wishlist.
     */
    public function hasProduct(int $productId, int $variationId = null): bool
    {
        return $this->items()
            ->where('product_id', $productId)
            ->when($variationId, function ($query) use ($variationId) {
                $query->where('product_variation_id', $variationId);
            })
            ->exists();
    }

    /**
     * Add product to wishlist.
     */
    public function addProduct(int $productId, int $variationId = null, string $notes = null): WishlistItem
    {
        $product = Product::findOrFail($productId);
        
        return $this->items()->create([
            'product_id' => $productId,
            'product_variation_id' => $variationId,
            'notes' => $notes,
            'price_when_added' => $product->current_price,
        ]);
    }

    /**
     * Remove product from wishlist.
     */
    public function removeProduct(int $productId, int $variationId = null): bool
    {
        return $this->items()
            ->where('product_id', $productId)
            ->when($variationId, function ($query) use ($variationId) {
                $query->where('product_variation_id', $variationId);
            })
            ->delete() > 0;
    }

    /**
     * Set as default wishlist.
     */
    public function setAsDefault(): void
    {
        // Remove default from other wishlists
        static::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // Set this wishlist as default
        $this->update(['is_default' => true]);
    }

    /**
     * Scope for public wishlists.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for default wishlist.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get items with price changes.
     */
    public function getItemsWithPriceChanges()
    {
        return $this->items()->with('product')->get()->map(function ($item) {
            $currentPrice = $item->product->current_price;
            $originalPrice = $item->price_when_added;
            
            return [
                'item' => $item,
                'price_change' => $currentPrice - $originalPrice,
                'price_change_percentage' => $originalPrice > 0 ? 
                    (($currentPrice - $originalPrice) / $originalPrice) * 100 : 0,
                'is_price_drop' => $currentPrice < $originalPrice,
            ];
        });
    }

    /**
     * Clear all items from wishlist.
     */
    public function clearItems(): void
    {
        $this->items()->delete();
    }

    /**
     * Move all items to cart.
     */
    public function moveAllToCart(): array
    {
        $results = [];
        
        foreach ($this->items as $item) {
            $results[] = $item->moveToCart();
        }
        
        return $results;
    }
}
