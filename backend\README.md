# E-Commerce Management System - Backend API

Laravel-based REST API for the comprehensive e-commerce management system.

## Features

- **User Management**: Admin users, role-based access control, user groups
- **Product Catalog**: Hierarchical categories, products with variations, SEO
- **Order Management**: Order processing, admin-customer communication
- **Coupon System**: Flexible discount system with targeting and exclusions
- **Transaction Management**: Payment tracking, invoice generation
- **Media Management**: File upload with drag-drop support
- **Analytics Dashboard**: User behavior and product analytics
- **Design Configuration**: Theme customization and settings

## Technology Stack

- **Framework**: Laravel 10.x
- **Authentication**: JWT (tymon/jwt-auth)
- **Authorization**: <PERSON><PERSON> Permission
- **Database**: MySQL 8.0+
- **Cache**: Redis
- **File Storage**: Laravel Storage with Media Library
- **Activity Logging**: Spatie Activity Log

## Installation

### Prerequisites

- PHP 8.1 or higher
- Composer
- MySQL 8.0 or higher
- Redis (optional, for caching)

### Setup Steps

1. **Install Dependencies**
   ```bash
   composer install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Update the following in your `.env` file:
   ```env
   DB_DATABASE=ecommerce_db
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   
   JWT_SECRET=your_jwt_secret
   
   REDIS_HOST=127.0.0.1
   REDIS_PORT=6379
   ```

3. **Generate Application Key**
   ```bash
   php artisan key:generate
   ```

4. **Generate JWT Secret**
   ```bash
   php artisan jwt:secret
   ```

5. **Run Migrations and Seeders**
   ```bash
   php artisan migrate --seed
   ```

6. **Create Storage Link**
   ```bash
   php artisan storage:link
   ```

7. **Start Development Server**
   ```bash
   php artisan serve
   ```

## API Documentation

### Base URL
```
http://localhost:8000/api
```

### Authentication

All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer {your_jwt_token}
```

### Endpoints Overview

#### Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh token
- `GET /auth/me` - Get current user

#### User Management
- `GET /users` - List users
- `POST /users` - Create user
- `GET /users/{id}` - Get user details
- `PUT /users/{id}` - Update user
- `DELETE /users/{id}` - Delete user
- `POST /users/{id}/assign-role` - Assign role to user
- `POST /users/{id}/assign-group` - Assign user to group

#### Product Management
- `GET /products` - List products
- `POST /products` - Create product
- `GET /products/{id}` - Get product details
- `PUT /products/{id}` - Update product
- `DELETE /products/{id}` - Delete product
- `POST /products/{id}/duplicate` - Duplicate product
- `POST /products/bulk-action` - Bulk operations

#### Category Management
- `GET /categories` - List categories
- `POST /categories` - Create category
- `GET /categories/{id}` - Get category details
- `PUT /categories/{id}` - Update category
- `DELETE /categories/{id}` - Delete category

#### Order Management
- `GET /orders` - List orders
- `POST /orders` - Create order
- `GET /orders/{id}` - Get order details
- `PUT /orders/{id}` - Update order
- `POST /orders/{id}/status` - Update order status

#### Media Management
- `POST /media/upload` - Upload files
- `GET /media` - List media files
- `DELETE /media/{id}` - Delete media file

#### Settings
- `GET /settings` - Get all settings
- `POST /settings` - Update settings
- `GET /settings/{group}` - Get settings group

#### Analytics
- `GET /analytics/dashboard` - Dashboard analytics
- `GET /analytics/users` - User analytics
- `GET /analytics/products` - Product analytics
- `GET /analytics/orders` - Order analytics

## Database Schema

### Core Tables

- `users` - User accounts and profiles
- `user_groups` - User grouping system
- `user_group_members` - User-group relationships
- `roles` - User roles (from Spatie Permission)
- `permissions` - User permissions (from Spatie Permission)
- `products` - Product catalog
- `categories` - Product categories
- `orders` - Customer orders
- `order_items` - Order line items
- `coupons` - Discount coupons
- `transactions` - Payment transactions
- `invoices` - Invoice management
- `media` - File storage
- `settings` - Application settings
- `activity_log` - User activity tracking

## Security Features

- JWT Authentication with refresh tokens
- Role-based access control
- Input validation and sanitization
- CORS protection
- Rate limiting
- SQL injection prevention
- XSS protection
- CSRF protection

## Performance Optimization

- Database query optimization with indexes
- Redis caching for frequently accessed data
- Eager loading to prevent N+1 queries
- API response caching
- Image optimization for media files

## Testing

Run the test suite:
```bash
php artisan test
```

Run specific test types:
```bash
# Unit tests
php artisan test --testsuite=Unit

# Feature tests
php artisan test --testsuite=Feature
```

## Deployment

### Production Setup

1. **Environment Configuration**
   ```env
   APP_ENV=production
   APP_DEBUG=false
   ```

2. **Optimize Application**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   composer install --optimize-autoloader --no-dev
   ```

3. **Set Permissions**
   ```bash
   chmod -R 755 storage bootstrap/cache
   ```

### Docker Deployment

Use the provided `docker-compose.yml` in the root directory:
```bash
docker-compose up -d
```

## Contributing

1. Follow PSR-12 coding standards
2. Write tests for new features
3. Update documentation
4. Use conventional commit messages

## License

Proprietary - All rights reserved

## Credits

Web Design and developed By RekTech
