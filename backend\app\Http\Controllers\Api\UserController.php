<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class UserController extends BaseController
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $search = $request->get('search');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $filters = $request->get('filters', []);

        $query = User::with(['roles', 'groups']);

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Apply filters
        if (isset($filters['role'])) {
            $query->role($filters['role']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['group_id'])) {
            $query->inGroup($filters['group_id']);
        }

        if (isset($filters['created_from'])) {
            $query->where('created_at', '>=', $filters['created_from']);
        }

        if (isset($filters['created_to'])) {
            $query->where('created_at', '<=', $filters['created_to']);
        }

        // Sorting
        $query->orderBy($sortBy, $sortOrder);

        $users = $query->paginate($perPage);

        // Transform user data
        $users->getCollection()->transform(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'address' => $user->address,
                'discount_percentage' => $user->discount_percentage,
                'is_active' => $user->is_active,
                'avatar' => $user->avatar_url,
                'roles' => $user->getRoleNames(),
                'groups' => $user->groups->pluck('name'),
                'total_orders' => $user->total_orders,
                'total_spent' => $user->total_spent,
                'last_login_at' => $user->last_login_at,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
            ];
        });

        return $this->sendPaginatedResponse($users);
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'roles' => 'array',
            'roles.*' => 'string|exists:roles,name',
            'groups' => 'array',
            'groups.*' => 'integer|exists:user_groups,id',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        DB::beginTransaction();
        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'address' => $request->address,
                'discount_percentage' => $request->discount_percentage ?? 0,
                'is_active' => $request->is_active ?? true,
            ]);

            // Assign roles
            if ($request->has('roles')) {
                $user->assignRole($request->roles);
            } else {
                $user->assignRole('customer'); // Default role
            }

            // Assign to groups
            if ($request->has('groups')) {
                foreach ($request->groups as $groupId) {
                    $group = UserGroup::find($groupId);
                    if ($group) {
                        $group->addUser($user);
                    }
                }
            }

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($user)
                ->log('User created');

            return $this->sendCreated([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'roles' => $user->getRoleNames(),
                'groups' => $user->groups->pluck('name'),
                'is_active' => $user->is_active,
            ], 'User created successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to create user: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Display the specified user.
     */
    public function show(string $id): JsonResponse
    {
        $user = User::with(['roles', 'groups', 'orders', 'transactions'])->find($id);

        if (!$user) {
            return $this->sendNotFound('User not found');
        }

        return $this->sendResponse([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
            'address' => $user->address,
            'discount_percentage' => $user->discount_percentage,
            'is_active' => $user->is_active,
            'avatar' => $user->avatar_url,
            'roles' => $user->getRoleNames(),
            'permissions' => $user->getAllPermissions()->pluck('name'),
            'groups' => $user->groups->map(function ($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'color' => $group->color,
                ];
            }),
            'statistics' => [
                'total_orders' => $user->total_orders,
                'total_spent' => $user->total_spent,
                'average_order_value' => $user->average_order_value,
                'last_order_date' => $user->orders()->latest()->first()?->created_at,
            ],
            'last_login_at' => $user->last_login_at,
            'last_login_ip' => $user->last_login_ip,
            'created_at' => $user->created_at,
            'updated_at' => $user->updated_at,
        ]);
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $user = User::find($id);

        if (!$user) {
            return $this->sendNotFound('User not found');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'email' => 'string|email|max:255|unique:users,email,' . $id,
            'password' => 'nullable|string|min:6',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'roles' => 'array',
            'roles.*' => 'string|exists:roles,name',
            'groups' => 'array',
            'groups.*' => 'integer|exists:user_groups,id',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        DB::beginTransaction();
        try {
            $updateData = $request->only(['name', 'email', 'phone', 'address', 'discount_percentage', 'is_active']);
            
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            // Update roles
            if ($request->has('roles')) {
                $user->syncRoles($request->roles);
            }

            // Update groups
            if ($request->has('groups')) {
                // Remove from all current groups
                foreach ($user->groups as $group) {
                    $group->removeUser($user);
                }
                
                // Add to new groups
                foreach ($request->groups as $groupId) {
                    $group = UserGroup::find($groupId);
                    if ($group) {
                        $group->addUser($user);
                    }
                }
            }

            DB::commit();

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($user)
                ->log('User updated');

            return $this->sendUpdated([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'roles' => $user->getRoleNames(),
                'groups' => $user->groups->pluck('name'),
                'is_active' => $user->is_active,
            ], 'User updated successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Failed to update user: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Remove the specified user.
     */
    public function destroy(string $id): JsonResponse
    {
        $user = User::find($id);

        if (!$user) {
            return $this->sendNotFound('User not found');
        }

        // Prevent deletion of current user
        if ($user->id === auth()->id()) {
            return $this->sendError('Cannot delete your own account');
        }

        // Prevent deletion of super admin by non-super admin
        if ($user->hasRole('super-admin') && !auth()->user()->hasRole('super-admin')) {
            return $this->sendForbidden('Cannot delete super admin user');
        }

        try {
            // Log activity before deletion
            activity()
                ->causedBy(auth()->user())
                ->performedOn($user)
                ->withProperties(['user_name' => $user->name, 'user_email' => $user->email])
                ->log('User deleted');

            $user->delete();

            return $this->sendDeleted('User deleted successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to delete user: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Assign role to user.
     */
    public function assignRole(Request $request, string $id): JsonResponse
    {
        $user = User::find($id);

        if (!$user) {
            return $this->sendNotFound('User not found');
        }

        $validator = Validator::make($request->all(), [
            'role' => 'required|string|exists:roles,name',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $user->assignRole($request->role);

            // Log activity
            activity()
                ->causedBy(auth()->user())
                ->performedOn($user)
                ->withProperties(['role' => $request->role])
                ->log('Role assigned to user');

            return $this->sendResponse([
                'user_id' => $user->id,
                'roles' => $user->getRoleNames(),
            ], 'Role assigned successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to assign role: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Assign user to group.
     */
    public function assignGroup(Request $request, string $id): JsonResponse
    {
        $user = User::find($id);

        if (!$user) {
            return $this->sendNotFound('User not found');
        }

        $validator = Validator::make($request->all(), [
            'group_id' => 'required|integer|exists:user_groups,id',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $group = UserGroup::find($request->group_id);
            $group->addUser($user);

            return $this->sendResponse([
                'user_id' => $user->id,
                'groups' => $user->groups->pluck('name'),
            ], 'User assigned to group successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to assign user to group: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Get user groups.
     */
    public function groups(Request $request): JsonResponse
    {
        $groups = UserGroup::active()
            ->withCount('users')
            ->orderBy('name')
            ->get();

        return $this->sendResponse($groups->map(function ($group) {
            return [
                'id' => $group->id,
                'name' => $group->name,
                'description' => $group->description,
                'color' => $group->color,
                'users_count' => $group->users_count,
                'discount_percentage' => $group->discount_percentage,
                'is_active' => $group->is_active,
                'created_at' => $group->created_at,
            ];
        }));
    }

    /**
     * Create user group.
     */
    public function createGroup(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:user_groups',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $group = UserGroup::create([
                'name' => $request->name,
                'description' => $request->description,
                'color' => $request->color ?? '#3B82F6',
                'discount_percentage' => $request->discount_percentage ?? 0,
                'is_active' => $request->is_active ?? true,
                'created_by' => auth()->id(),
            ]);

            return $this->sendCreated([
                'id' => $group->id,
                'name' => $group->name,
                'description' => $group->description,
                'color' => $group->color,
                'discount_percentage' => $group->discount_percentage,
                'is_active' => $group->is_active,
            ], 'User group created successfully');

        } catch (\Exception $e) {
            return $this->sendError('Failed to create user group: ' . $e->getMessage(), [], 500);
        }
    }
}
