import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useOffline } from '../context/OfflineContext';
import { colors, spacing, typography } from '../theme';

const { width } = Dimensions.get('window');

const OfflineStatusBar = ({ style }) => {
  const { 
    isOnline, 
    pendingRequests, 
    syncInProgress, 
    syncOfflineData,
    showOfflineStatus 
  } = useOffline();

  // Don't show anything if online and no pending requests
  if (isOnline && pendingRequests === 0) {
    return null;
  }

  const getStatusInfo = () => {
    if (!isOnline) {
      return {
        text: 'You are offline',
        subText: pendingRequests > 0 ? `${pendingRequests} changes pending` : 'Some features may be limited',
        icon: 'wifi-off',
        backgroundColor: colors.warning,
        showSyncButton: false
      };
    }

    if (syncInProgress) {
      return {
        text: 'Syncing changes...',
        subText: `${pendingRequests} items remaining`,
        icon: 'sync',
        backgroundColor: colors.info,
        showSyncButton: false
      };
    }

    if (pendingRequests > 0) {
      return {
        text: 'Changes ready to sync',
        subText: `${pendingRequests} pending changes`,
        icon: 'cloud-upload',
        backgroundColor: colors.success,
        showSyncButton: true
      };
    }

    return null;
  };

  const statusInfo = getStatusInfo();

  if (!statusInfo) {
    return null;
  }

  return (
    <View style={[styles.container, { backgroundColor: statusInfo.backgroundColor }, style]}>
      <TouchableOpacity
        style={styles.content}
        onPress={showOfflineStatus}
        activeOpacity={0.8}
      >
        <View style={styles.leftContent}>
          <Icon 
            name={statusInfo.icon} 
            size={20} 
            color={colors.background.primary}
            style={[styles.icon, syncInProgress && styles.spinningIcon]}
          />
          <View style={styles.textContainer}>
            <Text style={styles.statusText}>{statusInfo.text}</Text>
            <Text style={styles.subText}>{statusInfo.subText}</Text>
          </View>
        </View>

        {statusInfo.showSyncButton && (
          <TouchableOpacity
            style={styles.syncButton}
            onPress={(e) => {
              e.stopPropagation();
              syncOfflineData();
            }}
          >
            <Icon name="sync" size={16} color={colors.background.primary} />
            <Text style={styles.syncButtonText}>Sync</Text>
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: width,
    minHeight: 60,
    justifyContent: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    marginRight: spacing.sm,
  },
  spinningIcon: {
    // Add rotation animation if needed
  },
  textContainer: {
    flex: 1,
  },
  statusText: {
    fontSize: typography.body.fontSize,
    fontWeight: '600',
    color: colors.background.primary,
  },
  subText: {
    fontSize: typography.caption.fontSize,
    color: colors.background.primary,
    opacity: 0.9,
    marginTop: 2,
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 16,
    marginLeft: spacing.sm,
  },
  syncButtonText: {
    fontSize: typography.caption.fontSize,
    fontWeight: '600',
    color: colors.background.primary,
    marginLeft: spacing.xs,
  },
});

export default OfflineStatusBar;
