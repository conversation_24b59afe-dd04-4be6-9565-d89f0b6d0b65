<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserAddress;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserAddressController extends Controller
{
    /**
     * Display a listing of user addresses.
     */
    public function index(): JsonResponse
    {
        $addresses = Auth::user()->addresses()->orderBy('is_default', 'desc')->get();

        return response()->json([
            'success' => true,
            'message' => 'Addresses retrieved successfully',
            'data' => $addresses
        ]);
    }

    /**
     * Store a newly created address.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', 'string', Rule::in(['shipping', 'billing', 'both'])],
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'company' => 'nullable|string|max:255',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:2',
            'phone' => 'nullable|string|max:20',
            'is_default' => 'boolean'
        ]);

        $validated['user_id'] = Auth::id();

        $address = UserAddress::create($validated);

        if ($validated['is_default'] ?? false) {
            $address->setAsDefault();
        }

        return response()->json([
            'success' => true,
            'message' => 'Address created successfully',
            'data' => $address->fresh()
        ], 201);
    }

    /**
     * Display the specified address.
     */
    public function show(UserAddress $address): JsonResponse
    {
        $this->authorize('view', $address);

        return response()->json([
            'success' => true,
            'message' => 'Address retrieved successfully',
            'data' => $address
        ]);
    }

    /**
     * Update the specified address.
     */
    public function update(Request $request, UserAddress $address): JsonResponse
    {
        $this->authorize('update', $address);

        $validated = $request->validate([
            'type' => ['sometimes', 'string', Rule::in(['shipping', 'billing', 'both'])],
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'company' => 'nullable|string|max:255',
            'address_line_1' => 'sometimes|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'sometimes|string|max:255',
            'state' => 'sometimes|string|max:255',
            'postal_code' => 'sometimes|string|max:20',
            'country' => 'sometimes|string|max:2',
            'phone' => 'nullable|string|max:20',
            'is_default' => 'boolean'
        ]);

        $address->update($validated);

        if ($validated['is_default'] ?? false) {
            $address->setAsDefault();
        }

        return response()->json([
            'success' => true,
            'message' => 'Address updated successfully',
            'data' => $address->fresh()
        ]);
    }

    /**
     * Remove the specified address.
     */
    public function destroy(UserAddress $address): JsonResponse
    {
        $this->authorize('delete', $address);

        $address->delete();

        return response()->json([
            'success' => true,
            'message' => 'Address deleted successfully'
        ]);
    }

    /**
     * Set address as default.
     */
    public function setDefault(UserAddress $address): JsonResponse
    {
        $this->authorize('update', $address);

        $address->setAsDefault();

        return response()->json([
            'success' => true,
            'message' => 'Address set as default successfully',
            'data' => $address->fresh()
        ]);
    }

    /**
     * Get default addresses.
     */
    public function defaults(): JsonResponse
    {
        $addresses = Auth::user()->addresses()
            ->where('is_default', true)
            ->get()
            ->groupBy('type');

        return response()->json([
            'success' => true,
            'message' => 'Default addresses retrieved successfully',
            'data' => [
                'shipping' => $addresses->get('shipping', collect())->first() ?? $addresses->get('both', collect())->first(),
                'billing' => $addresses->get('billing', collect())->first() ?? $addresses->get('both', collect())->first(),
            ]
        ]);
    }

    /**
     * Get addresses by type.
     */
    public function byType(string $type): JsonResponse
    {
        if (!in_array($type, ['shipping', 'billing', 'both'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid address type'
            ], 400);
        }

        $addresses = Auth::user()->addresses()
            ->where(function ($query) use ($type) {
                $query->where('type', $type)->orWhere('type', 'both');
            })
            ->orderBy('is_default', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Addresses retrieved successfully',
            'data' => $addresses
        ]);
    }
}
