<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;
use App\Models\User;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'manage_user_roles',
            'manage_user_groups',

            // Product Management
            'view_products',
            'create_products',
            'edit_products',
            'delete_products',
            'manage_categories',
            'bulk_edit_products',

            // Order Management
            'view_orders',
            'create_orders',
            'edit_orders',
            'delete_orders',
            'process_orders',
            'refund_orders',

            // Coupon Management
            'view_coupons',
            'create_coupons',
            'edit_coupons',
            'delete_coupons',

            // Transaction Management
            'view_transactions',
            'create_transactions',
            'edit_transactions',
            'delete_transactions',

            // Invoice Management
            'view_invoices',
            'create_invoices',
            'edit_invoices',
            'delete_invoices',
            'convert_invoices',

            // Media Management
            'view_media',
            'upload_media',
            'delete_media',
            'manage_media_library',

            // Analytics
            'view_analytics',
            'view_reports',
            'export_data',

            // Settings
            'view_settings',
            'edit_settings',
            'manage_system_settings',
            'manage_design_settings',
            'manage_payment_settings',

            // System Administration
            'access_system_logs',
            'manage_modules',
            'clear_cache',
            'backup_system',
            'manage_security',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - Full access
        $superAdmin = Role::create(['name' => 'super-admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - Most permissions except system administration
        $admin = Role::create(['name' => 'admin']);
        $adminPermissions = [
            'view_users', 'create_users', 'edit_users', 'delete_users', 'manage_user_roles', 'manage_user_groups',
            'view_products', 'create_products', 'edit_products', 'delete_products', 'manage_categories', 'bulk_edit_products',
            'view_orders', 'create_orders', 'edit_orders', 'delete_orders', 'process_orders', 'refund_orders',
            'view_coupons', 'create_coupons', 'edit_coupons', 'delete_coupons',
            'view_transactions', 'create_transactions', 'edit_transactions',
            'view_invoices', 'create_invoices', 'edit_invoices', 'convert_invoices',
            'view_media', 'upload_media', 'delete_media', 'manage_media_library',
            'view_analytics', 'view_reports', 'export_data',
            'view_settings', 'edit_settings', 'manage_design_settings', 'manage_payment_settings',
        ];
        $admin->givePermissionTo($adminPermissions);

        // Editor - Product and content management
        $editor = Role::create(['name' => 'editor']);
        $editorPermissions = [
            'view_users',
            'view_products', 'create_products', 'edit_products', 'manage_categories', 'bulk_edit_products',
            'view_orders', 'edit_orders', 'process_orders',
            'view_coupons', 'create_coupons', 'edit_coupons',
            'view_media', 'upload_media', 'delete_media', 'manage_media_library',
            'view_analytics', 'view_reports',
            'view_settings',
        ];
        $editor->givePermissionTo($editorPermissions);

        // Data Entry - Basic product management
        $dataEntry = Role::create(['name' => 'data-entry']);
        $dataEntryPermissions = [
            'view_products', 'create_products', 'edit_products',
            'view_orders',
            'view_media', 'upload_media',
        ];
        $dataEntry->givePermissionTo($dataEntryPermissions);

        // Accountant - Financial data access
        $accountant = Role::create(['name' => 'accountant']);
        $accountantPermissions = [
            'view_orders',
            'view_transactions', 'create_transactions', 'edit_transactions',
            'view_invoices', 'create_invoices', 'edit_invoices', 'convert_invoices',
            'view_analytics', 'view_reports', 'export_data',
        ];
        $accountant->givePermissionTo($accountantPermissions);

        // Customer - Basic customer permissions
        $customer = Role::create(['name' => 'customer']);
        $customerPermissions = [
            // Customers typically don't have admin panel access
            // These permissions would be used in the mobile app or customer portal
        ];
        $customer->givePermissionTo($customerPermissions);

        // Customer Service - Order and customer management
        $customerService = Role::create(['name' => 'customer-service']);
        $customerServicePermissions = [
            'view_users', 'edit_users',
            'view_orders', 'edit_orders', 'process_orders',
            'view_transactions',
            'view_invoices',
            'view_analytics',
        ];
        $customerService->givePermissionTo($customerServicePermissions);

        // Marketing - Coupon and analytics access
        $marketing = Role::create(['name' => 'marketing']);
        $marketingPermissions = [
            'view_users',
            'view_products',
            'view_orders',
            'view_coupons', 'create_coupons', 'edit_coupons', 'delete_coupons',
            'view_analytics', 'view_reports', 'export_data',
            'view_media', 'upload_media', 'manage_media_library',
        ];
        $marketing->givePermissionTo($marketingPermissions);

        // Create default super admin user
        $superAdminUser = User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin123'),
            'phone' => '+1234567890',
            'address' => '123 Admin Street, Admin City, AC 12345',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $superAdminUser->assignRole('super-admin');

        // Create sample admin user
        $adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'phone' => '+1234567891',
            'address' => '456 Admin Avenue, Admin City, AC 12346',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $adminUser->assignRole('admin');

        // Create sample editor user
        $editorUser = User::create([
            'name' => 'Editor User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'phone' => '+1234567892',
            'address' => '789 Editor Lane, Editor City, EC 12347',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $editorUser->assignRole('editor');

        // Create sample customer users
        for ($i = 1; $i <= 10; $i++) {
            $customer = User::create([
                'name' => "Customer {$i}",
                'email' => "customer{$i}@example.com",
                'password' => bcrypt('password123'),
                'phone' => '+123456789' . str_pad($i, 2, '0', STR_PAD_LEFT),
                'address' => "{$i}00 Customer Street, Customer City, CC 1234{$i}",
                'discount_percentage' => rand(0, 20),
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $customer->assignRole('customer');
        }

        $this->command->info('Roles and permissions seeded successfully!');
        $this->command->info('Default super admin: <EMAIL> / admin123');
        $this->command->info('Default admin: <EMAIL> / password123');
        $this->command->info('Default editor: <EMAIL> / password123');
    }
}
